import { useState, useRef, useEffect } from 'react';
import { ColDef } from 'ag-grid-community';
import styles from '../ChartPropertyController.module.css';
import { FormControlLabel, Switch } from '@mui/material';
import { TextField } from '@mui/material';
import { AgGridReact } from 'ag-grid-react';
import { useUpdateCustomChartsMutation } from '../../../../services/sketchbookServices';
import { toast } from 'react-hot-toast';
import { chartPayloadHandler } from '../utils/chartPayloadHandler';
import IconButton from '../../../common/button/IconButton';
import { FaPlus, FaTrash } from 'react-icons/fa';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface TimelineControlsProps {
  selectedChart: any;
  onChartUpdate: (chart: any) => void;
  sketchbookId: string;
  activePage: any;
}

export const TimelineControls = ({
  selectedChart,
  onChartUpdate,
  sketchbookId,
  activePage,
}: TimelineControlsProps) => {
  const [activeTab, setActiveTab] = useState('tasks');
  const gridRef = useRef<any>(null);
  const [updateCustomCharts] = useUpdateCustomChartsMutation();

  // Safely access nested properties
  const chartTitle = selectedChart?.title || 'Timeline';
  const taskRowData = selectedChart?.data?.datasets?.[0]?.data || [];
  const remainingRowData = selectedChart?.data?.datasets?.[1]?.data || [];

  // Initialize chart data if empty
  useEffect(() => {
    if (!selectedChart?.data?.datasets?.length) {
      const updatedChart = JSON.parse(JSON.stringify(selectedChart));
      const today = new Date().toISOString().split('T')[0];

      // Initialize tasks dataset
      updatedChart.data = updatedChart.data || {};
      updatedChart.data.datasets = [
        {
          label: 'Tasks',
          data: [{ x: today, y: 'New Task', duration: 0 }],
          borderColor: 'rgba(197, 170, 255, 1)',
          backgroundColor: 'rgba(197, 170, 255, 0.7)',
          borderWidth: 1,
          fill: false,
          isTimeline: true,
        },
        {
          label: 'Remaining Tasks',
          data: [{ x: today, y: 0, duration: 0 }],
          borderColor: 'rgba(255, 99, 132, 1)',
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderWidth: 1,
          fill: true,
          isTimeline: true,
        },
      ];

      // Set title if not set
      if (!updatedChart.title) {
        updatedChart.title = 'Timeline Chart';
      }

      onChartUpdate(updatedChart);
    }
  }, [selectedChart, onChartUpdate]);

  const updateTimelineData = async (
    datasetIndex: number,
    pointIndex: number,
    field: string,
    value: any
  ) => {
    try {
      const updatedChart = JSON.parse(JSON.stringify(selectedChart));

      // Make sure duration property exists
      if (
        !updatedChart.data.datasets[datasetIndex].data[
          pointIndex
        ].hasOwnProperty('duration')
      ) {
        updatedChart.data.datasets[datasetIndex].data[pointIndex].duration = 0;
      }

      if (field === 'task') {
        updatedChart.data.datasets[datasetIndex].data[pointIndex].y = value;
      } else if (field === 'date') {
        updatedChart.data.datasets[datasetIndex].data[pointIndex].x = value;
      } else if (field === 'remaining') {
        updatedChart.data.datasets[datasetIndex].data[pointIndex].y =
          Number(value);
      }

      // Ensure the chart has the isTimeline flag
      updatedChart.data.datasets.forEach((dataset: any) => {
        dataset.isTimeline = true;
      });

      // Update the UI first for immediate feedback
      onChartUpdate(updatedChart);

      // Then save to the backend
      const updatedChartPayload = chartPayloadHandler(
        updatedChart,
        'timeline',
        sketchbookId,
        activePage
      );

      const response = await updateCustomCharts({
        id: selectedChart.id,
        payload: updatedChartPayload,
        chartType: 'timeline',
      }).unwrap();

      if (response?.success) {
        toast.success('Timeline updated successfully');
      }
    } catch (error) {
      toast.error('Failed to update timeline');
      console.error('Error updating timeline:', error);
    }
  };

  const addNewDataPoint = async () => {
    const updatedChart = JSON.parse(JSON.stringify(selectedChart));
    const datasetIndex = activeTab === 'tasks' ? 0 : 1;

    // Make sure datasets array exists
    if (!updatedChart.data) {
      updatedChart.data = {};
    }

    if (!updatedChart.data.datasets) {
      updatedChart.data.datasets = [];
    }

    // Make sure the specific dataset exists
    if (!updatedChart.data.datasets[datasetIndex]) {
      updatedChart.data.datasets[datasetIndex] = {
        label: activeTab === 'tasks' ? 'Tasks' : 'Remaining Tasks',
        data: [],
        backgroundColor:
          activeTab === 'tasks'
            ? 'rgba(197, 170, 255, 0.7)'
            : 'rgba(255, 99, 132, 0.7)',
        borderColor:
          activeTab === 'tasks'
            ? 'rgba(197, 170, 255, 1)'
            : 'rgba(255, 99, 132, 1)',
        fill: activeTab === 'tasks' ? false : true,
        isTimeline: true,
      };
    }

    // Make sure data array exists
    if (!updatedChart.data.datasets[datasetIndex].data) {
      updatedChart.data.datasets[datasetIndex].data = [];
    }

    // Create new point with string date format
    const today = new Date().toISOString().split('T')[0];
    const newPoint = {
      x: today,
      y: activeTab === 'tasks' ? 'New Task' : 0,
      duration: 0,
    };

    updatedChart.data.datasets[datasetIndex].data.push(newPoint);
    updatedChart.data.datasets[datasetIndex].isTimeline = true;

    try {
      console.log('Adding new data point:', JSON.stringify(updatedChart));
      const updatedChartPayload = chartPayloadHandler(
        updatedChart,
        'timeline',
        sketchbookId,
        activePage
      );
      console.log(
        'New data point payload:',
        JSON.stringify(updatedChartPayload)
      );

      const response = await updateCustomCharts({
        id: selectedChart.id,
        payload: updatedChartPayload,
        chartType: 'timeline',
      }).unwrap();

      console.log('New data point response:', response);

      onChartUpdate(updatedChart);
      toast.success(
        `New ${activeTab === 'tasks' ? 'task' : 'remaining task'} added`
      );
    } catch (error) {
      toast.error('Failed to add new data point');
      console.error('Error adding data point:', error);
    }
  };

  const deleteSelectedRows = async () => {
    const gridApi = gridRef.current?.api;
    const selectedRows = gridApi.getSelectedRows();

    if (selectedRows.length === 0) {
      toast.error('Please select rows to delete');
      return;
    }

    const updatedChart = JSON.parse(JSON.stringify(selectedChart));
    const datasetIndex = activeTab === 'tasks' ? 0 : 1;
    const rowIndexes = selectedRows
      .map((row: any) => row.rowIndex)
      .sort((a: number, b: number) => b - a);

    rowIndexes.forEach((index: number) => {
      updatedChart.data.datasets[datasetIndex].data.splice(index, 1);
    });

    try {
      const updatedChartPayload = chartPayloadHandler(
        updatedChart,
        'timeline',
        sketchbookId,
        activePage
      );
      await updateCustomCharts({
        id: selectedChart.id,
        payload: updatedChartPayload,
        chartType: 'timeline',
      }).unwrap();

      onChartUpdate(updatedChart);
      toast.success('Selected items deleted');
    } catch (error) {
      toast.error('Failed to delete selected items');
      console.error('Error deleting items:', error);
    }
  };

  const taskColumnDefs: ColDef[] = [
    {
      width: 40,
      headerName: '',
      field: 'selection',
    },
    {
      field: 'x',
      headerName: 'Date',
      editable: true,
      cellEditor: 'agDateCellEditor',
      cellEditorParams: {
        browserDatePicker: true,
        parseValue: (value: string) => new Date(value),
      },
      valueGetter: (params: any) => {
        return params.data.x ? new Date(params.data.x) : null;
      },
      valueFormatter: (params: any) => {
        if (!params.value) return '';
        return params.value.toLocaleDateString();
      },
      valueSetter: (params: any) => {
        try {
          // Ensure we have a valid date
          if (!params.newValue) {
            return false;
          }

          // Format as ISO string with UTC timezone
          const dateValue = params.newValue.toISOString().split('T')[0];

          // Update the data
          params.data.x = dateValue;

          // Call the update function
          updateTimelineData(
            params.data.datasetIndex,
            params.node.rowIndex,
            'date',
            dateValue
          );
          return true;
        } catch (error) {
          console.error('Error setting date value:', error);
          toast.error('Invalid date format');
          return false;
        }
      },
    },
    {
      field: 'y',
      headerName: 'Task',
      editable: true,
      valueSetter: (params: any) => {
        const updatedData = { ...params.data };
        updatedData.y = params.newValue;
        updateTimelineData(
          params.data.datasetIndex,
          params.node.rowIndex,
          'task',
          params.newValue
        );
        return true;
      },
    },
  ];

  const remainingColumnDefs: ColDef[] = [
    {
      width: 40,
      headerName: '',
      field: 'selection',
    },
    {
      field: 'x',
      headerName: 'Date',
      editable: true,
      cellEditor: 'agDateCellEditor',
      cellEditorParams: {
        browserDatePicker: true,
        parseValue: (value: string) => new Date(value),
      },
      valueGetter: (params: any) => {
        return params.data.x ? new Date(params.data.x) : null;
      },
      valueFormatter: (params: any) => {
        if (!params.value) return '';
        return params.value.toLocaleDateString();
      },
      valueSetter: (params: any) => {
        try {
          // Ensure we have a valid date
          if (!params.newValue) {
            return false;
          }

          // Format as ISO string with UTC timezone
          const dateValue = params.newValue.toISOString().split('T')[0];

          // Update the data
          params.data.x = dateValue;

          // Call the update function
          updateTimelineData(
            params.data.datasetIndex,
            params.node.rowIndex,
            'date',
            dateValue
          );
          return true;
        } catch (error) {
          console.error('Error setting date value:', error);
          toast.error('Invalid date format');
          return false;
        }
      },
    },
    {
      field: 'y',
      headerName: 'Remaining',
      editable: true,
      valueParser: (params: any) => Number(params.newValue),
      valueFormatter: (params: any) => params.value?.toString() || '0',
      valueSetter: (params: any) => {
        const updatedData = { ...params.data };
        updatedData.y = Number(params.newValue);
        updateTimelineData(
          params.data.datasetIndex,
          params.node.rowIndex,
          'remaining',
          Number(params.newValue)
        );
        return true;
      },
    },
  ];

  const getRowData = () => {
    if (activeTab === 'tasks') {
      return taskRowData.map((data: any, index: number) => ({
        ...data,
        datasetIndex: 0,
        rowIndex: index,
      }));
    } else {
      return remainingRowData.map((data: any, index: number) => ({
        ...data,
        datasetIndex: 1,
        rowIndex: index,
      }));
    }
  };

  // No custom components needed as we're using AG Grid's built-in date picker

  return (
    <div className={styles.timelineControls}>
      <div className={styles.section}>
        <div className={styles.heading}>{'Chart Properties'}</div>

        {/* Chart Title Controls */}
        <div className={styles.controlGroup}>
          <TextField
            fullWidth
            label="Chart Title"
            value={chartTitle}
            onChange={(e: any) => {
              const updatedChart = JSON.parse(JSON.stringify(selectedChart));
              updatedChart.title = e.target.value;
              onChartUpdate(updatedChart);
            }}
            onBlur={() => {
              const updatedChart = JSON.parse(JSON.stringify(selectedChart));
              updatedChart.title = chartTitle;
              const updatedChartPayload = chartPayloadHandler(
                updatedChart,
                'timeline',
                sketchbookId,
                activePage
              );
              updateCustomCharts({
                id: selectedChart.id,
                payload: updatedChartPayload,
                chartType: 'timeline',
              });
            }}
            margin="normal"
          />
        </div>

        {/* Timeline Settings */}
        {/* <div className={styles.controlGroup}>
          <div className={styles.heading}>Timeline Settings</div>
          <FormControl fullWidth margin="normal">
            <InputLabel>Time Unit</InputLabel>
            <Select
              value={selectedChart.options.scales.x.time.unit}
              onChange={(e: any) => {
                const updatedChart = JSON.parse(JSON.stringify(selectedChart));
                updatedChart.options.scales.x.time.unit = e.target.value;
                onChartUpdate(updatedChart);
              }}
              onBlur={(e: any ) => {
                const updatedChart = JSON.parse(JSON.stringify(selectedChart));
                updatedChart.options.scales.x.time.unit = e.target.value;
                const updatedChartPayload = chartPayloadHandler(updatedChart, 'timeline', sketchbookId, activePage);
                updateCustomCharts({
                  id: selectedChart.id,
                  payload: updatedChartPayload,
                  chartType: 'timeline'
                });
              }}
            >
              <MenuItem value="day">Day</MenuItem>
              <MenuItem value="week">Week</MenuItem>
              <MenuItem value="month">Month</MenuItem>
            </Select>
          </FormControl>
        </div> */}

        {/* Visual Settings */}
        <div className={styles.controlGroup}>
          <div className={styles.heading}>Visual Settings</div>

          {/* Tasks Dataset Settings */}
          <div className={styles.datasetSettings}>
            <div className={styles.subheading}>Tasks</div>
            <FormControlLabel
              control={
                <Switch
                  checked={selectedChart?.data?.datasets?.[0]?.fill || false}
                  onChange={(e: any) => {
                    const updatedChart = JSON.parse(
                      JSON.stringify(selectedChart)
                    );
                    if (!updatedChart.data.datasets[0]) {
                      updatedChart.data.datasets[0] = {};
                    }
                    updatedChart.data.datasets[0].fill = e.target.checked;
                    onChartUpdate(updatedChart);
                  }}
                  onBlur={(e: any) => {
                    const updatedChart = JSON.parse(
                      JSON.stringify(selectedChart)
                    );
                    if (!updatedChart.data.datasets[0]) {
                      updatedChart.data.datasets[0] = {};
                    }
                    updatedChart.data.datasets[0].fill = e.target.checked;
                    const updatedChartPayload = chartPayloadHandler(
                      updatedChart,
                      'timeline',
                      sketchbookId,
                      activePage
                    );
                    updateCustomCharts({
                      id: selectedChart.id,
                      payload: updatedChartPayload,
                      chartType: 'timeline',
                    });
                  }}
                />
              }
              label="Show Area Fill"
            />
            <div className={styles.colorPicker}>
              <label>Task Color</label>
              <input
                type="color"
                value={
                  selectedChart?.data?.datasets?.[0]?.backgroundColor ||
                  'rgba(197, 170, 255, 0.7)'
                }
                onChange={(e: any) => {
                  const updatedChart = JSON.parse(
                    JSON.stringify(selectedChart)
                  );
                  if (!updatedChart.data.datasets[0]) {
                    updatedChart.data.datasets[0] = {};
                  }
                  updatedChart.data.datasets[0].backgroundColor =
                    e.target.value;
                  updatedChart.data.datasets[0].borderColor = e.target.value;
                  onChartUpdate(updatedChart);
                }}
                onBlur={(e: any) => {
                  const updatedChart = JSON.parse(
                    JSON.stringify(selectedChart)
                  );
                  if (!updatedChart.data.datasets[0]) {
                    updatedChart.data.datasets[0] = {};
                  }
                  updatedChart.data.datasets[0].backgroundColor =
                    e.target.value;
                  updatedChart.data.datasets[0].borderColor = e.target.value;
                  const updatedChartPayload = chartPayloadHandler(
                    updatedChart,
                    'timeline',
                    sketchbookId,
                    activePage
                  );
                  updateCustomCharts({
                    id: selectedChart.id,
                    payload: updatedChartPayload,
                    chartType: 'timeline',
                  });
                }}
              />
            </div>
          </div>

          {/* Remaining Tasks Dataset Settings */}
          <div className={styles.datasetSettings}>
            <div className={styles.subheading}>Remaining Tasks</div>
            <FormControlLabel
              control={
                <Switch
                  checked={selectedChart?.data?.datasets?.[1]?.fill || false}
                  onChange={(e: any) => {
                    const updatedChart = JSON.parse(
                      JSON.stringify(selectedChart)
                    );
                    if (!updatedChart.data.datasets[1]) {
                      updatedChart.data.datasets[1] = {};
                    }
                    updatedChart.data.datasets[1].fill = e.target.checked;
                    onChartUpdate(updatedChart);
                  }}
                  onBlur={(e: any) => {
                    const updatedChart = JSON.parse(
                      JSON.stringify(selectedChart)
                    );
                    if (!updatedChart.data.datasets[1]) {
                      updatedChart.data.datasets[1] = {};
                    }
                    updatedChart.data.datasets[1].fill = e.target.checked;
                    const updatedChartPayload = chartPayloadHandler(
                      updatedChart,
                      'timeline',
                      sketchbookId,
                      activePage
                    );
                    updateCustomCharts({
                      id: selectedChart.id,
                      payload: updatedChartPayload,
                      chartType: 'timeline',
                    });
                  }}
                />
              }
              label="Show Area Fill"
            />
            <div className={styles.colorPicker}>
              <label>Remaining Tasks Color</label>
              <input
                type="color"
                value={
                  selectedChart?.data?.datasets?.[1]?.backgroundColor ||
                  'rgba(255, 99, 132, 0.7)'
                }
                onChange={(e) => {
                  const updatedChart = JSON.parse(
                    JSON.stringify(selectedChart)
                  );
                  if (!updatedChart.data.datasets[1]) {
                    updatedChart.data.datasets[1] = {};
                  }
                  updatedChart.data.datasets[1].backgroundColor =
                    e.target.value;
                  updatedChart.data.datasets[1].borderColor = e.target.value;
                  onChartUpdate(updatedChart);
                }}
                onBlur={(e: any) => {
                  const updatedChart = JSON.parse(
                    JSON.stringify(selectedChart)
                  );
                  if (!updatedChart.data.datasets[1]) {
                    updatedChart.data.datasets[1] = {};
                  }
                  updatedChart.data.datasets[1].backgroundColor =
                    e.target.value;
                  updatedChart.data.datasets[1].borderColor = e.target.value;
                  const updatedChartPayload = chartPayloadHandler(
                    updatedChart,
                    'timeline',
                    sketchbookId,
                    activePage
                  );
                  updateCustomCharts({
                    id: selectedChart.id,
                    payload: updatedChartPayload,
                    chartType: 'timeline',
                  });
                }}
              />
            </div>
          </div>
        </div>

        {/* Data Tabs */}
        <div className={styles.controlGroup}>
          <div className={styles.heading}>Timeline Data</div>
          <div className={styles.tabs}>
            <button
              className={`${styles.tab} ${activeTab === 'tasks' ? styles.activeTab : ''}`}
              onClick={() => setActiveTab('tasks')}
            >
              Tasks
            </button>
            <button
              className={`${styles.tab} ${activeTab === 'remaining' ? styles.activeTab : ''}`}
              onClick={() => setActiveTab('remaining')}
            >
              Remaining Tasks
            </button>
          </div>

          {/* Data Grid */}
          <div
            className="ag-theme-alpine"
            style={{ height: 400, width: '100%' }}
          >
            <AgGridReact
              ref={gridRef}
              columnDefs={
                activeTab === 'tasks' ? taskColumnDefs : remainingColumnDefs
              }
              rowData={getRowData()}
              rowSelection={{
                mode: 'multiple',
                headerCheckbox: true,
                checkboxes: true,
                enableClickSelection: false,
                enableSelectionWithoutKeys: false,
              }}
              getRowId={(params) =>
                `${params.data.datasetIndex}-${params.data.rowIndex}`
              }
            />
          </div>

          <div className={styles.buttonContainer}>
            <IconButton
              icon={<FaPlus />}
              onClick={addNewDataPoint}
              type="primary"
              title={`Add ${activeTab === 'tasks' ? 'Task' : 'Remaining Task'}`}
              tooltipPosition="top"
            />
            <IconButton
              icon={<FaTrash />}
              onClick={deleteSelectedRows}
              type="secondary"
              title="Delete Selected"
              tooltipPosition="top"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
