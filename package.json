{"name": "neuquip", "private": true, "version": "0.0.0", "type": "module", "developer": "<PERSON><PERSON><PERSON>", "scripts": {"dev": "vite", "build": "node --max-old-space-size=8192 ./node_modules/vite/bin/vite.js build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.9.0", "@lexical/code": "^0.30.0", "@lexical/link": "^0.30.0", "@lexical/list": "^0.30.0", "@lexical/markdown": "^0.30.0", "@lexical/react": "^0.30.0", "@lexical/rich-text": "^0.30.0", "@lexical/table": "^0.30.0", "@mui/icons-material": "^5.16.7", "@mui/material": "^5.16.7", "@mui/system": "^6.4.1", "@reduxjs/toolkit": "https://pkg.csb.dev/reduxjs/redux-toolkit/commit/06a30ee4/@reduxjs/toolkit", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@toast-ui/editor": "^3.2.2", "@toast-ui/react-editor": "^3.2.3", "@types/jspdf": "^2.0.0", "@types/react-color": "^3.0.12", "@types/react-datepicker": "^7.0.0", "@types/react-grid-layout": "^1.3.5", "@types/react-resizable": "^3.0.8", "@types/react-speech-recognition": "^3.9.6", "@uiw/react-md-editor": "^4.0.4", "ag-grid-community": "^32.3.2", "ag-grid-react": "^32.3.2", "axios": "^1.7.4", "chart.js": "^4.4.5", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "driver.js": "^1.3.6", "file-saver": "^2.0.5", "gantt-task-react": "^0.3.9", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "katex": "^0.16.22", "lexical": "^0.30.0", "lodash": "^4.17.21", "mammoth": "^1.8.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.3.31", "re-resizable": "^6.10.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-datepicker": "^7.5.0", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-gauge-chart": "^0.5.1", "react-grid-layout": "^1.5.0", "react-hook-form": "^7.52.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "react-redux": "^9.1.2", "react-resizable": "^3.0.5", "react-router-dom": "^6.25.1", "react-select": "^5.8.1", "react-speech-recognition": "^4.0.1", "react-syntax-highlighter": "^15.6.1", "react-zoom-pan-pinch": "^3.6.1", "reactflow": "^11.11.4", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-gauge-chart": "^0.4.3", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "prettier": "^3.3.2", "typescript": "^5.2.2", "vite": "^5.3.1", "vite-plugin-svgr": "^4.2.0"}}