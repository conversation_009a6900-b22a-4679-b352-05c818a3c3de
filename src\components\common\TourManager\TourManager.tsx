import React, { useState } from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Typography,
  Divider,
  Badge,
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SchoolIcon from '@mui/icons-material/School';
import LiveHelpIcon from '@mui/icons-material/LiveHelp';
import YouTubeIcon from '@mui/icons-material/YouTube';
import { useTour } from '../../../contexts/TourContext';
import VideoTourDialog from '../VideoTourDialog/VideoTourDialog';
import styles from './TourManager.module.css';
import toast from 'react-hot-toast';

const TourManager: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [videoTourOpen, setVideoTourOpen] = useState(false);
  const [selectedVideoTour, setSelectedVideoTour] = useState<{
    videoId: string;
    title: string;
    description?: string;
  } | null>(null);

  const {
    availableTours,
    startTour,
    resetTours,
    hasSeenTour,
    isVideoTour,
    getVideoTourData,
  } = useTour();

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleStartTour = (tourId: string) => {
    // Check if this is a video tour
    if (isVideoTour(tourId)) {
      const videoData = getVideoTourData(tourId);
      if (videoData) {
        setSelectedVideoTour(videoData);
        setVideoTourOpen(true);
        startTour(tourId); // Still call startTour to mark it as seen
      }
    } else {
      // Regular tour
      startTour(tourId);
    }
    handleClose();
  };

  const handleCloseVideoTour = () => {
    setVideoTourOpen(false);
    setSelectedVideoTour(null);
  };

  const handleResetTours = () => {
    toast((t) => (
      <div className={styles.customtoast}>
        <div className={styles.customtoastmessage}>
          ⚠️ <strong>Reset tour history?</strong>
          <div className={styles.customtoastsubtext}>
            This will let you see all the tours again.
          </div>
        </div>
        <button
          onClick={() => {
            resetTours();
            toast.dismiss(t.id);
          }}
          className={styles.customtoastbutton}
        >
          Reset
        </button>
      </div>
    ));
  };

  // Count unseen tours
  const unseenTourCount = availableTours.filter(
    (tour) => !hasSeenTour(tour.id)
  ).length;

  return (
    <>
      <Tooltip title="Help & Interactive Tours" arrow placement="bottom">
        <IconButton
          color="primary"
          onClick={handleOpen}
          className={styles.helpButton}
          size="small"
        >
          <Badge
            badgeContent={unseenTourCount}
            color="error"
            overlap="circular"
          >
            <LiveHelpIcon />
          </Badge>
        </IconButton>
      </Tooltip>

      {/* Video Tour Dialog */}
      {selectedVideoTour && (
        <VideoTourDialog
          open={videoTourOpen}
          onClose={handleCloseVideoTour}
          videoId={selectedVideoTour.videoId}
          title={selectedVideoTour.title}
          description={selectedVideoTour.description}
        />
      )}

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        className={styles.dialog}
        // PaperProps={{
        //   className: styles.dialogPaper,
        // }}
      >
        <DialogTitle className={styles.dialogTitle}>
          <div className={styles.titleContent}>
            <SchoolIcon className={styles.titleIcon} />
            <Typography variant="h6">Interactive Tours</Typography>
          </div>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            className={styles.closeButton}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers className={styles.dialogContent}>
          <Typography variant="body1" className={styles.dialogDescription}>
            Take a guided tour to learn how to use different features of the
            application. Each tour will highlight key elements and provide
            helpful instructions.
          </Typography>
          <List className={styles.tourList}>
            {availableTours.map((tour) => (
              <ListItem
                key={tour.id}
                disablePadding
                className={styles.tourListItem}
              >
                <ListItemButton
                  onClick={() => handleStartTour(tour.id)}
                  className={`${styles.tourItem} ${hasSeenTour(tour.id) ? styles.seenTour : styles.unseenTour}`}
                >
                  <ListItemIcon className={styles.tourIcon}>
                    {isVideoTour(tour.id) ? (
                      <YouTubeIcon color="error" />
                    ) : (
                      <PlayArrowIcon color="primary" />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={tour.name}
                    secondary={
                      isVideoTour(tour.id)
                        ? 'Video tutorial'
                        : `${tour.steps.length} steps • Interactive guide`
                    }
                    primaryTypographyProps={{ className: styles.tourTitle }}
                    secondaryTypographyProps={{
                      className: styles.tourSubtitle,
                    }}
                  />
                  {hasSeenTour(tour.id) ? (
                    <Tooltip title="You've completed this tour" arrow>
                      <CheckCircleIcon
                        color="success"
                        className={styles.completedIcon}
                      />
                    </Tooltip>
                  ) : (
                    <span className={styles.newBadge}>NEW</span>
                  )}
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <Divider />
        <DialogActions className={styles.dialogActions}>
          <Button
            startIcon={<RestartAltIcon />}
            onClick={handleResetTours}
            color="secondary"
            variant="outlined"
            className={styles.resetButton}
          >
            Reset Tour History
          </Button>
          <Button
            onClick={handleClose}
            color="primary"
            variant="contained"
            className={styles.closeDialogButton}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TourManager;
