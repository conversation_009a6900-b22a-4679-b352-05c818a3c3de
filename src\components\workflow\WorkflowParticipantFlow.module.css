.approvalFlow {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow-x: auto;
  padding: 30px 20px;
  gap: 15px;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.approvalFlow::-webkit-scrollbar {
  display: none;
}

.flowItem {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  position: relative;
}

.participantNode {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--color-background-secondary);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  width: 180px;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
  border: 1px solid var(--color-border);
  position: relative;
  color: var(--color-text-primary);
}

.nodeHeader {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
}

.currentBadge {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 20px;
  font-weight: 500;
  letter-spacing: 0.3px;
  background: #fef3c7;
  color: #b45309;
  white-space: nowrap;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-bottom: 12px;
  border: 3px solid #f8fafc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.avatar:hover {
  transform: scale(1.05);
}

.participantInfo {
  text-align: center;
  width: 100%;
}

.participantName {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #1e293b;
}

.badgeContainer {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.participantStatus,
.creatorBadge {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 20px;
  font-weight: 500;
  letter-spacing: 0.3px;
  white-space: nowrap;
}

.creatorBadge {
  background: #e0f2fe;
  color: #0284c7;
}

.connector {
  width: 60px;
  height: 2px;
  background: linear-gradient(to right, #e2e8f0, #94a3b8);
  position: relative;
  margin: 0 15px;
}

.connector::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 8px solid #94a3b8;
}

/* Status Colors */
.default {
  background-color: #e2e8f0;
  color: #64748b;
}

.approved {
  background-color: #dcfce7;
  color: #166534;
}

.rejected {
  background-color: #fee2e2;
  color: #991b1b;
}

.conditionalApproved {
  background-color: #fef9c3;
  color: #854d0e;
}

.pending {
  background-color: #e0f2fe;
  color: #075985;
}

/* Status badges for sequential workflow */
.statusBadge {
  padding: 0.25rem 0.5rem;
  border-radius: 50%;
  font-size: 0.875rem;
  font-weight: 700;
  min-width: 1.5rem;
  min-height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-left: 0.5rem;
}

.statusBadge.approved {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.statusBadge.rejected {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.statusBadge.conditional {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

/* Update current badge for better visibility */
.currentBadge {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 20px;
  font-weight: 600;
  letter-spacing: 0.3px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}
