import React, { memo } from 'react';
import {
  PredefinedPrompt,
  predefinedPrompts,
} from '../../../types/predefinedPrompts';
import styles from './PredefinedPrompts.module.css';
import {
  FiFileText,
  FiDatabase,
  FiSearch,
  FiList,
  FiUsers,
  FiTrendingUp,
  FiCode,
  FiPackage,
  FiBarChart2,
  FiDollarSign,
} from 'react-icons/fi';
import { useTheme } from '../../../hooks/useTheme';

interface PredefinedPromptsProps {
  onPromptSelect: (prompt: PredefinedPrompt) => void;
  selectedCategory: string;
}

const getPromptIcon = (id: string) => {
  switch (id) {
    case 'summarize':
      return <FiFileText size={24} />;
    case 'key-points':
      return <FiList size={24} />;
    case 'analyze-data':
      return <FiDatabase size={24} />;
    case 'find-info':
      return <FiSearch size={24} />;
    case 'marketing-audience':
    case 'client-insights':
      return <FiUsers size={24} />;
    case 'marketing-content':
    case 'user-stories':
      return <FiFileText size={24} />;
    case 'campaign-analysis':
    case 'pipeline-analysis':
    case 'sales-forecast':
    case 'trend-analysis':
      return <FiTrendingUp size={24} />;
    case 'competitor-research':
    case 'competitive-analysis':
      return <FiSearch size={24} />;
    case 'code-review':
    case 'error-analysis':
      return <FiCode size={24} />;
    case 'architecture-analysis':
    case 'tech-specs':
      return <FiFileText size={24} />;
    case 'feature-analysis':
    case 'roadmap-planning':
      return <FiPackage size={24} />;
    case 'data-visualization':
    case 'statistical-analysis':
    case 'data-cleaning':
      return <FiBarChart2 size={24} />;
    case 'financial-summary':
    case 'expense-analysis':
    case 'budget-planning':
    case 'investment-analysis':
      return <FiDollarSign size={24} />;
    default:
      return <FiFileText size={24} />;
  }
};

const PromptCard = memo(
  ({
    prompt,
    onSelect,
  }: {
    prompt: PredefinedPrompt;
    onSelect: (prompt: PredefinedPrompt) => void;
    theme: any;
  }) => (
    <button
      id={prompt.id}
      key={prompt.id}
      className={styles.promptCard}
      onClick={() => onSelect(prompt)}
      style={{
        borderColor: `var(--color-primary-light)20`,
        background: 'var(--color-background-card)',
      }}
    >
      <div
        className={styles.promptIcon}
        style={{
          color: 'var(--color-primary)',
          background: 'var(--color-primary-light)10',
          borderColor: 'var(--color-primary)20',
        }}
      >
        {getPromptIcon(prompt.id)}
      </div>
      <div className={styles.promptContent}>
        <h4 style={{ color: 'var(--color-text-primary)' }}>{prompt.title}</h4>
        <p style={{ color: 'var(--color-text-secondary)' }}>
          {prompt.description}
        </p>
      </div>
    </button>
  )
);

PromptCard.displayName = 'PromptCard';

export const PredefinedPrompts: React.FC<PredefinedPromptsProps> = memo(
  ({ onPromptSelect, selectedCategory }) => {
    const theme = useTheme();

    // Get the prompts for the selected category
    const categoryPrompts = predefinedPrompts[selectedCategory] || [];

    return (
      <div id="predefinedPrompts" className={styles.promptsContainer}>
        <div className={styles.promptsGrid}>
          {categoryPrompts.map((prompt) => (
            <PromptCard
              key={prompt.id}
              prompt={prompt}
              onSelect={onPromptSelect}
              theme={theme}
            />
          ))}
        </div>
      </div>
    );
  }
);
