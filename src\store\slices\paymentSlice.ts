import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  PaymentState,
  PaymentPlan,
  PaymentIntent,
  PaymentMethod,
  PaymentRecord,
  PaymentError,
} from '../../types/payment';

const initialState: PaymentState = {
  isLoading: false,
  isProcessing: false,
  currentPlan: null,
  paymentIntent: null,
  paymentMethods: [],
  error: null,
  success: false,
  lastPayment: null,
};

const paymentSlice = createSlice({
  name: 'payment',
  initialState,
  reducers: {
    // Loading states
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setProcessing: (state, action: PayloadAction<boolean>) => {
      state.isProcessing = action.payload;
    },

    // Plan selection
    setCurrentPlan: (state, action: PayloadAction<PaymentPlan | null>) => {
      state.currentPlan = action.payload;
      state.error = null;
    },

    // Payment intent management
    setPaymentIntent: (state, action: PayloadAction<PaymentIntent | null>) => {
      state.paymentIntent = action.payload;
    },

    // Payment methods
    setPaymentMethods: (state, action: PayloadAction<PaymentMethod[]>) => {
      state.paymentMethods = action.payload;
    },
    addPaymentMethod: (state, action: PayloadAction<PaymentMethod>) => {
      state.paymentMethods.push(action.payload);
    },
    removePaymentMethod: (state, action: PayloadAction<string>) => {
      state.paymentMethods = state.paymentMethods.filter(
        (method) => method.id !== action.payload
      );
    },

    // Error handling
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false;
      state.isProcessing = false;
      state.success = false;
    },
    clearError: (state) => {
      state.error = null;
    },

    // Success handling
    setSuccess: (state, action: PayloadAction<boolean>) => {
      state.success = action.payload;
      if (action.payload) {
        state.error = null;
        state.isLoading = false;
        state.isProcessing = false;
      }
    },

    // Payment completion
    setLastPayment: (state, action: PayloadAction<PaymentRecord | null>) => {
      state.lastPayment = action.payload;
      if (action.payload) {
        state.success = true;
        state.error = null;
        state.isProcessing = false;
      }
    },

    // Reset payment state
    resetPaymentState: (state) => {
      state.isLoading = false;
      state.isProcessing = false;
      state.paymentIntent = null;
      state.error = null;
      state.success = false;
    },

    // Clear all payment data
    clearPaymentData: () => {
      return initialState;
    },

    // Payment flow actions
    startPaymentFlow: (state, action: PayloadAction<PaymentPlan>) => {
      state.currentPlan = action.payload;
      state.isLoading = true;
      state.error = null;
      state.success = false;
    },

    // Clear loading state when payment page is ready
    clearPaymentLoading: (state) => {
      state.isLoading = false;
    },

    completePaymentFlow: (state, action: PayloadAction<PaymentRecord>) => {
      state.lastPayment = action.payload;
      state.success = true;
      state.isLoading = false;
      state.isProcessing = false;
      state.error = null;
      state.paymentIntent = null;
    },

    failPaymentFlow: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.success = false;
      state.isLoading = false;
      state.isProcessing = false;
    },
  },
});

export const {
  setLoading,
  setProcessing,
  setCurrentPlan,
  setPaymentIntent,
  setPaymentMethods,
  addPaymentMethod,
  removePaymentMethod,
  setError,
  clearError,
  setSuccess,
  setLastPayment,
  resetPaymentState,
  clearPaymentData,
  startPaymentFlow,
  clearPaymentLoading,
  completePaymentFlow,
  failPaymentFlow,
} = paymentSlice.actions;

export default paymentSlice.reducer;

// Selectors
export const selectPaymentState = (state: { payment: PaymentState }) =>
  state.payment;
export const selectCurrentPlan = (state: { payment: PaymentState }) =>
  state.payment.currentPlan;
export const selectPaymentIntent = (state: { payment: PaymentState }) =>
  state.payment.paymentIntent;
export const selectPaymentMethods = (state: { payment: PaymentState }) =>
  state.payment.paymentMethods;
export const selectPaymentError = (state: { payment: PaymentState }) =>
  state.payment.error;
export const selectPaymentSuccess = (state: { payment: PaymentState }) =>
  state.payment.success;
export const selectLastPayment = (state: { payment: PaymentState }) =>
  state.payment.lastPayment;
export const selectIsPaymentLoading = (state: { payment: PaymentState }) =>
  state.payment.isLoading;
export const selectIsPaymentProcessing = (state: { payment: PaymentState }) =>
  state.payment.isProcessing;
