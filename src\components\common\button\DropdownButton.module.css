.button {
  padding: 0.5rem 1rem;
  background-color: var(--color-background-secondary);
  color: var(--color-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  cursor: pointer;
  width: 100%;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: 400;
  line-height: 19.5px;
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.active {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.button:hover {
  border-color: var(--color-primary);
  color: var(--color-primary-contrast);
  background-color: var(--color-primary);
}
