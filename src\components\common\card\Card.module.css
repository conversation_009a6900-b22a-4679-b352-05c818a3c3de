.card {
  background: var(--color-background-secondary);
  border-radius: 20px;
  padding: 1.75rem;
  transition:
    all 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    background-color 0.3s ease,
    border-color 0.3s ease;
  border: 1px solid var(--color-primary);
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  /* min-width: 240px; */
  /* max-width: 100%; */
  color: var(--color-text-primary);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 10px 15px rgba(0, 0, 0, 0.1);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4299e1, #667eea);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover::before {
  opacity: 1;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.initialsWrapper {
  display: flex;
  align-items: center;
  min-width: 48px;
  height: 48px;
}

.initials {
  width: 52px;
  height: 52px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.25rem;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.title {
  height: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
  line-height: 1.4;
  letter-spacing: -0.01em;
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  transition: color 0.3s ease;
}

.avatarStack {
  display: flex;
  align-items: center;
  padding-left: 8px;
}

.avatarWrapper {
  position: relative;
  width: 24px;
  height: 24px;
}

.userAvatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid white;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.avatarHover {
  position: absolute;
  background: #2d3748;
  color: white;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-4px);
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.avatarWrapper:hover .avatarHover {
  opacity: 1;
  transform: translateX(-50%) translateY(-8px);
}

.extraUsers {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #edf2f7;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  margin-left: -8px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.footer {
  /* display: flex;
  align-items: center;
  justify-content: space-between; */
  margin-top: auto;
  padding-top: 1.25rem;
  border-top: 1px solid #edf2f7;
}

.timeInfo {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.timeInfoWrapper {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--color-text-secondary);
  font-size: 14px;
  transition: color 0.3s ease;
}
.editIcon {
  font-size: 1.125rem;
  color: var(--color-text-secondary);
  transition: color 0.3s ease;
}

.optionsWrapper {
  position: relative;
}

.optionsButton {
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.optionsButton:hover {
  background-color: #f7fafc;
}

.optionsIcon {
  color: #718096;
  font-size: 1.25rem;
  transition: color 0.2s ease;
}

.optionsMenu {
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 180px;
  z-index: 100;
  padding: 8px;
  animation: slideIn 0.2s ease-out;
}

.optionItem {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 10px 12px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #424242;
  font-size: 14px;
}

.optionItem:hover {
  background-color: #f5f5f5;
}

.optionIcon {
  font-size: 18px;
}

.deleteOption {
  color: #d32f2f;
}

.deleteOption:hover {
  background-color: #ffebee;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.optionsDivider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 8px 0;
}

.creatorBadge {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* background: #ebf8ff; */
  color: #2b6cb0;
  /* padding: 0.375rem 0.75rem; */
  margin-top: 5px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.favButton {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  margin-left: 4px;
}

.favButton:hover {
  background: #f5f5f5;
  color: #4f46e5;
}

.starIcon {
  font-size: 18px;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

/* Type-specific card styles */
.libraryCard {
  border-color: var(--color-info-main);
}

.libraryCard::before {
  background: linear-gradient(
    90deg,
    var(--color-info-main),
    var(--color-info-light)
  );
}

.libraryCard:hover {
  border-color: var(--color-info-dark);
}

.projectCard {
  border-color: var(--color-primary);
}

.projectCard::before {
  background: linear-gradient(
    90deg,
    var(--color-primary),
    var(--color-primary-light)
  );
}

.workflowCard {
  border-color: var(--color-warning-main);
}

.workflowCard::before {
  background: linear-gradient(
    90deg,
    var(--color-warning-main),
    var(--color-warning-light)
  );
}
