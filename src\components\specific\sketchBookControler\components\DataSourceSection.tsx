import React from 'react';
import styles from '../ChartPropertyController.module.css';
import CustomButton from '../../../common/button/CustomButton';

interface DataSourceSectionProps {
  dataSources: any[];
  selectedDataSource: string;
  setSelectedDataSource: (dataSourceId: string) => void;
  handleFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  applyDataSource: () => void;
}

export const DataSourceSection = ({
  dataSources,
  selectedDataSource,
  setSelectedDataSource,
  handleFileUpload,
  applyDataSource,
}: DataSourceSectionProps) => (
  <div className={styles.section}>
    <div className={styles.heading}>Data Source</div>
    <div className={styles.dataSourceControls}>
      <input
        type="file"
        accept=".xlsx,.xls"
        onChange={handleFileUpload}
        className={styles.fileInput}
        id="excel-upload"
      />
      <label
        htmlFor="excel-upload"
        // style={{ border: '1px solid red' }}
        className={styles.fileInputLabel}
      >
        Upload Excel File
      </label>

      {dataSources.length > 0 && (
        <>
          <select
            value={selectedDataSource || ''}
            onChange={(e) => setSelectedDataSource(e.target.value)}
            className={styles.dataSourceSelect}
          >
            <option value="">Select Data Source</option>
            {dataSources.map((ds) => (
              <option key={ds.id} value={ds.id}>
                {ds.name}
              </option>
            ))}
          </select>
          <CustomButton
            onClick={applyDataSource}
            // className={styles.applyButton}
            label=" Apply to Chart"
            disabled={!selectedDataSource}
          />
        </>
      )}
    </div>
  </div>
);
