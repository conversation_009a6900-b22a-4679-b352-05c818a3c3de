import { useState } from 'react';
import { useUpdateWorkflowMutation } from '../../services/workflowServices';
import { ActionType, Workflow } from '../../types/workflow/index';
import { useNavigate } from 'react-router-dom';
import { useNotifications } from '../useNotifications';
import useLocalStorage from '../useLocalStorage';

export const useWorkflowActions = (workflow: Workflow) => {
  const [message, setMessage] = useState('');
  const [showMessageInput, setShowMessageInput] = useState(false);
  const [actionType, setActionType] = useState<ActionType>(null);
  const [updateWorkflow] = useUpdateWorkflowMutation();
  const [currentUser] = useLocalStorage('user', null);
  const navigate = useNavigate();
  const { createStatusChangeNotification, updateWorkflowNotificationStatus } =
    useNotifications(true); // Enable API notifications

  const handleMessageSubmit = async () => {
    if (!message.trim()) return;

    try {
      const currentIndex = workflow.participants.findIndex(
        (p) => p.userId === workflow.currentUserId
      );

      if (currentIndex === -1)
        throw new Error('Current user not found in participants.');

      const participantData = {
        note: message,
        approval_message: actionType === 'approve' ? message : '',
        rejection_message: actionType === 'reject' ? message : '',
        conditional_approved_message:
          actionType === 'conditional' ? message : '',
        status:
          actionType === 'approve'
            ? 'approved'
            : actionType === 'reject'
              ? 'rejected'
              : actionType === 'conditional'
                ? 'conditional'
                : '',
      };

      // Find the next participant (if any)
      const nextParticipant =
        actionType !== 'reject'
          ? workflow.participants[currentIndex + 1]
          : null;

      const payload = {
        fullyApproved:
          (actionType === 'approve' || actionType === 'conditional') &&
          !nextParticipant,
        currentUserId:
          actionType === 'reject' ? null : nextParticipant?.userId || null,
        approvalStatus:
          actionType === 'reject'
            ? 'rejected'
            : !nextParticipant
              ? 'approved'
              : 'in-progress',
        status: actionType === 'reject' ? 'pending' : 'pending',
        participantIndex: currentIndex,
        participantData: participantData,
      };

      await updateWorkflow({
        payload,
        workflowId: workflow.id,
      }).unwrap();

      // Create notifications for status changes
      const workflowTitle = workflow.title || 'Untitled Workflow';
      const creatorName = currentUser?.name;
      const priority = workflow.priority as 'Low' | 'Medium' | 'High';

      // Determine final status for notifications
      let finalStatus: 'approved' | 'rejected' | 'conditional' | 'in-progress';
      if (actionType === 'reject') {
        finalStatus = 'rejected';
      } else if (actionType === 'approve' && !nextParticipant) {
        finalStatus = 'approved';
      } else if (actionType === 'conditional' && !nextParticipant) {
        finalStatus = 'conditional';
      } else {
        finalStatus = 'in-progress';
      }

      // Notify workflow creator about status change
      if (workflow.creatorId && workflow.creatorId !== currentUser?.id) {
        console.log(
          `🔔 Notifying workflow creator (${workflow.creatorId}) about status: ${finalStatus}`
        );
        createStatusChangeNotification(
          workflow.id,
          workflowTitle,
          finalStatus,
          workflow.creatorId.toString(),
          creatorName,
          priority,
          workflow.creatorId.toString() // Pass creatorId for targeting
        );
      }

      // If there's a next participant, notify them about assignment
      if (nextParticipant && actionType !== 'reject') {
        console.log(
          `🔔 Notifying next participant (${nextParticipant.userId}) about assignment`
        );
        createStatusChangeNotification(
          workflow.id,
          workflowTitle,
          'in-progress',
          nextParticipant.userId.toString(),
          creatorName,
          priority,
          workflow.creatorId?.toString() // Pass creatorId for targeting logic
        );
      }

      // If workflow is completed (approved/rejected), notify all participants
      if (actionType === 'reject' || !nextParticipant) {
        console.log(
          `🔔 Workflow completed with status: ${finalStatus}. Notifying all participants.`
        );

        // Create a single notification that will be sent to all participants
        // The backend will handle excluding the current user (action performer)
        createStatusChangeNotification(
          workflow.id,
          workflowTitle,
          finalStatus,
          'all-participants', // Special identifier for completion notifications
          creatorName,
          priority,
          workflow.creatorId?.toString() // Pass creatorId for targeting logic
        );
      }

      // Update notification status for this workflow
      updateWorkflowNotificationStatus(workflow.id, finalStatus);

      setMessage('');
      setShowMessageInput(false);
      setActionType(null);
      navigate(-1);
    } catch (error) {
      console.error('Error updating workflow:', error);
    }
  };

  const handleAction = (type: ActionType) => {
    setActionType(type);
    setShowMessageInput(true);
  };

  const handleCancelAction = () => {
    setShowMessageInput(false);
    setActionType(null);
    setMessage('');
  };

  return {
    message,
    setMessage,
    showMessageInput,
    actionType,
    handleMessageSubmit,
    handleAction,
    handleCancelAction,
  };
};
