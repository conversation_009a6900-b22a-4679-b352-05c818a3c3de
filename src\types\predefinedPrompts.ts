export interface PredefinedPrompt {
  id: string;
  title: string;
  description: string;
  prompts: Record<string, string>;
  supportedFileTypes: string[];
  category: string;
}

// Create a set of prompts for each category
export const predefinedPrompts: Record<string, PredefinedPrompt[]> = {
  featured: [
    {
      id: 'summarize',
      title: 'Summarize Document',
      description: 'Get a comprehensive summary of the document',
      prompts: {
        Featured_1: 'Summarize the document',
        Featured_2: 'Extract the main points and key takeaways',
        Featured_3: 'Analyze the data in this file and provide key insights',
        Featured_4: 'Search for specific information in the document',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'featured',
    },
    {
      id: 'key-points',
      title: 'Extract Key Points',
      description: 'Extract the main points and key takeaways',
      prompts: {
        Featured_5: 'Extract the key points and main takeaways',
        Featured_6: 'Summarize the document',
        Featured_7: 'Analyze the data in this file and provide key insights',
        Featured_8: 'Search for specific information in the document',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'featured',
    },
    {
      id: 'analyze-data',
      title: 'Analyze Data',
      description: 'Analyze data and provide insights',
      prompts: {
        Featured_9: 'Analyze the data in this file and provide key insights',
        Featured_10: 'Summarize the document',
        Featured_11: 'Extract the main points and key takeaways',
        Featured_12: 'Search for specific information in the document',
      },
      supportedFileTypes: ['.xlsx', '.csv'],
      category: 'featured',
    },
    {
      id: 'find-info',
      title: 'Find Information',
      description: 'Search for specific information in the document',
      prompts: {
        Featured_13: 'Search for specific information in the document',
        Featured_14: 'Summarize the document',
        Featured_15: 'Extract the main points and key takeaways',
        Featured_16: 'Analyze the data in this file and provide key insights',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'featured',
    },
  ],
  academic_research: [
    {
      id: 'research-summary',
      title: 'Research Summarization & Review',
      description: 'Summarize research papers and literature reviews',
      prompts: {
        'Academic Research_1': 'Summarize research findings',
        'Academic Research_11': 'Review literature and studies',
        'Academic Research_18': 'Synthesize research conclusions',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'academic_research',
    },
    {
      id: 'methodology-analysis',
      title: 'Methodology & Analysis',
      description: 'Analyze research methodologies and approaches',
      prompts: {
        'Academic Research_6': 'Evaluate research methodology',
        'Academic Research_14': 'Analyze research design',
        'Academic Research_19': 'Review analytical approaches',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'academic_research',
    },
    {
      id: 'citation-formatting',
      title: 'Citation & Formatting',
      description: 'Format citations and bibliographic references',
      prompts: {
        'Academic Research_4': 'Format citations properly',
        'Academic Research_10': 'Review bibliography structure',
        'Academic Research_16': 'Check citation consistency',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'academic_research',
    },
    {
      id: 'data-extraction',
      title: 'Data Extraction & Statistical Analysis',
      description: 'Extract research data and perform statistical analyses',
      prompts: {
        'Academic Research_13': 'Extract research data points',
        'Academic Research_24': 'Perform statistical analysis',
        'Academic Research_22': 'Interpret research findings',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'academic_research',
    },
  ],
  legal_compliance: [
    {
      id: 'contract-review',
      title: 'Contract & Agreement Review',
      description: 'Review legal contracts and agreements',
      prompts: {
        'Legal & Compliance_1': 'Review contract terms',
        'Legal & Compliance_4': 'Identify legal risks',
        'Legal & Compliance_13': 'Assess agreement clarity',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'legal_compliance',
    },
    {
      id: 'regulatory-compliance',
      title: 'Regulatory Compliance',
      description: 'Assess compliance with regulations and standards',
      prompts: {
        'Legal & Compliance_5': 'Evaluate compliance requirements',
        'Legal & Compliance_9': 'Review regulatory frameworks',
        'Legal & Compliance_23': 'Assess compliance risk',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'legal_compliance',
    },
    {
      id: 'risk-assessment',
      title: 'Risk & Liability Assessment',
      description: 'Evaluate legal risks and potential liabilities',
      prompts: {
        'Legal & Compliance_2': 'Identify liability issues',
        'Legal & Compliance_11': 'Assess legal exposure',
        'Legal & Compliance_22': 'Review risk mitigation strategies',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'legal_compliance',
    },
    {
      id: 'document-comparison',
      title: 'Document Comparison & Consistency',
      description: 'Compare legal documents for inconsistencies',
      prompts: {
        'Legal & Compliance_15': 'Compare document versions',
        'Legal & Compliance_24': 'Identify discrepancies',
        'Legal & Compliance_16': 'Ensure terminology consistency',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'legal_compliance',
    },
  ],
  medical_healthcare: [
    {
      id: 'clinical-research',
      title: 'Clinical Research & Trials',
      description: 'Analyze clinical studies and trial data',
      prompts: {
        'Medical & Healthcare_2': 'Review clinical trial data',
        'Medical & Healthcare_5': 'Analyze research protocols',
        'Medical & Healthcare_15': 'Evaluate trial outcomes',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'medical_healthcare',
    },
    {
      id: 'medical-data',
      title: 'Medical Data Analysis',
      description: 'Analyze healthcare data and patient records',
      prompts: {
        'Medical & Healthcare_9': 'Analyze patient data',
        'Medical & Healthcare_13': 'Review healthcare metrics',
        'Medical & Healthcare_22': 'Interpret clinical findings',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'medical_healthcare',
    },
    {
      id: 'medical-guidelines',
      title: 'Medical Guidelines & Compliance',
      description: 'Review medical guidelines and compliance documents',
      prompts: {
        'Medical & Healthcare_6': 'Review treatment guidelines',
        'Medical & Healthcare_19': 'Assess protocol compliance',
        'Medical & Healthcare_23': 'Evaluate standard procedures',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'medical_healthcare',
    },
    {
      id: 'treatment-analysis',
      title: 'Drug & Treatment Analysis',
      description: 'Analyze treatments, medications, and efficacy data',
      prompts: {
        'Medical & Healthcare_7': 'Analyze treatment efficacy',
        'Medical & Healthcare_12': 'Review medication data',
        'Medical & Healthcare_14': 'Assess therapeutic outcomes',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'medical_healthcare',
    },
  ],
  business_strategy: [
    {
      id: 'market-analysis',
      title: 'Market Analysis & Competitive Insights',
      description: 'Analyze markets and competitive landscapes',
      prompts: {
        'Business & Strategy_3': 'Analyze market trends',
        'Business & Strategy_7': 'Review competitive landscape',
        'Business & Strategy_17': 'Identify market opportunities',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'business_strategy',
    },
    {
      id: 'financial-planning',
      title: 'Financial Planning & Investment',
      description: 'Evaluate financial plans and investment strategies',
      prompts: {
        'Business & Strategy_5': 'Evaluate investment strategies',
        'Business & Strategy_15': 'Review financial projections',
        'Business & Strategy_20': 'Assess funding requirements',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'business_strategy',
    },
    {
      id: 'growth-strategy',
      title: 'Growth & Expansion Strategies',
      description: 'Develop strategies for business growth',
      prompts: {
        'Business & Strategy_4': 'Develop growth plans',
        'Business & Strategy_12': 'Evaluate expansion options',
        'Business & Strategy_19': 'Analyze market entry strategies',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt'],
      category: 'business_strategy',
    },
    {
      id: 'revenue-models',
      title: 'Operational & Revenue Models',
      description: 'Analyze business operations and revenue generation',
      prompts: {
        'Business & Strategy_11': 'Review operational processes',
        'Business & Strategy_14': 'Analyze revenue streams',
        'Business & Strategy_18': 'Evaluate business models',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'business_strategy',
    },
  ],
  finance_accounting: [
    {
      id: 'financial-performance',
      title: 'Financial Performance Analysis',
      description: 'Analyze financial statements and performance metrics',
      prompts: {
        'Finance & Accounting_1': 'Analyze financial statements',
        'Finance & Accounting_5': 'Review performance metrics',
        'Finance & Accounting_8': 'Evaluate profitability ratios',
        'Finance & Accounting_9': 'Assess financial health indicators',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'finance_accounting',
    },
    {
      id: 'risk-compliance',
      title: 'Risk & Compliance',
      description: 'Assess financial risks and regulatory compliance',
      prompts: {
        'Finance & Accounting_3': 'Identify compliance risks',
        'Finance & Accounting_7': 'Review regulatory requirements',
        'Finance & Accounting_14': 'Audit financial controls',
        'Finance & Accounting_21': 'Assess risk management frameworks',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'finance_accounting',
    },
    {
      id: 'investment-valuation',
      title: 'Investment & Valuation',
      description: 'Analyze investments and asset valuations',
      prompts: {
        'Finance & Accounting_6': 'Evaluate investment opportunities',
        'Finance & Accounting_15': 'Calculate valuation metrics',
        'Finance & Accounting_25': 'Analyze ROI and payback periods',
      },
      supportedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.xlsx', '.csv'],
      category: 'finance_accounting',
    },
    {
      id: 'cost-management',
      title: 'Cost & Expense Management',
      description: 'Optimize costs and manage expenses',
      prompts: {
        'Finance & Accounting_10': 'Optimize cost structures',
        'Finance & Accounting_17': 'Review expense allocation',
        'Finance & Accounting_18': 'Identify cost-saving opportunities',
      },
      supportedFileTypes: ['.xlsx', '.csv'],
      category: 'finance_accounting',
    },
  ],
};
