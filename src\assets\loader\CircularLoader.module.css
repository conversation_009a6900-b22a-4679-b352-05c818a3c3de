.loaderContainer {
  display: flex;
  flex-direction: column; /* Stack the loader and text vertically */
  justify-content: center;
  align-items: center;
  height: 100%;
  perspective: 1000px;
  position: relative;
}

.loaderWrapper {
  position: relative;
  width: fit-content; /* Ensure enough space for both loaders */
  height: 20px;
  margin-left: -50px;
}

.loader {
  border-radius: 50%;
  background: linear-gradient(107.11deg, #c6abff 12.42%, #abd7ff 100%);
  mask: radial-gradient(farthest-side, transparent calc(100% - 6px), black 0);
  -webkit-mask: radial-gradient(
    farthest-side,
    transparent calc(100% - 6px),
    black 0
  );
  position: absolute; /* Make loaders overlap */
  top: 0;
  left: 0;
}

/* Color variants */
.colorPrimary {
  background: linear-gradient(107.11deg, #c6abff 12.42%, #abd7ff 100%);
}

.colorWarning {
  background: linear-gradient(107.11deg, #ffcc80 12.42%, #ffab40 100%);
}

.colorError {
  background: linear-gradient(107.11deg, #ff8a80 12.42%, #ff5252 100%);
}

.loaderHorizontal {
  animation: rotateHorizontal 3s linear infinite;
}

.loaderVertical {
  animation: rotateVertical 3s linear infinite;
}

@keyframes rotateHorizontal {
  0% {
    transform: rotateX(0deg);
  }
  100% {
    transform: rotateX(360deg);
  }
}

@keyframes rotateVertical {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

/* Loader size classes */
.small {
  width: 20px;
  height: 20px;
}

.medium {
  width: 40px;
  height: 40px;
}

.large {
  width: 50px;
  height: 50px;
}

/* Text styling */
.loaderText {
  margin-top: 20px; /* Space between loader and text */
  font-size: 16px;
  color: #333;
  text-align: center;
  /* margin-left: 50px; */
  font-weight: 500;
}
