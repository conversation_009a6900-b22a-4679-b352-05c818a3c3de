.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--color-background-primary);
  transition: background-color 0.3s ease;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--color-background-secondary);
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}

.headerLeft {
  display: flex;
  gap: 1.5rem;
  /* align-items: center; */
  flex-direction: column;
}

.headerRight {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.subtitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.section {
  background: var(--color-background-secondary);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}

.sectionTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--color-border);
  transition:
    color 0.3s ease,
    border-color 0.3s ease;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.field {
  background: var(--color-background-tertiary);
  padding: 1rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.label {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.value {
  font-size: 1rem;
  color: var(--color-text-primary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.participants {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.participant {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--color-background-tertiary);
  border-radius: 8px;
  transition:
    transform 0.2s,
    background-color 0.3s ease;
}

.participant:hover {
  transform: translateX(4px);
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.participantInfo {
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.participantName {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--color-text-primary);
  text-align: center;
  width: 100%;
  transition: color 0.3s ease;
}

.badgeContainer {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.participantStatus,
.creatorBadge {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 20px;
  font-weight: 500;
  letter-spacing: 0.3px;
  white-space: nowrap;
}

.creatorBadge {
  background: #e0f2fe;
  color: #0284c7;
}

.participantActions {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.75rem;
}

.participantStatus {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

.approved {
  background: #dcfce7;
  color: #166534;
}

.rejected {
  background: #fee2e2;
  color: #991b1b;
}

.conditionalApproved {
  background: #fef9c3;
  color: #854d0e;
}

.pending {
  background: #e0f2fe;
  color: #075985;
}

.message {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  transition: color 0.3s ease;
}

.messageInput {
  background: var(--color-background-secondary);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}

.textarea {
  width: 100%;
  min-height: 120px;
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.95rem;
  resize: vertical;
  background-color: var(--color-background-tertiary);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.messageActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.actions {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--color-background-secondary);
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}

/* Status Colors */
.default {
  background-color: #e2e8f0;
  color: #64748b;
  border-color: #64748b;
}

.inProgress {
  background-color: #dbeafe;
  color: #2563eb;
  border-color: #2563eb;
}

.pending {
  background-color: #fef3c7;
  color: #d97706;
  border-color: #d97706;
}

.approved {
  background-color: #dcfce7;
  color: #16a34a;
  border-color: #16a34a;
}

.rejected {
  background-color: #fee2e2;
  color: #dc2626;
  border-color: #dc2626;
}

/* Common styles for all status classes */
.default,
.inProgress,
.pending,
.approved,
.rejected {
  width: fit-content;
  align-self: flex-end;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-flex;
  align-items: center;
  border: 1px solid;
}

.approvalFlow {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow-x: auto;
  padding: 30px 20px;
  gap: 15px;
  /* Add smooth scrolling */
  scroll-behavior: smooth;
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.approvalFlow::-webkit-scrollbar {
  display: none;
}

.flowItem {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  position: relative;
}

.participantNode {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--color-background-secondary);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  width: 180px;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    border-color 0.3s ease;
  border: 1px solid var(--color-border);
  position: relative;
  color: var(--color-text-primary);
}

.nodeHeader {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
}

.currentBadge {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 20px;
  font-weight: 500;
  letter-spacing: 0.3px;
  background: #fef3c7;
  color: #b45309;
  white-space: nowrap;
}

.badgeContainer {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.participantStatus,
.creatorBadge {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 20px;
  font-weight: 500;
  letter-spacing: 0.3px;
  white-space: nowrap;
}

.creatorBadge {
  background: #e0f2fe;
  color: #0284c7;
}

.participantNode:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.connector {
  width: 60px;
  height: 2px;
  background: linear-gradient(to right, #e2e8f0, #94a3b8);
  position: relative;
  margin: 0 15px;
}

.connector::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 8px solid #94a3b8;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-bottom: 12px;
  border: 3px solid #f8fafc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.avatar:hover {
  transform: scale(1.05);
}

.participantInfo {
  text-align: center;
  width: 100%;
}

.participantName {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #1e293b;
}

.participantStatus {
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

.creatorBadge,
.currentBadge {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 20px;
  margin-left: 4px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.creatorBadge {
  background: #e0f2fe;
  color: #0284c7;
}

.currentBadge {
  background: #fef3c7;
  color: #b45309;
}

.participantMessages {
  margin-top: 30px;
  padding: 20px;
  background: var(--color-background-tertiary);
  border-radius: 12px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}

.message {
  margin-bottom: 12px;
  padding: 12px 16px;
  background: var(--color-background-secondary);
  border-radius: 8px;
  border: 1px solid var(--color-border);
  transition:
    transform 0.2s ease,
    background-color 0.3s ease,
    border-color 0.3s ease;
  color: var(--color-text-primary);
}

.message:hover {
  transform: translateX(4px);
}

/* Loading and Error States */
.loaderContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  background: var(--color-background-primary);
  transition: background-color 0.3s ease;
}

.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  background: var(--color-background-primary);
  padding: 2rem;
  transition: background-color 0.3s ease;
}

.errorContent {
  text-align: center;
  max-width: 400px;
}

.errorIcon {
  color: var(--color-error, #ef4444);
  margin-bottom: 1rem;
}

.errorContent h2 {
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

.errorContent p {
  color: var(--color-text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.5;
  transition: color 0.3s ease;
}

/* Sequential workflow status styles */
.statusMessage {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.statusIcon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.statusText {
  flex: 1;
  font-size: 0.95rem;
}

/* Status message types */
.statusMessage.action-required {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  color: #92400e;
}

.statusMessage.waiting {
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  border: 1px solid #0288d1;
  color: #01579b;
}

.statusMessage.completed {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border: 1px solid #10b981;
  color: #065f46;
}

.statusMessage.info {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid #9ca3af;
  color: #374151;
}

/* Waiting and completed message styles */
.waitingMessage,
.completedMessage {
  text-align: center;
  padding: 2rem;
  margin: 2rem 0;
  border-radius: 12px;
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border);
}

.waitingMessage p,
.completedMessage p {
  color: var(--color-text-secondary);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.waitingMessage {
  background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
  border-color: #a855f7;
}

.waitingMessage p {
  color: #7c3aed;
}

.completedMessage {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: #22c55e;
}

.completedMessage p {
  color: #16a34a;
}

/* Dark theme support */
[data-theme='dark'] .statusMessage.action-required {
  background: linear-gradient(135deg, #451a03 0%, #78350f 100%);
  border-color: #d97706;
  color: #fbbf24;
}

[data-theme='dark'] .statusMessage.waiting {
  background: linear-gradient(135deg, #0c4a6e 0%, #075985 100%);
  border-color: #0ea5e9;
  color: #7dd3fc;
}

[data-theme='dark'] .statusMessage.completed {
  background: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
  border-color: #059669;
  color: #6ee7b7;
}

[data-theme='dark'] .statusMessage.info {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-color: #6b7280;
  color: #d1d5db;
}

[data-theme='dark'] .waitingMessage {
  background: linear-gradient(135deg, #581c87 0%, #7c3aed 100%);
  border-color: #a855f7;
}

[data-theme='dark'] .waitingMessage p {
  color: #c4b5fd;
}

[data-theme='dark'] .completedMessage {
  background: linear-gradient(135deg, #14532d 0%, #166534 100%);
  border-color: #22c55e;
}

[data-theme='dark'] .completedMessage p {
  color: #86efac;
}
