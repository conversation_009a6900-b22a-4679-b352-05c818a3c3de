.notificationPanel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 380px;
  max-width: 90vw;
  background-color: #ffffff;
  border: 1px solid #d1dfec;
  border-top: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  font-family: 'Lato', sans-serif;
  border-radius: 0px 0px 12px 12px;
  max-height: 500px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.header h3 {
  color: #0e2f51;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.markAllReadBtn {
  background: none;
  border: none;
  color: #6366f1;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.markAllReadBtn:hover:not(:disabled) {
  background-color: #e0e7ff;
}

.markAllReadBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.closeButton {
  height: 20px;
  width: 20px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.closeButton:hover {
  background-color: #f3f4f6;
}

.closeButton img {
  height: 100%;
  width: 100%;
}

.notificationListContainer {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.dateGroup {
  margin-bottom: 16px;
}

.dateGroupHeader {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  padding: 8px 20px 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.notificationItem {
  display: flex;
  align-items: flex-start;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
  border-left: 3px solid transparent;
}

.notificationItem:hover {
  background-color: #f9fafb;
}

.notificationItem.unread {
  background-color: #fef3c7;
  border-left-color: #f59e0b;
}

.notificationItem.actionRequired {
  border-left-color: #ef4444;
}

.notificationIcon {
  font-size: 16px;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.notificationContent {
  flex: 1;
  min-width: 0;
}

.notificationMessage {
  margin: 0 0 4px 0;
  color: #0e2f51;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
}

.notificationMeta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.notificationDate {
  font-size: 12px;
  color: #6b7280;
}

.priorityBadge {
  font-size: 10px;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.actionBadge {
  font-size: 10px;
  background-color: #ef4444;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.unreadDot {
  width: 8px;
  height: 8px;
  background-color: #3b82f6;
  border-radius: 50%;
  margin-left: 8px;
  margin-top: 6px;
  flex-shrink: 0;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.emptyState p {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

.emptyState span {
  font-size: 14px;
  color: #6b7280;
}

/* Loading spinner for notifications */
.loadingSpinner {
  font-size: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Loading state for notification items */
.notificationItem.loading {
  pointer-events: none;
  opacity: 0.7;
}

.notificationItem.loading .loadingSpinner {
  color: #3b82f6;
}

/* Error banner styles */
.errorBanner {
  background-color: #fef2f2;
  border-left: 4px solid #ef4444;
  padding: 8px 16px;
  margin: 0 4px;
  border-radius: 4px;
}

.errorText {
  color: #dc2626;
  font-size: 12px;
  font-weight: 500;
}
