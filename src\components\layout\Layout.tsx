import React, { useState } from 'react';
import Header from '../../components/common/header/Header';
import Sidebar from '../../components/specific/sidebar/Sidebar';
import style from './Layout.module.css';
import { Outlet, useLocation } from 'react-router-dom';

const Layout: React.FC = () => {
  const location = useLocation();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Determine if current route is chat page
  const isChatPage = location.pathname.includes('/chat');
  const isHomePage = location.pathname === '/';

  const handleSidebarCollapse = (collapsed: boolean) => {
    setIsSidebarCollapsed(collapsed);
  };

  return (
    <div className={style.layoutContainer}>
      <div className={style.headerContainer}>
        <Header />
      </div>
      <div className={style.sidebarContainer}>
        <div className={style.sidebar}>
          <Sidebar onCollapse={handleSidebarCollapse} />
        </div>
        <div
          id="childrenContainer"
          className={`${style.childrenContainer} ${!isChatPage ? style.scrollableChildren : ''} ${
            isSidebarCollapsed ? style.contentCollapsed : ''
          }`}
          style={isHomePage ? { position: 'relative' } : {}}
        >
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default Layout;
