import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Node, Edge } from 'reactflow';

interface SketchbookState {
  pages: any[];
  activePage: string | number;
  pageSize: any;
  charts: { [key: string]: any[] };
  layouts: { [key: string]: any[] };
  pageEnabled: boolean;
  flowNodes: Node[];
  flowEdges: Edge[];
  activePageIndex: number;
}

const initialState: SketchbookState = {
  pages: [],
  activePage: '',
  pageSize: {
    value: 'a4',
    label: 'A4',
    width: 559,
    height: 793,
    orientation: 'portrait',
  },
  charts: {},
  layouts: {},
  pageEnabled: true,
  flowNodes: [],
  flowEdges: [],
  activePageIndex: 0,
};

const sketchbookSlice = createSlice({
  name: 'sketchbook',
  initialState,
  reducers: {
    updateCharts: (
      state,
      action: PayloadAction<{
        pageId: number | string;
        charts: any[];
      }>
    ) => {
      const { pageId, charts } = action.payload;
      state.charts[pageId] = charts;
    },
    updateChart: (
      state,
      action: PayloadAction<{
        pageId: number | string;
        chartId: string;
        updatedChart: any;
      }>
    ) => {
      const { pageId, chartId, updatedChart } = action.payload;
      if (!state.charts[pageId]) {
        state.charts[pageId] = [];
      }
      const chartIndex = state.charts[pageId].findIndex(
        (chart) => chart.id === chartId
      );
      if (chartIndex !== -1) {
        state.charts[pageId][chartIndex] = updatedChart;
      }
    },
    updateLayout: (
      state,
      action: PayloadAction<{
        pageId: number | string;
        layout: any[];
      }>
    ) => {
      const { pageId, layout } = action.payload;
      state.layouts[pageId] = layout;
    },
    setPages: (state, action: PayloadAction<any[]>) => {
      state.pages = action.payload;
    },
    setActivePage: (state, action: PayloadAction<string>) => {
      state.activePage = action.payload;
    },
    addPage: (state, action: PayloadAction<{ id: string; name: string }>) => {
      state.pages.push(action.payload);
      state.activePage = action.payload.id;
      state.charts[action.payload.id] = [];
      state.layouts[action.payload.id] = [];
      state.pageEnabled = true;
    },
    removePage: (state, action: PayloadAction<string>) => {
      const pageId = action.payload;
      state.pages = state.pages.filter((page) => page.id !== pageId);
      delete state.charts[pageId];
      delete state.layouts[pageId];
      // delete state.flowNodes[pageId];
      // delete state.flowEdges[pageId];
      if (state.activePage === pageId) {
        state.activePage = state.pages[0]?.id || '';
      }
    },
    setPageSize: (state, action: PayloadAction<any>) => {
      const newPageSize = action.payload;
      // Ensure the page size has all required properties
      if (newPageSize && typeof newPageSize === 'object') {
        state.pageSize = {
          value: newPageSize.value || state.pageSize.value,
          label: newPageSize.label || state.pageSize.label,
          width: newPageSize.width || state.pageSize.width,
          height: newPageSize.height || state.pageSize.height,
          orientation: newPageSize.orientation || state.pageSize.orientation,
        };
      }
    },
    togglePageEnabled: (state, action: PayloadAction<boolean>) => {
      const pageEnabled = action.payload;
      state.pageEnabled = pageEnabled;
    },
    updateFlowNodes: (state, action: PayloadAction<Node[]>) => {
      state.flowNodes = action.payload;
    },
    updateFlowEdges: (state, action: PayloadAction<Edge[]>) => {
      state.flowEdges = action.payload;
    },
    resetSketchbook: (state) => {
      // Reset the state to initial values
      state.pages = [];
      state.activePage = '';
      state.charts = {};
      state.layouts = {};
      state.flowNodes = [];
      state.flowEdges = [];
      // We don't reset pageSize or pageEnabled as they will be set from the backend data
      // or kept as user preferences
    },
  },
});

export const {
  updateCharts,
  updateChart,
  updateLayout,
  setPages,
  setActivePage,
  addPage,
  removePage,
  setPageSize,
  togglePageEnabled,
  updateFlowNodes,
  updateFlowEdges,
  resetSketchbook,
} = sketchbookSlice.actions;

export default sketchbookSlice.reducer;
