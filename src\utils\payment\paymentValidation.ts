import { PaymentFormData, PaymentPlan } from '../../types/payment';
import { PAYMENT_VALIDATION } from '../../services/constants/paymentServiceConstants';

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

/**
 * Validate payment form data
 */
export const validatePaymentForm = (formData: PaymentFormData): ValidationResult => {
  const errors: Record<string, string> = {};

  // Validate plan ID
  if (!formData.planId || formData.planId.trim() === '') {
    errors.planId = 'Please select a payment plan';
  }

  // Validate customer email
  if (formData.customerEmail) {
    if (!PAYMENT_VALIDATION.EMAIL_REGEX.test(formData.customerEmail)) {
      errors.customerEmail = 'Please enter a valid email address';
    }
  }

  // Validate customer name
  if (formData.customerName) {
    if (formData.customerName.trim().length < 2) {
      errors.customerName = 'Name must be at least 2 characters long';
    }
    if (formData.customerName.trim().length > 100) {
      errors.customerName = 'Name must be less than 100 characters';
    }
  }

  // Validate billing address if provided
  if (formData.billingAddress) {
    const { line1, city, state, postalCode, country } = formData.billingAddress;

    if (!line1 || line1.trim() === '') {
      errors.addressLine1 = 'Address line 1 is required';
    }

    if (!city || city.trim() === '') {
      errors.city = 'City is required';
    }

    if (!state || state.trim() === '') {
      errors.state = 'State is required';
    }

    if (!postalCode || postalCode.trim() === '') {
      errors.postalCode = 'Postal code is required';
    } else if (!PAYMENT_VALIDATION.POSTAL_CODE_REGEX.test(postalCode)) {
      errors.postalCode = 'Please enter a valid postal code';
    }

    if (!country || country.trim() === '') {
      errors.country = 'Country is required';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Validate payment plan
 */
export const validatePaymentPlan = (plan: PaymentPlan): ValidationResult => {
  const errors: Record<string, string> = {};

  if (!plan.id || plan.id.trim() === '') {
    errors.id = 'Plan ID is required';
  }

  if (!plan.name || plan.name.trim() === '') {
    errors.name = 'Plan name is required';
  }

  if (typeof plan.price !== 'number' || plan.price < 0) {
    errors.price = 'Plan price must be a valid positive number';
  }

  if (!PAYMENT_VALIDATION.MIN_AMOUNT || plan.price < PAYMENT_VALIDATION.MIN_AMOUNT) {
    errors.price = `Plan price must be at least $${PAYMENT_VALIDATION.MIN_AMOUNT}`;
  }

  if (plan.price > PAYMENT_VALIDATION.MAX_AMOUNT) {
    errors.price = `Plan price cannot exceed $${PAYMENT_VALIDATION.MAX_AMOUNT}`;
  }

  if (!plan.currency || plan.currency.trim() === '') {
    errors.currency = 'Plan currency is required';
  }

  if (!plan.interval || !['day', 'week', 'month', 'year'].includes(plan.interval)) {
    errors.interval = 'Plan interval must be day, week, month, or year';
  }

  if (!Array.isArray(plan.features) || plan.features.length === 0) {
    errors.features = 'Plan must have at least one feature';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): boolean => {
  return PAYMENT_VALIDATION.EMAIL_REGEX.test(email);
};

/**
 * Validate postal code format
 */
export const validatePostalCode = (postalCode: string): boolean => {
  return PAYMENT_VALIDATION.POSTAL_CODE_REGEX.test(postalCode);
};

/**
 * Validate amount
 */
export const validateAmount = (amount: number): ValidationResult => {
  const errors: Record<string, string> = {};

  if (typeof amount !== 'number' || isNaN(amount)) {
    errors.amount = 'Amount must be a valid number';
  } else if (amount < PAYMENT_VALIDATION.MIN_AMOUNT) {
    errors.amount = `Amount must be at least $${PAYMENT_VALIDATION.MIN_AMOUNT}`;
  } else if (amount > PAYMENT_VALIDATION.MAX_AMOUNT) {
    errors.amount = `Amount cannot exceed $${PAYMENT_VALIDATION.MAX_AMOUNT}`;
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Validate card details (basic client-side validation)
 */
export const validateCardDetails = (cardDetails: {
  number?: string;
  expiry?: string;
  cvc?: string;
  name?: string;
}): ValidationResult => {
  const errors: Record<string, string> = {};

  // Basic card number validation (Luhn algorithm would be better)
  if (cardDetails.number) {
    const cleanNumber = cardDetails.number.replace(/\s/g, '');
    if (!/^\d{13,19}$/.test(cleanNumber)) {
      errors.number = 'Please enter a valid card number';
    }
  }

  // Expiry validation
  if (cardDetails.expiry) {
    const expiryRegex = /^(0[1-9]|1[0-2])\/\d{2}$/;
    if (!expiryRegex.test(cardDetails.expiry)) {
      errors.expiry = 'Please enter a valid expiry date (MM/YY)';
    } else {
      const [month, year] = cardDetails.expiry.split('/');
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear() % 100;
      const currentMonth = currentDate.getMonth() + 1;
      
      const cardYear = parseInt(year, 10);
      const cardMonth = parseInt(month, 10);
      
      if (cardYear < currentYear || (cardYear === currentYear && cardMonth < currentMonth)) {
        errors.expiry = 'Card has expired';
      }
    }
  }

  // CVC validation
  if (cardDetails.cvc) {
    if (!/^\d{3,4}$/.test(cardDetails.cvc)) {
      errors.cvc = 'Please enter a valid CVC';
    }
  }

  // Cardholder name validation
  if (cardDetails.name) {
    if (cardDetails.name.trim().length < 2) {
      errors.name = 'Please enter the cardholder name';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Sanitize input data
 */
export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

/**
 * Validate required fields
 */
export const validateRequiredFields = (
  data: Record<string, any>,
  requiredFields: string[]
): ValidationResult => {
  const errors: Record<string, string> = {};

  requiredFields.forEach((field) => {
    if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
      errors[field] = `${field.charAt(0).toUpperCase() + field.slice(1)} is required`;
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Validate phone number (basic validation)
 */
export const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
};

/**
 * Validate country code
 */
export const validateCountryCode = (countryCode: string): boolean => {
  // Basic validation for 2-letter country codes
  return /^[A-Z]{2}$/.test(countryCode.toUpperCase());
};

/**
 * Validate currency code
 */
export const validateCurrencyCode = (currencyCode: string): boolean => {
  // Basic validation for 3-letter currency codes
  return /^[A-Z]{3}$/.test(currencyCode.toUpperCase());
};
