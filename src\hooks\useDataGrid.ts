import { useState } from 'react';
import { ViewMode } from '../components/common/viewToggle/ViewToggle';

interface UseDataGridProps<T> {
  data: any | undefined;
  isLoading: boolean;
  isError: boolean;
  searchField: keyof T;
  externalViewMode?: ViewMode;
  externalSetViewMode?: (mode: ViewMode) => void;
}

interface UseDataGridReturn<T> {
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
  searchTerm: string;
  handleSearch: (term: string) => void;
  filteredData: T[];
  isLoading: boolean;
  isError: boolean;
}

export function useDataGrid<T>({
  data,
  isLoading,
  isError,
  searchField,
  externalViewMode,
  externalSetViewMode,
}: UseDataGridProps<T>): UseDataGridReturn<T> {
  // Use external view mode state if provided, otherwise use internal state
  const [internalViewMode, setInternalViewMode] = useState<ViewMode>('grid');
  const viewMode =
    externalViewMode !== undefined ? externalViewMode : internalViewMode;
  const setViewMode = externalSetViewMode || setInternalViewMode;

  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const filteredData =
    data?.filter((item: T) => {
      const searchValue = (item[searchField] as unknown as string) || '';
      return searchValue.toLowerCase().includes(searchTerm.toLowerCase());
    }) || [];

  return {
    viewMode,
    setViewMode,
    searchTerm,
    handleSearch,
    filteredData,
    isLoading,
    isError,
  };
}
