.dropzoneContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 9999;
  padding: 20px;
}

.uploadContent {
  background: white;
  border-radius: 8px;
  padding: 24px;
  width: min(90vw, 420px);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.uploadIcon {
  font-size: 40px;
  color: #4299e1;
  margin-bottom: 8px;
}

.uploadPrompt {
  text-align: center;
  width: 100%;
}

.dragDropText {
  font-size: 16px;
  color: #2d3748;
  margin-bottom: 12px;
  font-weight: 500;
}

.orText {
  display: block;
  color: #718096;
  margin: 8px 0;
  font-size: 14px;
}

.uploadButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.uploadButton:hover {
  background: #3182ce;
  transform: translateY(-1px);
}

.buttonIcon {
  font-size: 20px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.uploadStatus {
  font-size: 14px;
  color: #2d3748;
  text-align: center;
  margin: 0;
  font-weight: 500;
}

/* Progress bar styles */
.progressContainer {
  position: fixed;
  top: 15vh;
  right: 24px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  width: min(90vw, 320px);
  z-index: 9999;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
  position: relative;
}

.progressFill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: #4299e1;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progressText {
  font-size: 14px;
  color: #2d3748;
  margin: 0;
  text-align: center;
  font-weight: 500;
}

@media (max-width: 480px) {
  .uploadContent {
    padding: 20px;
  }

  .progressContainer {
    top: 10vh;
    right: 16px;
    left: 16px;
    width: auto;
  }
}
