import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSaveWorkflowMutation } from '../services/workflowServices';
import {
  WorkflowFormData,
  WorkflowPerson,
  WorkflowPayload,
} from '../types/workflowForm';
import toast from 'react-hot-toast';

interface UseWorkflowFormProps {
  currentUser: any;
  projectTitle?: string;
  sketchbookId?: string;
  projectId?: string;
}

export const useWorkflowForm = ({
  currentUser,
  projectTitle,
  sketchbookId,
  projectId,
}: UseWorkflowFormProps) => {
  const navigate = useNavigate();
  const [saveWorkflow] = useSaveWorkflowMutation();

  const [formData, setFormData] = useState<WorkflowFormData>({
    creator: currentUser.name,
    date: '',
    projectName: projectTitle || 'untitled',
    priority: '',
    dueDate: '',
    subject: '',
    requiredActions: '',
  });

  const [notes, setNotes] = useState<string[]>([]);
  const [people, setPeople] = useState<WorkflowPerson[]>([]);
  const [validationErrors, setValidationErrors] = useState<Set<string>>(
    new Set()
  );

  useEffect(() => {
    // Set default dates
    const today = new Date().toISOString().split('T')[0];
    const dueDateDefault = new Date();
    dueDateDefault.setDate(dueDateDefault.getDate() + 2);

    setFormData((prev: any) => ({
      ...prev,
      date: today,
      dueDate: dueDateDefault.toISOString().split('T')[0],
      subject: 'New Workflow Request',
    }));
  }, []);

  const handleInputChange =
    (field: keyof WorkflowFormData) => (value: string) => {
      setFormData((prev: any) => ({ ...prev, [field]: value }));
    };

  const handleAddNote = (note: string) => {
    setNotes((prev) => [...prev, note]);
  };

  const handleDeleteNote = (index: number) => {
    setNotes((prev) => prev.filter((_, i) => i !== index));
  };

  const handleAddPerson = (person: WorkflowPerson) => {
    setPeople((prev) => [...prev, person]);
  };

  const handleUpdatePerson = (updatedPerson: WorkflowPerson) => {
    setPeople((prev) =>
      prev.map((p) => (p.id === updatedPerson.id ? updatedPerson : p))
    );
  };

  const handleDeletePerson = (id: string | number) => {
    setPeople((prev) => prev.filter((person) => person.id !== id));
  };

  const createPayload = (): WorkflowPayload => {
    const payload = {
      title: projectTitle || '',
      sketchbookId: sketchbookId || '',
      projectId: projectId || '',
      creatorId: currentUser.id,
      createdDate: new Date().toISOString().split('T')[0],
      dueDate: formData.dueDate,
      notes: notes,
      participants: people.map((person) => ({
        userId: person.id,
        note: person.note || '',
        approval_message: '',
        rejection_message: '',
        conditional_approved_message: '',
        status: '',
      })),
      priority: formData.priority,
      projectName: formData.projectName,
      requiredActions: formData.requiredActions,
      subject: formData.subject,
      status: 'pending',
      fullyApproved: false,
      currentUserId: people[0]?.id || '',
      approvalStatus: 'in-progress',
    };

    if (
      !payload.title ||
      !payload.sketchbookId ||
      !payload.creatorId ||
      !payload.participants.length
    ) {
      throw new Error('Missing required fields');
    }

    return payload;
  };

  const validateForm = () => {
    const errors = new Set<string>();
    if (!formData.priority) errors.add('priority');
    if (!formData.subject) errors.add('subject');
    if (people.length === 0) errors.add('people');
    setValidationErrors(errors);
    return errors.size === 0;
  };

  const handleCreateWorkflow = async () => {
    if (!validateForm()) {
      toast.error(
        'Please fill in all required fields and add at least one participant'
      );
      return;
    }

    try {
      const payload = createPayload();
      await saveWorkflow({ payload }).unwrap();
      toast.success('Workflow created successfully');
      navigate('/awaiting-actions');
    } catch (error: any) {
      console.error('Error creating workflow:', error);
      toast.error(
        error.data?.message ||
          'Error creating workflow. Please check all fields.'
      );
    }
  };

  const handleSaveAsDraft = async () => {
    if (!validateForm()) {
      toast.error(
        'Please fill in all required fields and add at least one participant'
      );
      return;
    }

    try {
      const payload = createPayload();
      payload.status = 'draft';
      await saveWorkflow({ payload }).unwrap();
      toast.success('Workflow saved as draft successfully');
      navigate('/drafts');
    } catch (error) {
      console.error('Error saving workflow as draft:', error);
      toast.error('Error saving workflow as draft');
    }
  };

  return {
    formData,
    notes,
    people,
    validationErrors,
    handleInputChange,
    handleAddNote,
    handleDeleteNote,
    handleAddPerson,
    handleUpdatePerson,
    handleDeletePerson,
    handleCreateWorkflow,
    handleSaveAsDraft,
  };
};

export default useWorkflowForm;
