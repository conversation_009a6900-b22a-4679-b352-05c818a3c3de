.helpButton {
  transition: all 0.3s ease;
  position: relative;
}

.helpButton:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Dialog styling */
.dialog {
  border-radius: 12px;
}

.dialogPaper {
  background-color: var(--color-background-modal) !important;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

/* Dialog title */
.dialogTitle {
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
  padding: 16px 24px;
}

.titleContent {
  display: flex;
  align-items: center;
  gap: 12px;
}

.titleIcon {
  font-size: 24px;
  color: white;
}

.closeButton {
  position: absolute !important;
  right: 12px !important;
  top: 12px !important;
  color: white !important;
}

/* Dialog content */
.dialogContent {
  background-color: var(--color-background-primary) !important;
  padding: 24px !important;
}

.dialogDescription {
  margin-bottom: 24px !important;
  color: var(--color-text-secondary);
  line-height: 1.6 !important;
}

/* Tour list */
.tourList {
  border-color: red !important;
  padding: 0 !important;
}

.tourListItem {
  margin-bottom: 12px !important;
}

.tourItem {
  border-radius: 8px !important;
  padding: 12px 16px !important;
  transition: all 0.2s ease !important;
  border: 1px solid var(--color-border) !important;
}

.tourItem:hover {
  /* background-color: var(--color-background-secondary) !important; */
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.seenTour {
  background-color: var(--color-background-secondary);
  border-color: var(--color-text-primary) !important;
}

.unseenTour {
  background-color: rgba(var(--color-primary-rgb), 0.05);
  border-color: var(--color-text-primary) !important;
}

.tourIcon {
  min-width: 40px !important;
}

.tourTitle {
  color: var(--color-text-primary) !important;
  font-weight: 600 !important;
  font-size: 16px !important;
}

.tourSubtitle {
  font-size: 13px !important;
  color: var(--color-text-secondary) !important;
}

.completedIcon {
  font-size: 20px !important;
}

.newBadge {
  background-color: red;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 3px 6px;
  border-radius: 4px;
  letter-spacing: 0.5px;
}

/* Dialog actions */
.dialogActions {
  padding: 16px 24px !important;
  display: flex !important;
  justify-content: space-between !important;
  background-color: var(--color-background-secondary);
}

.resetButton {
  text-transform: none !important;
}

.closeDialogButton {
  text-transform: none !important;
  min-width: 100px !important;
}

.customtoast {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 400px;
  padding: 12px 16px;
  background-color: #fff3cd; /* soft yellow */
  color: #856404; /* dark yellow text */
  border: 1px solid #ffeeba;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  font-family: sans-serif;
}

.customtoastmessage {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.customtoastsubtext {
  font-size: 14px;
  opacity: 0.9;
}

.customtoastbutton {
  background-color: #ffe08a;
  border: none;
  padding: 6px 12px;
  font-size: 14px;
  color: #856404;
  font-weight: bold;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 12px;
}

.customtoastbutton:hover {
  background-color: #ffdd75;
}
