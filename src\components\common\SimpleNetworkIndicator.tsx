import React, { useState, useEffect } from 'react';
import { Tooltip, IconButton } from '@mui/material';
import WifiIcon from '@mui/icons-material/Wifi';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import SignalCellular1BarIcon from '@mui/icons-material/SignalCellular1Bar';
import { notifyNetworkStatusChange, isSlowConnection, notifySlowConnection } from '../../utils/networkUtils';

const SimpleNetworkIndicator: React.FC = () => {
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const [isSlow, setIsSlow] = useState<boolean>(false);

  // Check for slow connection
  useEffect(() => {
    const checkConnectionSpeed = () => {
      const slow = isSlowConnection();
      if (slow && !isSlow) {
        setIsSlow(true);
        notifySlowConnection();
      } else if (!slow && isSlow) {
        setIsSlow(false);
      }
    };

    // Check initially
    checkConnectionSpeed();

    // Set up interval to check periodically
    const intervalId = setInterval(checkConnectionSpeed, 10000); // Check every 10 seconds

    return () => clearInterval(intervalId);
  }, [isSlow]);

  // Handle online/offline events
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      notifyNetworkStatusChange(true);
    };

    const handleOffline = () => {
      setIsOnline(false);
      notifyNetworkStatusChange(false);
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Clean up
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Determine which icon to show
  const getIcon = () => {
    if (!isOnline) {
      return <WifiOffIcon color="error" />;
    }
    
    if (isSlow) {
      return <SignalCellular1BarIcon color="warning" />;
    }
    
    return <WifiIcon color="success" />;
  };

  // Determine tooltip text
  const getTooltipText = () => {
    if (!isOnline) {
      return 'You are offline';
    }
    
    if (isSlow) {
      return 'Slow internet connection';
    }
    
    return 'Connected';
  };

  return (
    <Tooltip title={getTooltipText()}>
      <IconButton size="small">
        {getIcon()}
      </IconButton>
    </Tooltip>
  );
};

export default SimpleNetworkIndicator;
