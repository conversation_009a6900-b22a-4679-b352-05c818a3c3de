import React from 'react';
import ToastViewer from './ToastViewer';

interface TextAreaProps {
  data: {
    markdown?: string;
    text?: string;
    style?: React.CSSProperties;
  };
}

const TextArea: React.FC<TextAreaProps> = ({ data }) => {
  const defaultStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    padding: '8px',
    overflow: 'auto',
    fontSize: '14px',
    fontFamily: 'var(--font-family-primary)',
    color: '#000', // Always black text
    backgroundColor: 'white', // Always white background
    textAlign: 'left',
    fontWeight: 'normal',
    fontStyle: 'normal',
    textDecoration: 'none',
  };

  const content = data.markdown || data.text || '';

  return (
    <div style={{ ...defaultStyle, ...(data.style || {}) }}>
      <ToastViewer
        content={content}
        style={data.style}
        theme="light" // Always use light theme
      />
    </div>
  );
};

export default TextArea;
