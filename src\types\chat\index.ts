export interface ChartSelection {
  id: string;
  project_id: string;
}

export interface ImageSelection {
  id: string;
  project_id: string;
}

export interface TextSelection {
  id: string;
  project_id: string;
}

export interface ChatMessage {
  id?: string | number;
  sender: 'user' | 'bot';
  content: string;
  type: 'light' | 'dark' | 'hint' | 'chartImage';
  image?: string;
  timestamp: string;
  responseId?: string;
  handleOnClick?: () => void;
  isSuggestedQuestions?: boolean;
  icon?: string;
  noOfPages?: number;
}
