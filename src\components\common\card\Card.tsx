import React, { useEffect, useState, useRef } from 'react'; // Added useEffect, useState, and useRef
import styles from './Card.module.css';
import { CiEdit, CiStar } from 'react-icons/ci';
import { SlOptionsVertical } from 'react-icons/sl';
import { Tooltip } from '@mui/material';
import { MdContentCopy, MdDelete } from 'react-icons/md';

interface User {
  name: string;
  avatar: string;
}

interface CardProps {
  showStatusAsDot?: string;
  currentUser?: boolean;
  role: 'creator' | 'approver';
  canEdit?: boolean;
  initials: string;
  title: string;
  users: User[];
  timeAgo: string;
  editorImage?: string;
  editorName?: string;
  status:
    | 'Draft'
    | 'Published'
    | 'Completed'
    | 'In Progress'
    | 'Awaiting'
    | 'Rejected';
  handleCardClick?: any;
  handleViewClick?: any;
  handleEditClick?: any;
  handleApproveClick?: any;
  handleConditionalClick?: any;
  handleRejectClick?: any;
  fromProject?: boolean;
  onDelete?: any;
  onDuplicate?: () => void;
  type?: 'library' | 'project' | 'workflow' | 'default';
}

const Card: React.FC<CardProps> = ({
  showStatusAsDot,
  currentUser,
  title,
  users,
  timeAgo,
  status,
  handleCardClick,
  fromProject = false,
  onDelete,
  onDuplicate,
  type = 'default',
}) => {
  const [bgColor, setBgColor] = useState<string>('');
  const [textColor, setTextColor] = useState<string>('');
  const [showOptions, setShowOptions] = useState(false);
  const optionsRef = useRef<HTMLDivElement>(null);

  const getRandomColor = (opacity: number) => {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    const r = parseInt(color.slice(1, 3), 16);
    const g = parseInt(color.slice(3, 5), 16);
    const b = parseInt(color.slice(5, 7), 16);
    return {
      rgba: `rgba(${r}, ${g}, ${b}, ${opacity})`,
      rgb: `rgb(${r}, ${g}, ${b})`,
    };
  };

  useEffect(() => {
    const { rgba, rgb } = getRandomColor(0.2);
    setBgColor(rgba);
    setTextColor(rgb);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        optionsRef.current &&
        !optionsRef.current.contains(event.target as Node)
      ) {
        setShowOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const getInitials = (title: string) => {
    const words = title.split(' ');
    const firstChar = words[0].charAt(0).toUpperCase();
    const secondChar = words.length > 1 ? words[1].charAt(0).toUpperCase() : '';
    return firstChar + secondChar;
  };

  const handleOptionsClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    setShowOptions(!showOptions);
  };

  const handleOptionSelect = (action: 'delete' | 'duplicate') => {
    if (action === 'delete') {
      onDelete && onDelete();
    } else {
      onDuplicate?.();
    }
    setShowOptions(false);
  };

  const getStatusDotColor = (status: string): string => {
    switch (status) {
      case 'approved':
        return 'var(--color-success-main)'; // green
      case 'in-progress':
        return 'var(--color-info-main)'; // blue
      case 'rejected':
        return 'var(--color-error-main)'; // red
      case 'conditionally-approved':
        return 'var(--color-warning-main)'; // orange
      default:
        return 'var(--color-grey-500)'; // grey for unknown status
    }
  };

  // Get type-specific CSS class
  const getCardClassName = () => {
    const baseClass = styles.card;
    switch (type) {
      case 'library':
        return `${baseClass} ${styles.libraryCard || ''}`;
      case 'project':
        return `${baseClass} ${styles.projectCard || ''}`;
      case 'workflow':
        return `${baseClass} ${styles.workflowCard || ''}`;
      default:
        return baseClass;
    }
  };

  return (
    <div onClick={handleCardClick} className={getCardClassName()}>
      <div className={styles.header}>
        <div className={styles.initialsWrapper}>
          <div
            className={styles.initials}
            style={{ backgroundColor: bgColor, color: textColor }}
          >
            {getInitials(title)}
          </div>
        </div>

        {!fromProject ? (
          <div className={styles.users}>
            <div className={styles.avatarStack}>
              {users.slice(0, 3).map((user, index) => (
                <div key={index} className={styles.avatarWrapper}>
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className={styles.userAvatar}
                    style={{ transform: `translateX(${index * -8}px)` }}
                  />
                  <div className={styles.avatarHover}>{user.name}</div>
                </div>
              ))}
              {users.length > 3 && (
                <div className={styles.extraUsers}>+{users.length - 3}</div>
              )}
            </div>
          </div>
        ) : (
          <div className={styles.optionsWrapper} ref={optionsRef}>
            <div
              className={styles.optionsButton}
              onClick={(e) => handleOptionsClick(e)}
              title="More options"
            >
              <SlOptionsVertical className={styles.optionsIcon} />
            </div>
            {showOptions && (
              <div className={styles.optionsMenu}>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOptionSelect('duplicate');
                  }}
                  className={styles.optionItem}
                >
                  <MdContentCopy className={styles.optionIcon} />
                  <span>Make a copy</span>
                </button>
                <div className={styles.optionsDivider} />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOptionSelect('delete');
                  }}
                  className={`${styles.optionItem} ${styles.deleteOption}`}
                >
                  <MdDelete className={styles.optionIcon} />
                  <span>Delete</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      <div className={styles.content}>
        <h3 className={styles.title}>{title}</h3>

        <div className={styles.footer}>
          <div className={styles.timeInfo}>
            <div className={styles.timeInfoWrapper}>
              <CiEdit className={styles.editIcon} />
              <span>
                {type === 'library'
                  ? 'Available for analysis'
                  : status === 'Published' && 'Edited'}{' '}
                {timeAgo}
              </span>
            </div>
            {status === 'Published' && type !== 'library' && (
              <button
                className={styles.favButton}
                onClick={(e) => {
                  e.stopPropagation();
                  // Add your favorite logic here
                }}
              >
                <CiStar className={styles.starIcon} />
              </button>
            )}
          </div>

          {status === 'Awaiting' && currentUser && type !== 'library' && (
            <div className={styles.creatorBadge}>
              <p>Created by You</p>
              <Tooltip title={showStatusAsDot}>
                <span
                  className={styles.statusDot}
                  style={{
                    backgroundColor: showStatusAsDot
                      ? getStatusDotColor(showStatusAsDot)
                      : undefined,
                  }}
                ></span>
              </Tooltip>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Card;
