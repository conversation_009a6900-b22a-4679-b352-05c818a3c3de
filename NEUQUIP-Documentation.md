# NEUQUIP Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Project Overview](#project-overview)
3. [System Architecture](#system-architecture)
4. [Key Features](#key-features)
   - [Chat System](#chat-system)
   - [Sketchbook](#sketchbook)
   - [Workflow Management](#workflow-management)
   - [User Management](#user-management)
5. [Technical Stack](#technical-stack)
6. [Project Structure](#project-structure)
7. [Component Documentation](#component-documentation)
8. [State Management](#state-management)
9. [API Services](#api-services)
10. [Hooks](#hooks)
11. [Theming and Styling](#theming-and-styling)
12. [Installation and Setup](#installation-and-setup)
13. [Available Scripts](#available-scripts)
14. [Deployment](#deployment)
15. [Best Practices](#best-practices)
16. [Troubleshooting](#troubleshooting)

## Introduction

NEUQUIP is a modern React TypeScript application that combines AI-powered document analysis, interactive data visualization, and collaborative workflow management. Developed by <PERSON><PERSON><PERSON>, it provides a comprehensive platform for users to analyze documents, create visual presentations, and manage approval workflows.

## Project Overview

NEUQUIP is built with React 18 and TypeScript, using Vite as the build tool. It features a modular architecture with separate components for chat, sketchbook, and workflow management. The application uses Redux Toolkit for state management, React Router for navigation, and Material-UI (MUI) for UI components.

The application is designed to be responsive and supports both light and dark themes. It includes a rich set of visualization tools, including various chart types, flow diagrams, and data tables.

## System Architecture

NEUQUIP follows a modern frontend architecture with the following key components:

- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **State Management**: Redux Toolkit
- **Routing**: React Router
- **UI Components**: Material-UI (MUI)
- **API Communication**: RTK Query and Axios
- **Data Visualization**: Chart.js, ReactFlow, AG Grid
- **Rich Text Editing**: Toast UI Editor, Lexical framework

The application communicates with a backend API for data storage and retrieval. The API endpoints are defined in the services directory.

## Key Features

### Chat System

The chat system allows users to interact with an AI assistant that can analyze documents, extract data, and generate visualizations. Users can upload files, ask questions, and receive contextual responses with charts and data tables.

**Key Components:**

- `ChatPage`: Main chat interface
- `ChatInput`: Input component for messages and file uploads
- `MessageList`: Displays chat messages and responses
- `ChatHeader`: Controls for chat settings and sketchbook integration

**Features:**

- Real-time messaging with AI-powered responses
- Document analysis with automatic chart and data extraction
- File upload supporting multiple formats (PDF, DOCX, CSV, TXT, etc.)
- Suggested questions and contextual responses
- Ability to save insights to sketchbooks

### Sketchbook

The sketchbook provides a canvas for creating visual presentations with charts, text, and images. It supports both page mode (for document-style layouts) and flow mode (for connected diagrams).

**Key Components:**

- `SketchBookPage`: Main sketchbook interface
- `SketchbooksHome`: Dashboard for managing sketchbooks
- `DropableEditor`: Canvas for placing and arranging elements
- `ChartPropertyController`: Controls for customizing charts and elements
- `SketchBookController`: Sidebar with available elements and tools

**Features:**

- Interactive canvas for data visualization and presentation
- Multiple page support with customizable page sizes (A4, A5, Letter, Legal)
- Rich visualization options (bar, line, pie, doughnut, radar, polar area charts, etc.)
- Drag-and-drop interface with resizable elements
- Template system for quick starts
- Export and sharing capabilities
- Markdown and rich text editing with Toast UI Editor
- Dark theme support for all chart types

### Workflow Management

The workflow system enables users to create approval processes for documents and sketchbooks, assign team members, and track approval status.

**Key Components:**

- `WorkFlow`: Main workflow creation interface
- `WorkflowTeam`: Team member assignment and management
- `WorkflowNotes`: Notes and comments for collaboration

**Features:**

- Create approval workflows for documents and sketchbooks
- Assign team members with specific roles
- Set priorities, due dates, and required actions
- Track approval status (approved, rejected, conditionally approved)
- Add notes and comments for collaboration
- Email notifications and status tracking

### User Management

The user management system allows users to create and manage profiles, set roles, and organize projects.

**Key Components:**

- `LoginPage`: User authentication
- `RegistrationPage`: User registration
- `EditProfile`: Profile management
- `AdminPage`: Admin dashboard for user management

**Features:**

- User profiles with customizable images
- Role-based access control
- Project organization and management
- Awaiting actions dashboard

## Technical Stack

### Frontend

- React 18 with TypeScript
- Vite for fast development and building
- Redux Toolkit for state management
- React Router for navigation
- Material-UI (MUI) for component library
- React Hook Form with Zod for form validation

### Data Visualization

- Chart.js with react-chartjs-2
- ReactFlow for flow diagrams
- AG Grid for data tables
- Gantt charts with gantt-task-react
- React Grid Layout for responsive layouts

### Rich Text Editing

- Toast UI Editor for markdown editing
- Lexical framework integration
- React Markdown for rendering

### API Communication

- RTK Query for data fetching and caching
- Axios for HTTP requests

### Utilities

- React Dropzone for file uploads
- React Color and React Colorful for color picking
- React Datepicker for date selection
- PDF handling with jspdf and pdf-lib
- File export with html2canvas and file-saver

## Project Structure

The project follows a modular structure with separate directories for components, pages, services, hooks, and utilities:

```
src/
├── assets/            # Static assets (images, styles, loaders)
├── components/        # React components
│   ├── chat/          # Chat-related components
│   ├── common/        # Reusable UI components (buttons, inputs, etc.)
│   ├── layout/        # Layout components (headers, navigation)
│   ├── specific/      # Feature-specific components
│   ├── templates/     # Template components
│   └── workflow/      # Workflow-related components
├── constants/         # Application constants
├── contexts/          # React contexts (Dialog, Theme)
├── hooks/             # Custom React hooks
│   ├── chat/          # Chat-related hooks
│   ├── sketchbook/    # Sketchbook-related hooks
│   └── workflow/      # Workflow-related hooks
├── mocks/             # Mock data for development
├── pages/             # Page components
│   ├── admin/         # Admin dashboard
│   ├── awaitedactions/# Awaited actions pages
│   ├── chatpage/      # Chat interface
│   ├── homeFinal/     # Home page
│   ├── loginpage/     # Authentication
│   ├── sketchbookpage/# Sketchbook interface
│   └── workflow/      # Workflow management
├── services/          # API services
│   └── constants/     # API endpoint constants
├── store/             # Redux store
│   ├── selectors/     # Redux selectors
│   └── slices/        # Redux slices
├── styles/            # Global styles
├── theme/             # Theme configuration
├── types/             # TypeScript type definitions
└── utils/             # Utility functions
```

## Component Documentation

### Core Components

#### App.tsx

The main application component that sets up routing, authentication, and theme providers. It uses React Router for navigation and lazy loading for performance optimization.

#### Layout.tsx

The main layout component that includes the header, sidebar, and content area. It provides a consistent layout for all pages.

### Chat Components

#### ChatPage.tsx

The main chat interface that integrates all chat-related components. It uses the `useChat` hook for managing chat state and interactions.

#### ChatHeader.tsx

Controls for chat settings, including saving to sketchbook, changing models, and managing page ranges.

#### ChatInput.tsx

Input component for messages and file uploads. It supports text input, file attachments, and suggested questions.

#### MessageList.tsx

Displays chat messages and responses, including text, charts, and images.

### Sketchbook Components

#### SketchBookPage.tsx

The main sketchbook interface that integrates all sketchbook-related components. It uses the `useSketchbook` hook for managing sketchbook state.

#### DropableEditor.tsx

Canvas for placing and arranging elements. It supports drag-and-drop, resizing, and customization of elements.

#### ChartPropertyController.tsx

Controls for customizing charts and elements, including colors, labels, and data.

#### SketchBookController.tsx

Sidebar with available elements and tools for adding to the sketchbook.

### Workflow Components

#### WorkFlow.tsx

The main workflow creation interface. It uses the `useWorkflowForm` hook for managing workflow state.

#### WorkflowTeam.tsx

Team member assignment and management component. It allows adding, removing, and configuring team members.

#### WorkflowNotes.tsx

Notes and comments component for collaboration. It allows adding and removing notes.

## State Management

NEUQUIP uses Redux Toolkit for state management. The store is organized into slices for different features:

### Store Structure

#### authSlice.ts

Manages authentication state, including user details and token.

#### sketchbookSlice.ts

Manages sketchbook state, including pages, layouts, charts, and flow elements.

#### chatSlice.ts

Manages chat state, including messages, projects, and file uploads.

#### workflowSlice.ts

Manages workflow state, including approvals, team members, and status.

### RTK Query Services

The application uses RTK Query for API communication. Services are defined in the `services` directory:

#### chatServices.ts

API services for chat-related functionality, including file uploads and model selection.

#### sketchbookServices.ts

API services for sketchbook-related functionality, including saving, updating, and retrieving sketchbooks.

#### workflowServices.ts

API services for workflow-related functionality, including creating and managing workflows.

## Hooks

NEUQUIP uses custom hooks for managing component logic and state. The hooks are organized by feature:

### Chat Hooks

#### useChat.ts

The main hook for managing chat functionality. It handles sending messages, processing responses, and managing chat state.

```typescript
export const useChat = ({
  userInLocalStorage,
  projectID,
  getFileName,
  getFileType,
  getProjectTitle,
  pageRangeChanges,
  newProfileImage,
  senderIcon,
  botIcon,
  onMongoProjectIdChange,
  projectData,
  handleOnQuestionClick,
}: UseChatProps) => {
  const [messages, setMessages] = useState<any[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [inputValue, setInputValue] = useState('');
  // ...
};
```

### Sketchbook Hooks

#### useSketchbook.ts

The main hook for managing sketchbook functionality. It handles loading sketchbook data, processing charts, and managing sketchbook state.

```typescript
export const useSketchbook = (sketchbookId: string) => {
  const { data, isLoading, error } = useGetSketchbookByIdQuery({
    sketchbookId,
  });

  // Process sketchbook data
  const projectTitle = data?.data?.sketchbook_name || 'Untitled';
  const projectId = data?.data?.project_id;
  // ...

  return {
    projectTitle,
    projectId,
    mongoIdOfInsightsForComplinces,
    sketchbookData: data?.data,
    initialChartsLoaded,
  };
};
```

### Workflow Hooks

#### useWorkflowForm.ts

The main hook for managing workflow form functionality. It handles form state, validation, and submission.

```typescript
export const useWorkflowForm = ({
  currentUser,
  projectTitle,
  sketchbookId,
  projectId,
}: WorkflowFormProps) => {
  const [formData, setFormData] = useState<WorkflowFormData>({
    projectName: projectTitle || '',
    subject: '',
    priority: 'Medium',
    dueDate: '',
    requiredActions: '',
  });

  // ...

  return {
    formData,
    notes,
    people,
    validationErrors,
    handleInputChange,
    handleAddNote,
    handleDeleteNote,
    handleAddPerson,
    handleUpdatePerson,
    handleDeletePerson,
    handleCreateWorkflow,
    handleSaveAsDraft,
  };
};
```

## API Services

NEUQUIP communicates with a backend API for data storage and retrieval. The API endpoints are defined in the `services/constants` directory.

### Chat API

```typescript
// Chat-related API endpoints
export const UPLOAD_FILE = '/api/v1/chat/upload-file';
export const UPDATE_TITLE = '/api/v1/chat/update-title';
export const SET_MODEL = '/api/v1/chat/set-model';
export const GET_CURRENT_MODEL = '/api/v1/chat/get-current-model';
```

### Sketchbook API

```typescript
// Sketchbook-related API endpoints
export const GET_SKETCHBOOK_BY_USER_ID = '/api/v1/sketchbook/get-by-userid/';
export const GET_SKETCHBOOK_BY_USER_ID_PAGINATED =
  '/api/v1/sketchbook/get-by-userid/';
export const GET_SKETCHBOOK_BY_ID = '/api/v1/sketchbook/get-by-id/';
export const SAVE_TO_SKETCHBOOK = '/api/v1/sketchbook/save';
export const UPDATE_SKETCHBOOK = '/api/v1/sketchbook/update/';
```

### Workflow API

```typescript
// Workflow-related API endpoints
export const CREATE_WORKFLOW = '/api/v1/workflow/create';
export const GET_WORKFLOW_BY_ID = '/api/v1/workflow/get-by-id/';
export const GET_WORKFLOW_BY_USER_ID = '/api/v1/workflow/get-by-userid/';
export const UPDATE_WORKFLOW_STATUS = '/api/v1/workflow/update-status/';
```

## Theming and Styling

NEUQUIP supports both light and dark themes with consistent styling across all components. The theme configuration is defined in the `theme` directory.

### Theme Context

The application uses a theme context for managing theme state:

```typescript
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [darkMode, setDarkMode] = useState<boolean>(() => {
    const savedMode = localStorage.getItem('darkMode');
    return savedMode ? JSON.parse(savedMode) : false;
  });

  const toggleDarkMode = () => {
    setDarkMode((prevMode) => {
      const newMode = !prevMode;
      localStorage.setItem('darkMode', JSON.stringify(newMode));
      return newMode;
    });
  };

  const theme = createTheme({
    palette: {
      mode: darkMode ? 'dark' : 'light',
      // ...
    },
  });

  return (
    <ThemeContext.Provider value={{ darkMode, toggleDarkMode }}>
      <MuiThemeProvider theme={theme}>{children}</MuiThemeProvider>
    </ThemeContext.Provider>
  );
};
```

### CSS Modules

The application uses CSS modules for component-specific styling:

```css
/* ChatPage.module.css */
.mainChatContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.chatContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}
```

## Installation and Setup

### Prerequisites

- Node.js (Latest LTS version)
- npm or yarn

### Setup

1. Clone the repository:

   ```bash
   git clone [your-repository-url]
   cd NEUQUIPFE
   ```

2. Install dependencies:

   ```bash
   npm install
   # or
   yarn install
   ```

3. Environment Setup:
   Create a `.env` file in the root directory with required configurations:

   ```
   VITE_API_BASE_URL=your_api_base_url
   VITE_AI_BASE_URL=your_ai_service_url
   ```

4. Start development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production (with increased memory allocation)
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

## Deployment

NEUQUIP can be deployed to various hosting platforms. Here are the steps for deploying to a production environment:

### Building for Production

1. Create a production build:

   ```bash
   npm run build
   # or
   yarn build
   ```

   This will create a `dist` directory with the production-ready files.

2. Test the production build locally:

   ```bash
   npm run preview
   # or
   yarn preview
   ```

### Deployment Options

#### Static Hosting (Netlify, Vercel, etc.)

1. Connect your repository to the hosting platform
2. Configure the build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
3. Configure environment variables in the hosting platform's dashboard

#### Docker Deployment

1. Create a Dockerfile:

   ```dockerfile
   FROM node:18-alpine as build
   WORKDIR /app
   COPY package*.json ./
   RUN npm install
   COPY . .
   RUN npm run build

   FROM nginx:alpine
   COPY --from=build /app/dist /usr/share/nginx/html
   COPY nginx.conf /etc/nginx/conf.d/default.conf
   EXPOSE 80
   CMD ["nginx", "-g", "daemon off;"]
   ```

2. Build and run the Docker image:

   ```bash
   docker build -t neuquip-frontend .
   docker run -p 80:80 neuquip-frontend
   ```

## Best Practices

### Code Style

- Follow the ESLint and Prettier configurations
- Use TypeScript for type safety
- Use functional components with hooks
- Keep components small and focused
- Use CSS modules for component-specific styling

### Performance Optimization

- Use React.memo for expensive components
- Use useMemo and useCallback for expensive calculations and callbacks
- Use lazy loading for routes and components
- Use virtualization for long lists
- Optimize images and assets

### State Management

- Use Redux for global state
- Use React context for theme and UI state
- Use local state for component-specific state
- Use RTK Query for API calls and caching

### Security

- Validate all user inputs
- Use HTTPS for all API calls
- Implement proper authentication and authorization
- Sanitize data before rendering
- Use environment variables for sensitive information

## Troubleshooting

### Common Issues

#### Build Errors

- **Memory Issues**: If you encounter memory issues during build, use the `--max-old-space-size` flag:

  ```bash
  node --max-old-space-size=8192 ./node_modules/vite/bin/vite.js build
  ```

- **Module Not Found**: Ensure all dependencies are installed and the import paths are correct.

#### Runtime Errors

- **API Connection Issues**: Check the API URL in the `.env` file and ensure the API server is running.
- **Authentication Issues**: Check if the token is valid and not expired.
- **Chart Rendering Issues**: Ensure the chart data is in the correct format and all required Chart.js components are registered.

### Debugging

- Use the browser's developer tools to debug JavaScript issues
- Use the React Developer Tools extension for debugging React components
- Use the Redux DevTools extension for debugging Redux state
- Use console.log for quick debugging (remember to remove before production)

## Conclusion

NEUQUIP is a comprehensive web application that combines AI-powered document analysis, interactive data visualization, and collaborative workflow management. It provides a modern UI with dark/light theme support, responsive design, and a rich set of visualization tools.

The application is built with React 18 and TypeScript, using Vite as the build tool. It features a modular architecture with separate components for chat, sketchbook, and workflow management. The application uses Redux Toolkit for state management, React Router for navigation, and Material-UI (MUI) for UI components.

This documentation provides an overview of the project structure, key features, and technical details. For more information, refer to the code comments and the README.md file.
