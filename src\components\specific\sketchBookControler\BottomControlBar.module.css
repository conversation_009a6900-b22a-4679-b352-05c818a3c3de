.bottomControlBar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--color-background-secondary);
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.06);
  padding: 4px 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1100;
  height: 31px;
  max-width: 100vw;
  box-sizing: border-box;
  border-top: 1px solid var(--color-border);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

/* Page Size Controls */
.pageSizeControls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  padding-right: 16px;
  margin-right: 8px;
  position: relative;
}

/* React-Select Custom Styles */
.pageSizeSelect {
  width: 90px;
}

/* Remove any global styles that might interfere */
.pageSizeSelect :global(.select__menu),
.pageSizeSelect :global(.select__menu-portal) {
  z-index: 99999 !important;
}

/* Ensure the select control is clickable */
.pageSizeSelect :global(.select__control) {
  cursor: pointer !important;
}

/* Add pointer events to ensure click events work */
.pageSizeSelect :global(.select__value-container),
.pageSizeSelect :global(.select__indicators) {
  pointer-events: auto !important;
}

/* Ensure menu items are clickable */
.pageSizeSelect :global(.select__option) {
  cursor: pointer !important;
}

/* Page Controls */
.pageControls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  position: relative;
  min-width: 0;
  margin-right: 82px;
}

.pagesContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow-x: auto;
  padding: 0 4px;
  position: relative;
  min-width: 0;
  width: 100%;
  mask-image: linear-gradient(to right, black 90%, transparent 100%);
  -webkit-mask-image: linear-gradient(to right, black 90%, transparent 100%);
  scrollbar-width: none;
  -ms-overflow-style: none;
  margin-right: 8px;
}

.pageItem {
  margin-left: 5px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 3px;
  cursor: pointer;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  white-space: nowrap;
  font-size: 11px;
  flex-shrink: 0;
  max-width: 84px;
  height: 20px;
  transition: all 0.2s ease;
  color: #000;
}

.pageItem span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pageItem:hover {
  background: var(--color-background-tertiary);
  border-color: var(--color-primary-light);
  color: var(--color-text-primary);
}

.pageItem.active {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
  font-weight: 500;
  color: var(--color-primary-contrast);
}

.addPageButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 2px 10px;
  border-radius: 3px;
  cursor: pointer;
  background: var(--color-background-secondary);
  border: 1px dashed var(--color-border);
  font-size: 11px;
  box-shadow: -4px 0 8px -2px var(--color-background-secondary);
  z-index: 2;
  width: 82px;
  height: 22px;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
  color: var(--color-text-secondary);
  white-space: nowrap;
}

.addPageButton:hover {
  background: #f8f9fa;
  border-color: #999;
  color: #333;
  box-shadow:
    -4px 0 8px -2px rgba(255, 255, 255, 1),
    0 1px 3px rgba(0, 0, 0, 0.05);
}

.addPageButton:active {
  background: #f0f0f0;
  border-color: #666;
  transform: translateY(-50%) scale(0.98);
}

.addPageButton svg {
  width: 11px;
  height: 11px;
  flex-shrink: 0;
}

.addPageButton span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1;
}

.removeButton {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  line-height: 1;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 2px;
  transition: all 0.2s ease;
  margin-left: 2px;
}

.removeButton:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.05);
}

.orientationButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  padding: 0;
  border: 1px solid var(--color-border);
  border-radius: 3px;
  background: var(--color-background-tertiary);
  cursor: pointer;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
  flex-shrink: 0;
  color: var(--color-text-secondary);
  margin-left: 8px;
  margin-right: 8px;
}

.orientationButton:hover {
  background-color: #f0f0f0;
  border-color: #ccc;
  color: #333;
}

.orientationButton svg {
  width: 12px;
  height: 12px;
}

/* Hide scrollbar but keep functionality */
.pagesContainer::-webkit-scrollbar {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .bottomControlBar {
    padding: 4px 12px;
    gap: 12px;
  }

  .pageSizeControls {
    width: 120px;
    padding-right: 12px;
  }

  .pageControls {
    margin-right: 76px;
  }

  .addPageButton {
    right: -76px;
    width: 76px;
  }
}

@media (max-width: 768px) {
  .bottomControlBar {
    padding: 4px 8px;
    gap: 8px;
    height: 40px;
  }

  .pageSizeControls {
    width: 112px;
    padding-right: 8px;
    margin-right: 4px;
  }

  .pageControls {
    margin-right: 64px;
    gap: 6px;
  }

  .pagesContainer {
    gap: 6px;
    padding: 0 3px;
    margin-right: 4px;
  }

  .addPageButton {
    /* right: -64px; */
    /* width: 64px; */
    background-color: red;
    padding: 2px 8px;
    height: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .addPageButton svg {
    width: 10px;
    height: 10px;
  }

  .addPageButton span {
    display: none;
  }

  .pageItem {
    max-width: 70px;
    padding: 2px 6px;
    height: 18px;
  }

  .orientationButton {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .bottomControlBar {
    height: 36px;
    padding: 3px 6px;
    gap: 6px;
  }

  .pageSizeControls {
    width: 100px;
    padding-right: 6px;
    margin-right: 3px;
    gap: 4px;
  }

  .pageSizeSelect {
    width: 80px;
  }

  .pageControls {
    margin-right: 40px;
    gap: 4px;
  }

  .addPageButton {
    right: -40px;
    width: 40px;
    height: 18px;
    padding: 2px 4px;
  }

  .pageItem {
    max-width: 60px;
    padding: 1px 5px;
    height: 16px;
    font-size: 10px;
    margin-left: 3px;
  }

  .orientationButton {
    width: 18px;
    height: 18px;
    margin-left: 4px;
    margin-right: 4px;
  }

  .orientationButton svg {
    width: 10px;
    height: 10px;
  }
}
