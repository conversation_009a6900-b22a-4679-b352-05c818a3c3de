import React from 'react';
import { Dialog, DialogContent, IconButton } from '@mui/material';
import { IoClose } from 'react-icons/io5';
import SketchbookTemplates from './SketchbookTemplates';
import styles from './SketchbookTemplatesModal.module.css';

interface SketchbookTemplatesModalProps {
  open: boolean;
  onClose: () => void;
}

const SketchbookTemplatesModal: React.FC<SketchbookTemplatesModalProps> = ({ 
  open, 
  onClose 
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      classes={{ paper: styles.dialogPaper }}
    >
      <div className={styles.closeButtonContainer}>
        <IconButton onClick={onClose} className={styles.closeButton}>
          <IoClose />
        </IconButton>
      </div>
      <DialogContent className={styles.dialogContent}>
        <SketchbookTemplates onClose={onClose} />
      </DialogContent>
    </Dialog>
  );
};

export default SketchbookTemplatesModal;
