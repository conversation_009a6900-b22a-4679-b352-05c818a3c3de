import { useState, useEffect } from 'react';
import { ViewMode } from '../components/common/viewToggle/ViewToggle';

// Define the types of UI preferences we want to store
export type TabType = 'templates' | 'recent';

interface UIPreferences {
  viewMode: ViewMode;
  activeTab: TabType;
}

// Define the hook parameters
interface UseUIPreferencesProps {
  pageKey: string; // Unique key for the page (e.g., 'homepage', 'projects-page')
  defaultViewMode?: ViewMode;
  defaultActiveTab?: TabType;
}

/**
 * Custom hook to manage and persist UI preferences across page refreshes
 */
export const useUIPreferences = ({
  pageKey,
  defaultViewMode = 'grid',
  defaultActiveTab = 'templates',
}: UseUIPreferencesProps) => {
  // Generate storage keys for this specific page
  const viewModeKey = `${pageKey}-viewMode`;
  const activeTabKey = `${pageKey}-activeTab`;

  // Initialize state with values from localStorage or defaults
  const [viewMode, setViewModeState] = useState<ViewMode>(() => {
    const savedViewMode = localStorage.getItem(viewModeKey);
    return (savedViewMode as ViewMode) || defaultViewMode;
  });

  const [activeTab, setActiveTabState] = useState<TabType>(() => {
    const savedActiveTab = localStorage.getItem(activeTabKey);
    return (savedActiveTab as TabType) || defaultActiveTab;
  });

  // Update localStorage when viewMode changes
  useEffect(() => {
    localStorage.setItem(viewModeKey, viewMode);
  }, [viewMode, viewModeKey]);

  // Update localStorage when activeTab changes
  useEffect(() => {
    localStorage.setItem(activeTabKey, activeTab);
  }, [activeTab, activeTabKey]);

  // Wrapper functions to update both state and localStorage
  const setViewMode = (newViewMode: ViewMode) => {
    setViewModeState(newViewMode);
  };

  const setActiveTab = (newActiveTab: TabType) => {
    setActiveTabState(newActiveTab);
  };

  return {
    viewMode,
    setViewMode,
    activeTab,
    setActiveTab,
  };
};

export default useUIPreferences;
