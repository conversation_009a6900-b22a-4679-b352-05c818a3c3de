import { ReactNode, useEffect, useRef } from 'react';
import styles from './DataGridTemplate.module.css';
import Search from '../specific/search/Search';
import ViewToggle, { ViewMode } from '../common/viewToggle/ViewToggle';
import CircularLoader from '../../assets/loader/CircularLoader';

interface DataGridTemplateProps {
  isLoading: boolean;
  isError: boolean;
  isEmpty: boolean;
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  onSearch: (term: string) => void;
  headerExtras?: ReactNode;
  children: ReactNode;
  loadingText?: string;
  errorTitle?: string;
  errorMessage?: string;
  emptyTitle?: string;
  emptyMessage?: string;
  hasMore?: boolean;
  onLoadMore?: () => void;
  isLoadingMore?: boolean;
  actionButton?: ReactNode;
}

export const DataGridTemplate = ({
  isLoading,
  isError,
  isEmpty,
  viewMode,
  onViewModeChange,
  onSearch,
  headerExtras,
  children,
  loadingText = 'Loading...',
  errorTitle = 'Error Loading Data',
  errorMessage = 'There was a problem loading your data. Please try again.',
  emptyTitle = 'No Items Found',
  emptyMessage = 'No items found matching your current filters.',
  hasMore = false,
  onLoadMore,
  isLoadingMore = false,
  actionButton, // Add this prop
}: DataGridTemplateProps) => {
  const observerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
        onLoadMore?.();
      }
    });

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => {
      if (observerRef.current) {
        observer.unobserve(observerRef.current);
      }
    };
  }, [hasMore, isLoadingMore, onLoadMore]);

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className={styles.loaderContainer}>
          <CircularLoader text={loadingText} />
        </div>
      );
    }

    if (isError) {
      return (
        <div className={styles.emptyState}>
          <h2>{errorTitle}</h2>
          <p>{errorMessage}</p>
        </div>
      );
    }

    if (isEmpty) {
      return (
        <div className={styles.emptyState}>
          <h2>{emptyTitle}</h2>
          <p>{emptyMessage}</p>
        </div>
      );
    }

    return (
      <div className={`${styles.grid} ${styles[viewMode]}`}>{children}</div>
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.headerSection}>
        <div className={styles.topRow}>
          <div className={styles.searchContainer}>
            <Search onSearch={onSearch} />
          </div>
          <div className={styles.controlsGroup}>
            <ViewToggle viewMode={viewMode} onViewChange={onViewModeChange} />
            {actionButton}
          </div>
        </div>
        {headerExtras}
      </div>

      {renderContent()}
      {isLoadingMore && <div>Loading more...</div>}
      <div ref={observerRef} style={{ height: '1px' }} />
    </div>
  );
};
