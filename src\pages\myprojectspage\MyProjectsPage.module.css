.projectContainer {
  display: flex;
  flex-direction: column;
  padding: 20px;
  height: 100%;
}

.headerSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 95%;
  align-self: center;
}
.projects {
  display: grid;
  gap: 20px;
}

.projects.grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  padding: 24px;
}

.projects.list {
  grid-template-columns: 1fr;
  margin: 20px 25px;
}

.projectError {
  width: 100%;
  text-align: center;
  color: #666;
  padding: 40px;
}

.icon img {
  width: 25px;
  height: 25px;
}

.buttonContainer {
  width: 155px;
  height: 48px;
  margin: 10px;
  display: flex;
  justify-content: flex-start;
  gap: 5px;
}

.loaderContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.emptyState {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  margin: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.emptyState h2 {
  margin-bottom: 12px;
  color: #333;
  font-size: 20px;
}

.emptyState p {
  color: #666;
  font-size: 16px;
}
/* .icon svg {
  transition: stroke 0.5s;
  width: 24px;
  height: 24px;
  margin-right: 8px;
} */

/* .header:hover .icon svg,
.header.active .icon svg {
  st: #174e86;
} */
