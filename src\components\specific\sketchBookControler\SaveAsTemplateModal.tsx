import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  IconButton,
  Typography,
  Box,
  CircularProgress,
} from '@mui/material';
import { IoClose } from 'react-icons/io5';
import { toast } from 'react-hot-toast';
import { useSaveSketchbookAsTemplateMutation } from '../../../services/sketchbookServices';
import useLocalStorage from '../../../hooks/useLocalStorage';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store/store';

interface SaveAsTemplateModalProps {
  open: boolean;
  onClose: () => void;
  sketchbookId: string;
  currentTitle: string;
}

const SaveAsTemplateModal: React.FC<SaveAsTemplateModalProps> = ({
  open,
  onClose,
  sketchbookId,
  currentTitle,
}) => {
  // const [userDetails] = useLocalStorage('user', null);
  // const userId = userDetails?.id;
  const [templateName, setTemplateName] = useState(`${currentTitle} Template`);
  const [isSaving, setIsSaving] = useState(false);
  const [saveSketchbookAsTemplate] = useSaveSketchbookAsTemplateMutation();

  // Get current sketchbook data from Redux store
  // const sketchbookState = useSelector((state: RootState) => state.sketchbook);
  // // const { pages, pageSize, flowNodes, flowEdges, pageEnabled, activePage } =
  // //   sketchbookState;

  const handleSave = async () => {
    if (!templateName.trim()) {
      toast.error('Template name is required');
      return;
    }

    setIsSaving(true);
    const toastId = toast.loading('Saving template...');

    try {
      // Create template payload for new API
      const templatePayload = {
        sketchbookId: sketchbookId,
        templateName: templateName,
      };

      // Save template using new API
      const response = await saveSketchbookAsTemplate(templatePayload).unwrap();

      console.log('Template saved response:', response);

      if (response.success) {
        toast.dismiss(toastId);
        toast.success('Template saved successfully');
        onClose();
      } else {
        throw new Error(response.message || 'Failed to save template');
      }
    } catch (error) {
      console.error('Error saving template:', error);
      toast.dismiss(toastId);
      toast.error('Failed to save template');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={isSaving ? undefined : onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          backgroundColor: 'var(--color-background-primary)',
        },
      }}
    >
      <DialogTitle sx={{ p: 3, pb: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h5" fontWeight={600}>
            Save as Template
          </Typography>
          <IconButton
            onClick={onClose}
            disabled={isSaving}
            sx={{
              color: 'var(--color-text-secondary)',
              '&:hover': {
                backgroundColor: 'var(--color-background-tertiary)',
              },
            }}
          >
            <IoClose />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ p: 3, pt: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Save your current sketchbook as a template that you can reuse later.
        </Typography>

        <TextField
          label="Template Name"
          value={templateName}
          onChange={(e) => setTemplateName(e.target.value)}
          fullWidth
          margin="normal"
          variant="outlined"
          disabled={isSaving}
          InputProps={{
            sx: {
              backgroundColor: 'var(--color-background-secondary)',
              color: 'var(--color-text-primary)',
            },
          }}
        />

        {/* <TextField
          label="Description"
          value={templateDescription}
          onChange={(e) => setTemplateDescription(e.target.value)}
          fullWidth
          margin="normal"
          variant="outlined"
          multiline
          rows={3}
          disabled={isSaving}
          InputProps={{
            sx: {
              backgroundColor: 'var(--color-background-secondary)',
              color: 'var(--color-text-primary)',
            },
          }}
        /> */}

        <Box display="flex" justifyContent="flex-end" gap={2} mt={3}>
          <Button
            variant="outlined"
            onClick={onClose}
            disabled={isSaving}
            sx={{
              borderColor: 'var(--color-border)',
              color: 'var(--color-text-primary)',
              '&:hover': {
                borderColor: 'var(--color-primary)',
                backgroundColor: 'transparent',
              },
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSave}
            disabled={isSaving}
            sx={{
              backgroundColor: 'var(--color-primary)',
              color: 'var(--color-primary-contrast)',
              '&:hover': {
                backgroundColor: 'var(--color-primary-dark)',
              },
            }}
            startIcon={
              isSaving ? <CircularProgress size={20} color="inherit" /> : null
            }
          >
            {isSaving ? 'Saving...' : 'Save Template'}
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default SaveAsTemplateModal;
