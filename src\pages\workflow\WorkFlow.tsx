import styles from './WorkFlow.module.css';
import { useLocation, useNavigate } from 'react-router-dom';
import useLocalStorage from '../../hooks/useLocalStorage';
import CustomButton from '../../components/common/button/CustomButton';
import IconButton from '../../components/common/button/IconButton';
import {
  IoArrowBack,
  IoClose,
  IoDocumentOutline,
  IoPeople,
  IoCalendarOutline,
  IoInformationCircleOutline,
} from 'react-icons/io5';
import { useGetAllUsersQuery } from '../../services/workflowServices';
import WorkflowNotes from '../../components/workflow/WorkflowNotes';
import WorkflowTeam from '../../components/workflow/WorkflowTeam';
import { useWorkflowForm } from '../../hooks/workflow';
import { priorityOptions } from '../../types/workflow/index';

const Workflow = () => {
  const { state } = useLocation();
  const { projectTitle, sketchbookId, projectId } = state || {};
  const navigate = useNavigate();
  const [currentUser] = useLocalStorage('user', null);
  const { data: users, isLoading: isUsersLoading } = useGetAllUsersQuery(null);

  const {
    formData,
    notes,
    people,
    validationErrors,
    handleInputChange,
    handleAddNote,
    handleDeleteNote,
    handleAddPerson,
    handleUpdatePerson,
    handleDeletePerson,
    handleCreateWorkflow,
    handleSaveAsDraft,
  } = useWorkflowForm({
    currentUser,
    projectTitle,
    sketchbookId,
    projectId,
  });

  return (
    <div className={styles.approvalWorkflow}>
      <div className={styles.header}>
        <IconButton
          type="secondary"
          icon={<IoArrowBack size={20} />}
          onClick={() => navigate(-1)}
          size="medium"
          title="Go back"
        />
        <h1 className={styles.title}>Create New Workflow</h1>
      </div>

      <div className={styles.card}>
        <h2 className={styles.sectionTitle}>
          <IoInformationCircleOutline size={20} /> Basic Information
        </h2>
        <div className={styles.formSection}>
          <div className={styles.inputGroup}>
            <label>Creator</label>
            <input
              type="text"
              placeholder="Creator Name"
              value={formData.creator}
              onChange={handleInputChange('creator')}
              className={styles.standardInput}
              readOnly
            />
            <div className={styles.helperText}>
              Your name as the workflow creator
            </div>
          </div>

          <div className={styles.inputGroup}>
            <label>
              <IoCalendarOutline size={18} /> Date
            </label>
            <input
              type="date"
              value={formData.date}
              onChange={handleInputChange('date')}
              className={styles.standardInput}
              readOnly
            />
            <div className={styles.helperText}>
              Today's date (automatically set)
            </div>
          </div>

          <div className={styles.inputGroup}>
            <label>Project Name</label>
            <input
              type="text"
              placeholder="Project Name"
              value={formData.projectName}
              onChange={handleInputChange('projectName')}
              className={styles.standardInput}
            />
            <div className={styles.helperText}>
              Name of the project this workflow is for
            </div>
          </div>

          <div className={styles.inputGroup}>
            <label>
              Priority <span className={styles.required}>*</span>
            </label>
            <select
              value={formData.priority}
              onChange={handleInputChange('priority')}
              className={`${styles.standardInput} ${validationErrors.has('priority') ? styles.errorInput : ''}`}
            >
              <option value="">Select Priority</option>
              {priorityOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {validationErrors.has('priority') ? (
              <div className={styles.errorText}>
                Please select a priority level
              </div>
            ) : (
              <div className={styles.helperText}>
                Importance level of this workflow
              </div>
            )}
          </div>

          <div className={styles.inputGroup}>
            <label>
              <IoCalendarOutline size={18} /> Due Date{' '}
              <span className={styles.required}>*</span>
            </label>
            <input
              type="date"
              value={formData.dueDate}
              onChange={handleInputChange('dueDate')}
              className={`${styles.standardInput} ${validationErrors.has('dueDate') ? styles.errorInput : ''}`}
            />
            {validationErrors.has('dueDate') ? (
              <div className={styles.errorText}>Please select a due date</div>
            ) : (
              <div className={styles.helperText}>
                When this workflow needs to be completed
              </div>
            )}
          </div>

          <div className={styles.inputGroup}>
            <label>
              Subject <span className={styles.required}>*</span>
            </label>
            <input
              type="text"
              placeholder="Subject"
              value={formData.subject}
              onChange={handleInputChange('subject')}
              className={`${styles.standardInput} ${validationErrors.has('subject') ? styles.errorInput : ''}`}
            />
            {validationErrors.has('subject') ? (
              <div className={styles.errorText}>Please enter a subject</div>
            ) : (
              <div className={styles.helperText}>
                Brief description of the workflow purpose
              </div>
            )}
          </div>

          <div className={styles.inputGroup}>
            <label>
              Required Actions <span className={styles.required}>*</span>
            </label>
            <textarea
              placeholder="Describe what actions are required from the team members"
              value={formData.requiredActions}
              onChange={handleInputChange('requiredActions')}
              className={`${styles.standardInput} ${validationErrors.has('requiredActions') ? styles.errorInput : ''}`}
              rows={4}
            />
            {validationErrors.has('requiredActions') ? (
              <div className={styles.errorText}>
                Please describe the required actions
              </div>
            ) : (
              <div className={styles.helperText}>
                Detailed description of what needs to be done
              </div>
            )}
          </div>
        </div>
      </div>

      <div className={styles.card}>
        <h2 className={styles.sectionTitle}>
          <IoDocumentOutline size={20} /> Notes & Comments
        </h2>
        <WorkflowNotes
          notes={notes}
          onAddNote={handleAddNote}
          onDeleteNote={handleDeleteNote}
        />
      </div>

      <div className={styles.card}>
        <h2 className={styles.sectionTitle}>
          <IoPeople size={20} /> Team Members
        </h2>
        <WorkflowTeam
          people={people}
          onAddPerson={handleAddPerson}
          onUpdatePerson={handleUpdatePerson}
          onDeletePerson={handleDeletePerson}
          users={users?.data?.users || []}
          validationError={validationErrors.has('people')}
        />
      </div>

      <div className={styles.card}>
        <div className={styles.actionButtons}>
          <CustomButton
            type="secondary"
            label="Cancel"
            leftIcon={<IoClose size={18} />}
            onClick={() => navigate(-1)}
            style={{ minWidth: '150px' }}
          />
          <CustomButton
            type="primary"
            label="Create Workflow"
            leftIcon={<IoDocumentOutline size={18} />}
            onClick={handleCreateWorkflow}
            style={{ minWidth: '150px' }}
          />
        </div>
      </div>
    </div>
  );
};

export default Workflow;
