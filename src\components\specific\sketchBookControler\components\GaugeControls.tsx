import React, { useState, useCallback, useMemo, useEffect } from 'react';
import styles from '../ChartPropertyController.module.css';
import { debounce } from 'lodash';
import { useUpdateCustomChartsMutation } from '../../../../services/sketchbookServices';
import { chartPayloadHandler } from '../utils/chartPayloadHandler';
import { Tooltip } from '@mui/material';
import { FaInfoCircle } from 'react-icons/fa';

// Define proper TypeScript interfaces for better type safety
interface GaugeOptions {
  nrOfLevels?: number;
  arcWidth?: number;
  arcPadding?: number;
  cornerRadius?: number;
  colors?: string[];
  needleColor?: string;
  textColor?: string;
  animate?: boolean;
  animDelay?: number;
  animateDuration?: number;
  [key: string]: any;
}

interface GaugeData {
  percent: number;
  text?: string;
}

interface GaugeChart {
  id: string;
  type: string;
  options: GaugeOptions;
  data: GaugeData;
  [key: string]: any;
}

interface GaugeControlsProps {
  sketchbookId: string;
  activePage: any;
  selectedChart: GaugeChart;
  onChartUpdate: (chart: GaugeChart) => void;
}

export const GaugeControls: React.FC<GaugeControlsProps> = ({
  sketchbookId,
  activePage,
  selectedChart,
  onChartUpdate,
}) => {
  if (!selectedChart || selectedChart.type !== 'gauge') {
    return null;
  }

  const [updateCustomCharts] = useUpdateCustomChartsMutation();
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [localValue, setLocalValue] = useState<number | null>(null);

  // Local state for input fields that use onBlur
  const [localNrOfLevels, setLocalNrOfLevels] = useState<string>('');
  const [localText, setLocalText] = useState<string>('');
  const [localAnimDelay, setLocalAnimDelay] = useState<string>('');
  const [localAnimDuration, setLocalAnimDuration] = useState<string>('');

  // Memoize options and data to prevent unnecessary re-renders
  const options = useMemo(
    () => selectedChart.options || {},
    [selectedChart.options]
  );
  const data = useMemo(
    () => selectedChart.data || { percent: 0.5 },
    [selectedChart.data]
  );

  // Reset local values when selected chart changes
  useEffect(() => {
    setLocalValue(null);
    setLocalNrOfLevels(String(options.nrOfLevels || 20));
    setLocalText(data.text || '');
    setLocalAnimDelay(String(options.animDelay || 0));
    setLocalAnimDuration(String(options.animateDuration || 2000));
  }, [
    selectedChart.id,
    options.nrOfLevels,
    options.animDelay,
    options.animateDuration,
    data.text,
  ]);

  // Consolidated update function to reduce code duplication
  const updateChart = useCallback(
    async (updatedChart: GaugeChart) => {
      try {
        const updatedChartPayload = chartPayloadHandler(
          updatedChart,
          'gauge',
          sketchbookId,
          activePage
        );
        setIsSaving(true);
        setError(null);
        await updateCustomCharts({
          id: selectedChart.id,
          payload: updatedChartPayload,
          chartType: 'gauge',
        });
        onChartUpdate(updatedChart);
      } catch (error) {
        setError('Failed to update gauge settings');
        console.error('Error updating gauge chart:', error);
      } finally {
        setIsSaving(false);
      }
    },
    [
      selectedChart.id,
      sketchbookId,
      activePage,
      updateCustomCharts,
      onChartUpdate,
    ]
  );

  // Handle option changes (for all options except percent)
  const handleOptionChange = useCallback(
    (key: string, value: any) => {
      const updatedChart = {
        ...selectedChart,
        options: {
          ...selectedChart.options,
          [key]: value,
        },
      };
      updateChart(updatedChart);
    },
    [selectedChart, updateChart]
  );

  // Handle percent value changes
  const handlePercentChange = useCallback(
    (value: number) => {
      // Clamp value between 0 and 1
      const clampedValue = Math.max(0, Math.min(1, value));
      setLocalValue(clampedValue);

      const updatedChart = {
        ...selectedChart,
        data: {
          ...selectedChart.data,
          percent: clampedValue,
        },
      };
      updateChart(updatedChart);
    },
    [selectedChart, updateChart]
  );

  // Handle color changes with special handling for array values
  const handleColorChange = useCallback(
    (
      key: string,
      index: number | null,
      event: React.ChangeEvent<HTMLInputElement>
    ) => {
      const color = event.target.value;

      if (index !== null) {
        const newColors = [...(options.colors || ['#FF5F6D', '#FFC371'])];
        newColors[index] = color;
        handleOptionChange('colors', newColors);
      } else {
        handleOptionChange(key, color);
      }
    },
    [options.colors, handleOptionChange]
  );

  // Create debounced versions for non-visual controls (text, numbers)
  const debouncedHandleOptionChange = useMemo(
    () => debounce(handleOptionChange, 500),
    [handleOptionChange]
  );

  // Immediate handlers for visual controls (no debouncing for real-time feedback)
  const immediateHandleOptionChange = handleOptionChange;
  const immediateHandlePercentChange = handlePercentChange;

  // Get the current percent value (use local state if available, otherwise use from data)
  const currentPercent = localValue !== null ? localValue : data.percent || 0;

  // Format percent for display (0-100%)
  const formatPercent = (value: number) => `${Math.round(value * 100)}%`;

  return (
    <div className={styles.section}>
      <div className={styles.heading}>
        Gauge Settings
        {isSaving && <span className={styles.savingIndicator}>Saving...</span>}
      </div>

      {error && <div className={styles.errorMessage}>{error}</div>}

      <div className={styles.modernControlsContainer}>
        <div className={styles.controlCard}>
          <h3 className={styles.cardTitle}>Basic Settings</h3>
          <div className={styles.controlGroup}>
            <div className={styles.labelWithTooltip}>
              <label className={styles.modernLabel}>Value</label>
              <Tooltip
                title="Set the gauge value (0-100%)"
                arrow
                placement="top"
              >
                <span className={styles.infoIcon}>
                  <FaInfoCircle />
                </span>
              </Tooltip>
            </div>
            <div className={styles.sliderWithValue}>
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={currentPercent}
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  setLocalValue(value);
                  immediateHandlePercentChange(value);
                }}
                className={styles.modernSlider}
              />
              <span className={styles.sliderValue}>
                {formatPercent(currentPercent)}
              </span>
            </div>
            <input
              type="number"
              step="0.01"
              min="0"
              max="1"
              value={currentPercent}
              onBlur={(e) => {
                const value = parseFloat(e.target.value);
                if (!isNaN(value)) {
                  setLocalValue(value);
                  handlePercentChange(value);
                }
              }}
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                setLocalValue(value);
              }}
              className={styles.modernInput}
            />
          </div>

          <div className={styles.controlGroup}>
            <div className={styles.labelWithTooltip}>
              <label className={styles.modernLabel}>Number of Levels</label>
              <Tooltip
                title="Number of segments in the gauge"
                arrow
                placement="top"
              >
                <span className={styles.infoIcon}>
                  <FaInfoCircle />
                </span>
              </Tooltip>
            </div>
            <input
              type="number"
              min="1"
              max="100"
              value={localNrOfLevels}
              onChange={(e) => setLocalNrOfLevels(e.target.value)}
              onBlur={(e) => {
                const value = parseInt(e.target.value);
                if (!isNaN(value) && value >= 1 && value <= 100) {
                  handleOptionChange('nrOfLevels', value);
                } else {
                  // Reset to current value if invalid
                  setLocalNrOfLevels(String(options.nrOfLevels || 20));
                }
              }}
              className={styles.modernInput}
            />
            <div className={styles.labelWithTooltip}>
              <label className={styles.modernLabel}>Text</label>
              <Tooltip
                title="Enter the text to show below the gauge. Click anywhere outside the input box to save your changes."
                arrow
                placement="top"
              >
                <span className={styles.infoIcon}>
                  <FaInfoCircle />
                </span>
              </Tooltip>
            </div>
            <textarea
              rows={2}
              style={{ resize: 'vertical' }}
              value={localText}
              placeholder="Enter text..."
              onChange={(e) => setLocalText(e.target.value)}
              onBlur={(e) => {
                const updatedChart = {
                  ...selectedChart,
                  data: {
                    ...selectedChart.data,
                    text: e.target.value,
                  },
                };
                updateChart(updatedChart);
              }}
              className={styles.modernInput}
            />
          </div>
        </div>

        <div className={styles.controlCard}>
          <h3 className={styles.cardTitle}>Appearance</h3>
          <div className={styles.controlGroup}>
            <div className={styles.labelWithTooltip}>
              <label className={styles.modernLabel}>Arc Width</label>
              <Tooltip
                title="Width of the gauge arc (0-1)"
                arrow
                placement="top"
              >
                <span className={styles.infoIcon}>
                  <FaInfoCircle />
                </span>
              </Tooltip>
            </div>
            <div className={styles.sliderWithValue}>
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={options.arcWidth || 0.3}
                onChange={(e) =>
                  immediateHandleOptionChange(
                    'arcWidth',
                    parseFloat(e.target.value)
                  )
                }
                className={styles.modernSlider}
              />
              <span className={styles.sliderValue}>
                {options.arcWidth || 0.3}
              </span>
            </div>
          </div>

          <div className={styles.controlGroup}>
            <div className={styles.labelWithTooltip}>
              <label className={styles.modernLabel}>Arc Padding</label>
              <Tooltip
                title="Padding between arc segments (0-0.5)"
                arrow
                placement="top"
              >
                <span className={styles.infoIcon}>
                  <FaInfoCircle />
                </span>
              </Tooltip>
            </div>
            <div className={styles.sliderWithValue}>
              <input
                type="range"
                min="0"
                max="0.5"
                step="0.01"
                value={options.arcPadding || 0.05}
                onChange={(e) =>
                  immediateHandleOptionChange(
                    'arcPadding',
                    parseFloat(e.target.value)
                  )
                }
                className={styles.modernSlider}
              />
              <span className={styles.sliderValue}>
                {options.arcPadding || 0.05}
              </span>
            </div>
          </div>

          <div className={styles.controlGroup}>
            <div className={styles.labelWithTooltip}>
              <label className={styles.modernLabel}>Corner Radius</label>
              <Tooltip
                title="Radius of arc segment corners (0-20)"
                arrow
                placement="top"
              >
                <span className={styles.infoIcon}>
                  <FaInfoCircle />
                </span>
              </Tooltip>
            </div>
            <div className={styles.sliderWithValue}>
              <input
                type="range"
                min="0"
                max="20"
                value={options.cornerRadius || 6}
                onChange={(e) =>
                  immediateHandleOptionChange(
                    'cornerRadius',
                    parseInt(e.target.value)
                  )
                }
                className={styles.modernSlider}
              />
              <span className={styles.sliderValue}>
                {options.cornerRadius || 6}
              </span>
            </div>
          </div>
        </div>

        <div className={styles.controlCard}>
          <h3 className={styles.cardTitle}>Colors</h3>
          <div className={styles.colorGrid}>
            <div className={styles.colorControl}>
              <div className={styles.labelWithTooltip}>
                <label className={styles.modernLabel}>Start Color</label>
                <Tooltip
                  title="Color for the minimum value"
                  arrow
                  placement="top"
                >
                  <span className={styles.infoIcon}>
                    <FaInfoCircle />
                  </span>
                </Tooltip>
              </div>
              <input
                type="color"
                value={(options.colors && options.colors[0]) || '#FF5F6D'}
                onChange={(e) => handleColorChange('colors', 0, e)}
                className={styles.modernColorPicker}
              />
            </div>

            <div className={styles.colorControl}>
              <div className={styles.labelWithTooltip}>
                <label className={styles.modernLabel}>End Color</label>
                <Tooltip
                  title="Color for the maximum value"
                  arrow
                  placement="top"
                >
                  <span className={styles.infoIcon}>
                    <FaInfoCircle />
                  </span>
                </Tooltip>
              </div>
              <input
                type="color"
                value={(options.colors && options.colors[1]) || '#FFC371'}
                onChange={(e) => handleColorChange('colors', 1, e)}
                className={styles.modernColorPicker}
              />
            </div>

            <div className={styles.colorControl}>
              <div className={styles.labelWithTooltip}>
                <label className={styles.modernLabel}>Needle Color</label>
                <Tooltip
                  title="Color of the gauge needle"
                  arrow
                  placement="top"
                >
                  <span className={styles.infoIcon}>
                    <FaInfoCircle />
                  </span>
                </Tooltip>
              </div>
              <input
                type="color"
                value={options.needleColor || '#464A4F'}
                onChange={(e) => handleColorChange('needleColor', null, e)}
                className={styles.modernColorPicker}
              />
            </div>

            <div className={styles.colorControl}>
              <div className={styles.labelWithTooltip}>
                <label className={styles.modernLabel}>Text Color</label>
                <Tooltip title="Color of the gauge text" arrow placement="top">
                  <span className={styles.infoIcon}>
                    <FaInfoCircle />
                  </span>
                </Tooltip>
              </div>
              <input
                type="color"
                value={options.textColor || '#464A4F'}
                onChange={(e) => handleColorChange('textColor', null, e)}
                className={styles.modernColorPicker}
              />
            </div>
          </div>
        </div>

        <div className={styles.controlCard}>
          <h3 className={styles.cardTitle}>Animation</h3>
          <div className={styles.controlGroup}>
            <label className={styles.modernCheckboxLabel}>
              <input
                type="checkbox"
                checked={options.animate !== false}
                onChange={(e) =>
                  debouncedHandleOptionChange('animate', e.target.checked)
                }
                className={styles.modernCheckbox}
              />
              Enable Animation
            </label>
          </div>

          {options.animate && (
            <>
              <div className={styles.controlGroup}>
                <div className={styles.labelWithTooltip}>
                  <label className={styles.modernLabel}>
                    Animation Delay (ms)
                  </label>
                  <Tooltip
                    title="Delay before animation starts"
                    arrow
                    placement="top"
                  >
                    <span className={styles.infoIcon}>
                      <FaInfoCircle />
                    </span>
                  </Tooltip>
                </div>
                <input
                  type="number"
                  min="0"
                  max="5000"
                  value={localAnimDelay}
                  onChange={(e) => setLocalAnimDelay(e.target.value)}
                  onBlur={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value) && value >= 0 && value <= 5000) {
                      handleOptionChange('animDelay', value);
                    } else {
                      // Reset to current value if invalid
                      setLocalAnimDelay(String(options.animDelay || 0));
                    }
                  }}
                  className={styles.modernInput}
                />
              </div>

              <div className={styles.controlGroup}>
                <div className={styles.labelWithTooltip}>
                  <label className={styles.modernLabel}>
                    Animation Duration (ms)
                  </label>
                  <Tooltip
                    title="Duration of the animation"
                    arrow
                    placement="top"
                  >
                    <span className={styles.infoIcon}>
                      <FaInfoCircle />
                    </span>
                  </Tooltip>
                </div>
                <input
                  type="number"
                  min="0"
                  max="10000"
                  value={localAnimDuration}
                  onChange={(e) => setLocalAnimDuration(e.target.value)}
                  onBlur={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value) && value >= 0 && value <= 10000) {
                      handleOptionChange('animateDuration', value);
                    } else {
                      // Reset to current value if invalid
                      setLocalAnimDuration(
                        String(options.animateDuration || 2000)
                      );
                    }
                  }}
                  className={styles.modernInput}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
