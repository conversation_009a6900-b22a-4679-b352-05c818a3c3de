import { toast } from 'react-hot-toast';
import { WorkflowNotification } from '../types/workflow/index';

/**
 * Generate notification message based on type and context
 */
export const generateNotificationMessage = (
  type: WorkflowNotification['type'],
  workflowTitle: string,
  creatorName?: string,
  status?: string
): string => {
  switch (type) {
    case 'assignment':
      return `You have been assigned to review "${workflowTitle}"${creatorName ? ` by ${creatorName}` : ''}`;
    
    case 'status_change':
      switch (status) {
        case 'approved':
          return `Workflow "${workflowTitle}" has been approved`;
        case 'rejected':
          return `Workflow "${workflowTitle}" has been rejected`;
        case 'conditional':
          return `Workflow "${workflowTitle}" has been conditionally approved`;
        default:
          return `Workflow "${workflowTitle}" status has been updated`;
      }
    
    case 'workflow_created':
      return `New workflow "${workflowTitle}" has been created${creatorName ? ` by ${creatorName}` : ''}`;
    
    default:
      return `Update on workflow "${workflowTitle}"`;
  }
};

/**
 * Generate unique notification ID
 */
export const generateNotificationId = (
  workflowId: string,
  userId: string,
  type: string,
  timestamp?: string
): string => {
  const time = timestamp || new Date().toISOString();
  return `${workflowId}-${userId}-${type}-${time}`.replace(/[:.]/g, '-');
};

/**
 * Show toast notification for workflow events
 */
export const showWorkflowToast = (
  type: WorkflowNotification['type'],
  message: string,
  priority?: 'Low' | 'Medium' | 'High'
): void => {
  const toastOptions = {
    duration: priority === 'High' ? 6000 : 4000,
    position: 'top-right' as const,
  };

  switch (type) {
    case 'assignment':
      toast(message, {
        ...toastOptions,
        icon: '📋',
        style: {
          background: '#fff3cd',
          color: '#856404',
          border: '1px solid #ffeeba',
        },
      });
      break;
    
    case 'status_change':
      if (message.includes('approved')) {
        toast.success(message, {
          ...toastOptions,
          icon: '✅',
        });
      } else if (message.includes('rejected')) {
        toast.error(message, {
          ...toastOptions,
          icon: '❌',
        });
      } else if (message.includes('conditional')) {
        toast(message, {
          ...toastOptions,
          icon: '⚠️',
          style: {
            background: '#d1ecf1',
            color: '#0c5460',
            border: '1px solid #bee5eb',
          },
        });
      }
      break;
    
    case 'workflow_created':
      toast(message, {
        ...toastOptions,
        icon: '🆕',
        style: {
          background: '#d4edda',
          color: '#155724',
          border: '1px solid #c3e6cb',
        },
      });
      break;
    
    default:
      toast(message, toastOptions);
  }
};

/**
 * Determine if notification requires action from user
 */
export const isActionRequired = (
  type: WorkflowNotification['type'],
  status?: string,
  currentUserId?: string,
  workflowCurrentUserId?: string
): boolean => {
  if (type === 'assignment') {
    return true;
  }
  
  if (type === 'status_change' && status === 'in-progress') {
    return currentUserId === workflowCurrentUserId;
  }
  
  return false;
};

/**
 * Get notification icon based on type and status
 */
export const getNotificationIcon = (
  type: WorkflowNotification['type'],
  status?: string
): string => {
  switch (type) {
    case 'assignment':
      return '📋';
    case 'workflow_created':
      return '🆕';
    case 'status_change':
      switch (status) {
        case 'approved':
          return '✅';
        case 'rejected':
          return '❌';
        case 'conditional':
          return '⚠️';
        default:
          return '📄';
      }
    default:
      return '📄';
  }
};

/**
 * Get notification priority color
 */
export const getNotificationPriorityColor = (priority?: 'Low' | 'Medium' | 'High'): string => {
  switch (priority) {
    case 'High':
      return '#dc3545'; // Red
    case 'Medium':
      return '#fd7e14'; // Orange
    case 'Low':
      return '#28a745'; // Green
    default:
      return '#6c757d'; // Gray
  }
};

/**
 * Format notification date for display
 */
export const formatNotificationDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  } else if (diffInMinutes < 1440) { // 24 hours
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}h ago`;
  } else if (diffInMinutes < 10080) { // 7 days
    const days = Math.floor(diffInMinutes / 1440);
    return `${days}d ago`;
  } else {
    return date.toLocaleDateString();
  }
};

/**
 * Group notifications by date
 */
export const groupNotificationsByDate = (notifications: WorkflowNotification[]): {
  [key: string]: WorkflowNotification[];
} => {
  const groups: { [key: string]: WorkflowNotification[] } = {};
  
  notifications.forEach(notification => {
    const date = new Date(notification.createdAt);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    let groupKey: string;
    
    if (date.toDateString() === today.toDateString()) {
      groupKey = 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      groupKey = 'Yesterday';
    } else {
      groupKey = date.toLocaleDateString();
    }
    
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    
    groups[groupKey].push(notification);
  });
  
  return groups;
};
