import React from 'react';
import Chip from '../common/chips/Chip';

const LaTeXChipTest: React.FC = () => {
  const testCases = [
    {
      id: 1,
      label: 'Not taking Alpha Beta symbols',
      description: 'Original failing test case - should auto-convert to Greek letters',
    },
    {
      id: 2,
      label: 'Testing $\\alpha$ and $\\beta$ symbols',
      description: 'Direct LaTeX syntax test',
    },
    {
      id: 3,
      label: 'Price is $100 and cost $200',
      description: 'Currency test - should not render as math',
    },
    {
      id: 4,
      label: 'Formula: $E = mc^2$ costs $50',
      description: 'Mixed LaTeX and currency test',
    },
    {
      id: 5,
      label: 'Greek letters: Alpha Beta Gamma Delta Epsilon',
      description: 'Multiple Greek letter auto-conversion',
    },
    {
      id: 6,
      label: 'Lowercase: alpha beta gamma delta epsilon',
      description: 'Lowercase Greek letter auto-conversion',
    },
    {
      id: 7,
      label: 'Math: $\\sum_{i=1}^{n} x_i$ and $\\int_0^1 f(x)dx$',
      description: 'Complex LaTeX expressions',
    },
    {
      id: 8,
      label: 'Percentage: 50% increase and $\\frac{1}{2}$ ratio',
      description: 'Percentage and fraction test',
    },
  ];

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>LaTeX Chip Component Test</h1>
      <p>Testing LaTeX rendering in chip components with various scenarios:</p>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {testCases.map((testCase) => (
          <div key={testCase.id} style={{ 
            border: '1px solid #ddd', 
            borderRadius: '8px', 
            padding: '16px',
            backgroundColor: '#f9f9f9'
          }}>
            <h3 style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#666' }}>
              Test {testCase.id}: {testCase.description}
            </h3>
            <div style={{ marginBottom: '8px', fontSize: '12px', color: '#888' }}>
              Input: "{testCase.label}"
            </div>
            <Chip
              id={testCase.id}
              label={testCase.label}
              type="light"
              handleOnClick={() => console.log(`Clicked test ${testCase.id}`)}
            />
          </div>
        ))}
      </div>

      <div style={{ marginTop: '32px', padding: '16px', backgroundColor: '#e8f4fd', borderRadius: '8px' }}>
        <h3>Expected Results:</h3>
        <ul>
          <li><strong>Test 1:</strong> Should show "Not taking α β symbols" with rendered Greek letters</li>
          <li><strong>Test 2:</strong> Should show "Testing α and β symbols" with rendered Greek letters</li>
          <li><strong>Test 3:</strong> Should show "$100" and "$200" as plain currency (not math)</li>
          <li><strong>Test 4:</strong> Should show rendered E=mc² formula and plain $50 currency</li>
          <li><strong>Test 5:</strong> Should show "Greek letters: α β γ δ ε" with all Greek letters rendered</li>
          <li><strong>Test 6:</strong> Should show lowercase Greek letters rendered</li>
          <li><strong>Test 7:</strong> Should show complex mathematical expressions rendered</li>
          <li><strong>Test 8:</strong> Should show "50%" as plain text and rendered fraction</li>
        </ul>
      </div>

      <div style={{ marginTop: '16px', padding: '16px', backgroundColor: '#fff3cd', borderRadius: '8px' }}>
        <h3>Troubleshooting:</h3>
        <p>If LaTeX is not rendering:</p>
        <ol>
          <li>Check browser console for KaTeX errors</li>
          <li>Verify KaTeX CSS is loaded properly</li>
          <li>Ensure remarkMath and rehypeKatex plugins are working</li>
          <li>Check if singleDollarTextMath is enabled</li>
        </ol>
      </div>
    </div>
  );
};

export default LaTeXChipTest;
