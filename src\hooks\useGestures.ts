import { useCallback, useRef, useState } from 'react';

interface GestureState {
  isPinching: boolean;
  isPanning: boolean;
  isLongPress: boolean;
  scale: number;
  panX: number;
  panY: number;
}

interface GestureHandlers {
  onPinchStart?: (scale: number) => void;
  onPinchMove?: (scale: number, deltaScale: number) => void;
  onPinchEnd?: (scale: number) => void;
  onPanStart?: (x: number, y: number) => void;
  onPanMove?: (x: number, y: number, deltaX: number, deltaY: number) => void;
  onPanEnd?: (x: number, y: number) => void;
  onLongPress?: (x: number, y: number) => void;
  onTap?: (x: number, y: number) => void;
  onDoubleTap?: (x: number, y: number) => void;
}

export const useGestures = (handlers: GestureHandlers = {}) => {
  const [gestureState, setGestureState] = useState<GestureState>({
    isPinching: false,
    isPanning: false,
    isLongPress: false,
    scale: 1,
    panX: 0,
    panY: 0,
  });

  const gestureRef = useRef({
    initialDistance: 0,
    initialScale: 1,
    initialPanX: 0,
    initialPanY: 0,
    lastTapTime: 0,
    longPressTimer: null as NodeJS.Timeout | null,
    touches: [] as Touch[],
  });

  const getDistance = useCallback((touch1: Touch, touch2: Touch) => {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  }, []);

  const getCenter = useCallback((touch1: Touch, touch2: Touch) => {
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2,
    };
  }, []);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touches = Array.from(e.touches);
    gestureRef.current.touches = touches;

    if (touches.length === 1) {
      // Single touch - potential pan or long press
      const touch = touches[0];
      gestureRef.current.initialPanX = touch.clientX;
      gestureRef.current.initialPanY = touch.clientY;

      // Start long press timer
      gestureRef.current.longPressTimer = setTimeout(() => {
        setGestureState(prev => ({ ...prev, isLongPress: true }));
        handlers.onLongPress?.(touch.clientX, touch.clientY);
      }, 500);

    } else if (touches.length === 2) {
      // Two touches - pinch gesture
      const distance = getDistance(touches[0], touches[1]);
      gestureRef.current.initialDistance = distance;
      gestureRef.current.initialScale = gestureState.scale;

      setGestureState(prev => ({ ...prev, isPinching: true }));
      handlers.onPinchStart?.(gestureState.scale);

      // Clear long press timer
      if (gestureRef.current.longPressTimer) {
        clearTimeout(gestureRef.current.longPressTimer);
        gestureRef.current.longPressTimer = null;
      }
    }
  }, [gestureState.scale, handlers, getDistance]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    const touches = Array.from(e.touches);

    if (touches.length === 1 && !gestureState.isPinching) {
      // Single touch pan
      const touch = touches[0];
      const deltaX = touch.clientX - gestureRef.current.initialPanX;
      const deltaY = touch.clientY - gestureRef.current.initialPanY;

      if (!gestureState.isPanning && (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10)) {
        setGestureState(prev => ({ ...prev, isPanning: true }));
        handlers.onPanStart?.(touch.clientX, touch.clientY);

        // Clear long press timer when movement detected
        if (gestureRef.current.longPressTimer) {
          clearTimeout(gestureRef.current.longPressTimer);
          gestureRef.current.longPressTimer = null;
        }
      }

      if (gestureState.isPanning) {
        const newPanX = gestureState.panX + deltaX;
        const newPanY = gestureState.panY + deltaY;
        
        setGestureState(prev => ({ ...prev, panX: newPanX, panY: newPanY }));
        handlers.onPanMove?.(newPanX, newPanY, deltaX, deltaY);
      }

    } else if (touches.length === 2 && gestureState.isPinching) {
      // Two touch pinch
      const distance = getDistance(touches[0], touches[1]);
      const scale = (distance / gestureRef.current.initialDistance) * gestureRef.current.initialScale;
      const deltaScale = scale - gestureState.scale;

      setGestureState(prev => ({ ...prev, scale }));
      handlers.onPinchMove?.(scale, deltaScale);
    }
  }, [gestureState, handlers, getDistance]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    const touches = Array.from(e.touches);

    // Clear long press timer
    if (gestureRef.current.longPressTimer) {
      clearTimeout(gestureRef.current.longPressTimer);
      gestureRef.current.longPressTimer = null;
    }

    if (touches.length === 0) {
      // All touches ended
      if (gestureState.isPinching) {
        handlers.onPinchEnd?.(gestureState.scale);
      }
      
      if (gestureState.isPanning) {
        handlers.onPanEnd?.(gestureState.panX, gestureState.panY);
      }

      // Handle tap gestures
      if (!gestureState.isPanning && !gestureState.isPinching && !gestureState.isLongPress) {
        const now = Date.now();
        const timeSinceLastTap = now - gestureRef.current.lastTapTime;
        
        if (timeSinceLastTap < 300) {
          // Double tap
          const touch = gestureRef.current.touches[0];
          handlers.onDoubleTap?.(touch.clientX, touch.clientY);
        } else {
          // Single tap
          const touch = gestureRef.current.touches[0];
          handlers.onTap?.(touch.clientX, touch.clientY);
        }
        
        gestureRef.current.lastTapTime = now;
      }

      setGestureState({
        isPinching: false,
        isPanning: false,
        isLongPress: false,
        scale: gestureState.scale,
        panX: gestureState.panX,
        panY: gestureState.panY,
      });
    }
  }, [gestureState, handlers]);

  return {
    gestureState,
    gestureHandlers: {
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
    },
  };
};
