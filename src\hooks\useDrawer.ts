import { useState, useCallback, useEffect } from 'react';

export const useDrawer = (initialLeftOpen = true, initialRightOpen = true) => {
  const [leftDrawerOpen, setLeftDrawerOpen] = useState(initialLeftOpen);
  const [rightDrawerOpen, setRightDrawerOpen] = useState(initialRightOpen);

  // Close drawers on mobile by default
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        setLeftDrawerOpen(false);
        setRightDrawerOpen(false);
      }
    };

    // Check on initial load
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const toggleLeftDrawer = useCallback(() => {
    setLeftDrawerOpen((prev) => !prev);
  }, []);

  const toggleRightDrawer = useCallback(() => {
    setRightDrawerOpen((prev) => !prev);
  }, []);

  return {
    leftDrawerOpen,
    rightDrawerOpen,
    toggleLeftDrawer,
    toggleRightDrawer,
  };
};
