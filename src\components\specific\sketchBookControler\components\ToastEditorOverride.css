/* Force white background and black text for Toast UI Editor regardless of theme */
.toast-editor-wrapper {
  background-color: white !important;
}

/* Editor toolbar */
.toast-editor-wrapper .toastui-editor-toolbar {
  background-color: #f5f5f5 !important;
  border-color: #ddd !important;
}

.toast-editor-wrapper .toastui-editor-toolbar-icons {
  color: #000 !important;
}

/* Editor main area */
.toast-editor-wrapper .toastui-editor-main {
  background-color: white !important;
}

/* Markdown editor */
.toast-editor-wrapper .toastui-editor-md-container {
  background-color: white !important;
}

.toast-editor-wrapper .toastui-editor-md-code-block-line-background {
  background-color: #f0f0f0 !important;
}

/* WYSIWYG editor */
.toast-editor-wrapper .toastui-editor-ww-container {
  background-color: white !important;
}

/* Editor content */
.toast-editor-wrapper .toastui-editor-contents {
  color: #000 !important;
  background-color: white !important;
}

.toast-editor-wrapper .toastui-editor-contents h1,
.toast-editor-wrapper .toastui-editor-contents h2,
.toast-editor-wrapper .toastui-editor-contents h3,
.toast-editor-wrapper .toastui-editor-contents h4,
.toast-editor-wrapper .toastui-editor-contents h5,
.toast-editor-wrapper .toastui-editor-contents h6 {
  color: #000 !important;
}

.toast-editor-wrapper .toastui-editor-contents p,
.toast-editor-wrapper .toastui-editor-contents li,
.toast-editor-wrapper .toastui-editor-contents blockquote {
  color: #000 !important;
}

/* Code blocks */
.toast-editor-wrapper .toastui-editor-contents pre,
.toast-editor-wrapper .toastui-editor-contents code {
  background-color: #f0f0f0 !important;
  color: #000 !important;
}

/* Table styling */
.toast-editor-wrapper .toastui-editor-contents th {
  background-color: #e0e0e0 !important;
  color: #000 !important;
}

.toast-editor-wrapper .toastui-editor-contents td {
  color: #000 !important;
  border-color: #ddd !important;
}

.toast-editor-wrapper .toastui-editor-contents table {
  border-color: #ddd !important;
}

/* Override dark theme styles */
.toast-editor-wrapper.dark-theme .toastui-editor-toolbar {
  background-color: #f5f5f5 !important;
  border-color: #ddd !important;
}

.toast-editor-wrapper.dark-theme .toastui-editor-main {
  background-color: white !important;
}

.toast-editor-wrapper.dark-theme .toastui-editor-md-container {
  background-color: white !important;
}

.toast-editor-wrapper.dark-theme .toastui-editor-ww-container {
  background-color: white !important;
}

.toast-editor-wrapper.dark-theme .toastui-editor-contents {
  color: #000 !important;
}

.toast-editor-wrapper.dark-theme .toastui-editor-contents h1,
.toast-editor-wrapper.dark-theme .toastui-editor-contents h2,
.toast-editor-wrapper.dark-theme .toastui-editor-contents h3,
.toast-editor-wrapper.dark-theme .toastui-editor-contents h4,
.toast-editor-wrapper.dark-theme .toastui-editor-contents h5,
.toast-editor-wrapper.dark-theme .toastui-editor-contents h6 {
  color: #000 !important;
}

.toast-editor-wrapper.dark-theme .toastui-editor-contents p,
.toast-editor-wrapper.dark-theme .toastui-editor-contents li,
.toast-editor-wrapper.dark-theme .toastui-editor-contents blockquote {
  color: #000 !important;
}

/* Ensure the editor input area has black text */
.toast-editor-wrapper .ProseMirror {
  color: #000 !important;
}

.toast-editor-wrapper.dark-theme .ProseMirror {
  color: #000 !important;
}

/* Fix dropdown menus */
.toastui-editor-dropdown-menu {
  background-color: white !important;
  border-color: #ddd !important;
  color: #000 !important;
}

.toast-editor-wrapper.dark-theme .toastui-editor-dropdown-menu {
  background-color: white !important;
  border-color: #ddd !important;
  color: #000 !important;
}

/* Fix dropdown menu items */
.toastui-editor-dropdown-menu .toastui-editor-dropdown-item {
  color: #000 !important;
}

.toastui-editor-dropdown-menu .toastui-editor-dropdown-item:hover {
  background-color: #f0f0f0 !important;
}

.toast-editor-wrapper.dark-theme .toastui-editor-dropdown-menu .toastui-editor-dropdown-item {
  color: #000 !important;
}

.toast-editor-wrapper.dark-theme .toastui-editor-dropdown-menu .toastui-editor-dropdown-item:hover {
  background-color: #f0f0f0 !important;
}
