.container {
  display: flex;
  flex-direction: column;
  padding: 20px;
  height: 100%;
}

.headerSection {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 95%;
  align-self: center;
}

.controlsGroup {
  display: flex;
  align-items: center;
  gap: 16px;
}

.viewControls {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding: 0 var(--spacing-4);
}

.topRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.searchContainer {
  flex: 1;
  margin-right: 16px;
}

.grid {
  display: grid;
  gap: 20px;
}

.grid.grid {
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  padding: 24px;
}

.grid.list {
  grid-template-columns: 1fr;
}

.loaderContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.emptyState {
  text-align: center;
  padding: 40px;
  background: var(--color-background-card);
  border-radius: var(--radius-lg);
  margin: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}

.emptyState h2 {
  margin-bottom: 12px;
  color: var(--color-text-primary);
  font-size: 20px;
  transition: color 0.3s ease;
}

.emptyState p {
  color: var(--color-text-secondary);
  font-size: 16px;
  transition: color 0.3s ease;
}

@media (max-width: 768px) {
  .topRow {
    flex-direction: column;
    align-items: stretch;
  }

  .controlsGroup {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }

  .header {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .viewControls {
    width: 100%;
    justify-content: space-between;
  }
}
