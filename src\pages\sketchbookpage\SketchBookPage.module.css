.sketchbookcontainer {
  position: relative;
  margin-top: 77px;
  height: calc(100vh - 108px);
  display: flex;
  overflow: hidden;
}

.headerContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 49px;
  z-index: 1200;
  background-color: var(--color-background-secondary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease;
}

.breadcrumbsContainer {
  position: fixed;
  top: 59px;
  left: 0;
  width: 100%;
  height: 28px;
  z-index: 1100;
  background-color: var(--color-background-secondary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding: 0 16px;
  font-size: 12px;
  box-sizing: border-box;
  overflow: visible;
  transition: background-color 0.3s ease;
}

.breadcrumbsWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.tourButtonContainer {
  display: flex;
  align-items: center;
}

.breadcrumbsContainer :global(.MuiBreadcrumbs-ol) {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  height: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.breadcrumbsContainer :global(.MuiBreadcrumbs-li) {
  display: flex;
  align-items: center;
  height: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.breadcrumbsContainer :global(.MuiBreadcrumbs-separator) {
  margin: 0 6px;
  display: flex;
  align-items: center;
  height: 100%;
}

.mainContent {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  flex: 1;
  background-color: #f8f9fa;
  margin-top: 10px;
}

.mainContentWithLeftDrawer {
  margin-top: 10px;
  /* margin-left: 224px; */
  width: calc(100% - 224px);
  transition:
    margin-left 0.3s ease,
    width 0.3s ease;
}

.mainContentWithRightDrawer {
  margin-top: 12px;
  /* margin-right: 224px; */
  width: calc(100% - 224px);
  transition:
    margin-right 0.3s ease,
    width 0.3s ease;
}

.mainContentWithBothDrawers {
  /* margin-left: 224px;
    margin-right: 224px; */
  width: calc(100% - 448px);
  transition:
    margin 0.3s ease,
    width 0.3s ease;
}

/* Remove the drawer button styles from SketchBookPage.module.css since they're now in StyledDrawer.module.css */
.drawerButton {
  display: none !important;
}

.drawerButton[data-position='left'] {
  display: none !important;
}

.drawerButton[data-position='right'] {
  display: none !important;
}

.drawerButton:hover {
  display: none !important;
}

/* Drawer Styles */
:global(.MuiDrawer-paper) {
  background-color: var(--color-background-secondary) !important;
  backdrop-filter: blur(5px) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  top: 77px !important;
  height: calc(100% - 118px) !important;
  width: 224px !important;
  transition:
    transform 0.3s ease,
    background-color 0.3s ease !important;
  overflow-x: hidden;
  margin-top: 10px;
  color: var(--color-text-primary) !important;
}

/* Close button styles */
:global(.MuiDrawer-paper) :global(.MuiIconButton-root) {
  position: absolute !important;
  top: 14px !important;
  transform: none !important;
  width: 24px !important;
  height: 24px !important;
  background-color: white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  z-index: 10 !important;
  padding: 0 !important;
  min-width: unset !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:global(.MuiDrawer-paper[data-anchor='left']) :global(.MuiIconButton-root) {
  right: -12px !important;
}

:global(.MuiDrawer-paper[data-anchor='right']) :global(.MuiIconButton-root) {
  left: -12px !important;
}

:global(.MuiDrawer-paper) :global(.MuiIconButton-root svg) {
  width: 14px !important;
  height: 14px !important;
  color: #666 !important;
}

:global(.MuiDrawer-paper) :global(.MuiIconButton-root:hover) {
  background-color: #f5f5f5 !important;
  transform: scale(1.05) !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
}

/* Scrollbar Styles */
.mainContent::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.mainContent::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.mainContent::-webkit-scrollbar-thumb {
  background: #bbb;
  border-radius: 4px;
}

.mainContent::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .mainContentWithBothDrawers {
    /* margin-left: 200px;
    margin-right: 200px; */
    width: calc(100% - 400px);
  }

  .mainContentWithLeftDrawer {
    /* margin-left: 200px; */
    width: calc(100%);
  }

  .mainContentWithRightDrawer {
    /* margin-right: 200px; */
    width: calc(100%);
  }

  :global(.MuiDrawer-paper) {
    width: 200px !important;
  }
}

@media (max-width: 992px) {
  .mainContentWithBothDrawers {
    /* margin-left: 180px;
    margin-right: 180px; */
    width: calc(100% - 360px);
  }

  .mainContentWithLeftDrawer {
    /* margin-left: 180px; */
    width: calc(100% - 180px);
  }

  .mainContentWithRightDrawer {
    /* margin-right: 180px; */
    width: calc(100% - 180px);
  }

  :global(.MuiDrawer-paper) {
    width: 180px !important;
  }

  .breadcrumbsContainer {
    padding: 0 12px;
  }

  .breadcrumbsContainer :global(.MuiBreadcrumbs-separator) {
    margin: 0 5px;
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .sketchbookcontainer {
    margin-top: 60px;
    height: calc(100vh - 60px);
  }

  .headerContainer {
    height: 60px;
  }

  .breadcrumbsContainer {
    top: 60px;
    height: 40px;
    padding: 0 16px;
    font-size: 14px;
  }

  .mainContent {
    padding: 16px;
    margin-bottom: 60px;
    touch-action: manipulation;
  }

  .mainContentWithLeftDrawer {
    width: calc(100% - 280px);
    transition: width 0.3s ease;
  }

  .mainContentWithRightDrawer {
    width: calc(100% - 280px);
    transition: width 0.3s ease;
  }

  .mainContentWithBothDrawers {
    width: calc(100% - 560px);
    transition: width 0.3s ease;
  }

  :global(.MuiDrawer-paper) {
    top: 100px !important;
    height: calc(100% - 160px) !important;
    width: 280px !important;
    border-radius: 8px 0 0 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  :global(.MuiDrawer-paper[data-anchor='right']) {
    border-radius: 0 8px 8px 0;
  }
}

/* Tablet portrait specific */
@media (min-width: 768px) and (max-width: 834px) and (orientation: portrait) {
  .mainContentWithBothDrawers,
  .mainContentWithLeftDrawer,
  .mainContentWithRightDrawer {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  :global(.MuiDrawer-paper) {
    width: 320px !important;
    transform: translateX(-100%) !important;
  }

  :global(.MuiDrawer-paper.MuiDrawer-paperAnchorLeft.MuiDrawer-paperTemporary) {
    transform: translateX(0) !important;
  }

  :global(.MuiDrawer-paper[data-anchor='right']) {
    transform: translateX(100%) !important;
  }

  :global(
      .MuiDrawer-paper[data-anchor='right'].MuiDrawer-paperAnchorRight.MuiDrawer-paperTemporary
    ) {
    transform: translateX(0) !important;
  }
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
  .sketchbookcontainer {
    margin-top: 70px;
    height: calc(100vh - 110px);
  }

  .headerContainer {
    height: 42px;
  }

  .breadcrumbsContainer {
    top: 42px;
    height: 28px;
    padding: 0 8px;
    font-size: 11px;
  }

  .mainContent {
    padding: 10px;
    margin-bottom: 40px;
    touch-action: manipulation;
  }

  .mainContentWithBothDrawers,
  .mainContentWithLeftDrawer,
  .mainContentWithRightDrawer {
    margin-left: 0;
    margin-right: 0;
    width: 100%;
  }

  :global(.MuiDrawer-paper) {
    top: 70px !important;
    height: calc(100% - 110px) !important;
    width: 240px !important;
  }

  :global(.MuiDrawer-paper[data-anchor='left']) {
    transform: translateX(-100%) !important;
  }

  :global(.MuiDrawer-paper[data-anchor='right']) {
    transform: translateX(100%) !important;
  }
}

:global(.MuiDrawer-root.MuiDrawer-modal) :global(.MuiDrawer-paper) {
  transform: none !important;
}

.breadcrumbsContainer :global(.MuiBreadcrumbs-separator) {
  margin: 0 4px;
}

@media (max-width: 480px) {
  .sketchbookcontainer {
    margin-top: 65px;
    height: calc(100vh - 101px);
  }

  .headerContainer {
    height: 40px;
  }

  .breadcrumbsContainer {
    top: 40px;
    height: 25px;
    padding: 0 6px;
    font-size: 10px;
  }

  .mainContent {
    padding: 8px;
    margin-bottom: 36px;
  }

  :global(.MuiDrawer-paper) {
    top: 65px !important;
    height: calc(100% - 105px) !important;
    width: 85% !important;
    max-width: 280px !important;
  }

  .breadcrumbsContainer :global(.MuiBreadcrumbs-separator) {
    margin: 0 3px;
  }
}
