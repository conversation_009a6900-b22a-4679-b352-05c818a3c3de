import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UserData {
  name: string;
  email: string;
  organization: string;
}

interface AuthState {
  token: string | null;
  userDetails: UserData | null;
  profileImage: string | null;
}

const initialState: AuthState = {
  token: null,
  userDetails: null,
  profileImage: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setAuth: (
      state,
      action: PayloadAction<{ token: string; userDetails: UserData }>
    ) => {
      state.token = action.payload.token;
      state.userDetails = action.payload.userDetails;
    },
    updateProfileImageState: (state, action: PayloadAction<string>) => {
      state.profileImage = action.payload;
    },
    clearAuth: (state) => {
      state.token = null;
      state.userDetails = null;
    },
    updateUser: (state, action: PayloadAction<UserData>) => {
      if (state.userDetails) {
        state.userDetails = action.payload;
      }
    },
  },
});

export const { setAuth, updateProfileImageState, clearAuth, updateUser } =
  authSlice.actions;
export default authSlice.reducer;
