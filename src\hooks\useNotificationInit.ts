import { useEffect } from 'react';
import useLocalStorage from './useLocalStorage';

/**
 * Hook to initialize notification system
 * Simplified to only handle cleanup - API calls are handled in App.tsx
 */
export const useNotificationInit = () => {
  const [currentUser] = useLocalStorage('user', null);

  useEffect(() => {
    if (!currentUser) return;

    // Clear any existing sample data flag since we now use real API data
    // API calls are handled at the app level to prevent excessive requests
    localStorage.removeItem('notifications-initialized');
  }, [currentUser]);
};

export default useNotificationInit;
