.container {
  padding: 24px;
  width: 100%;
  max-width: 1240px; /* Increased to accommodate all cards plus padding */
  margin: 0 auto;
  color: var(--color-text-primary);
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.header h2 {
  font-size: 28px;
  margin-bottom: 8px;
  color: var(--color-text-primary);
}

.header p {
  font-size: 16px;
  color: var(--color-text-secondary);
}

.templatesGrid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  padding: 16px;
  justify-items: center;
  width: 100%;
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .container {
    padding: 32px;
    max-width: 100%;
  }

  .header h2 {
    font-size: 32px;
    margin-bottom: 12px;
  }

  .header p {
    font-size: 18px;
  }

  .templatesGrid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    padding: 20px;
    justify-items: center;
  }

  .templateCard {
    width: 240px;
    height: 300px;
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;
  }

  .templateCard:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  }

  .templateImage {
    height: 180px;
  }

  .templateInfo {
    padding: 16px;
    height: 120px;
  }

  .templateInfo h3 {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .templateInfo p {
    font-size: 14px;
    line-height: 1.5;
  }

  .deleteButton {
    padding: 8px 16px;
    font-size: 14px;
    min-height: 44px;
  }
}

/* Tablet portrait specific */
@media (min-width: 768px) and (max-width: 834px) and (orientation: portrait) {
  .templatesGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 16px;
  }

  .templateCard {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }
}

/* Tablet landscape specific */
@media (min-width: 834px) and (max-width: 1024px) and (orientation: landscape) {
  .templatesGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 20px;
  }

  .templateCard {
    width: 100%;
    max-width: 280px;
    margin: 0 auto;
  }
}

/* Desktop breakpoints */
@media (max-width: 1200px) and (min-width: 1025px) {
  .templatesGrid {
    grid-template-columns: repeat(4, 1fr);
  }

  .templateCard {
    width: 220px;
  }
}

@media (max-width: 992px) and (min-width: 1025px) {
  .templatesGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
  .templatesGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 12px;
  }

  .templateCard {
    width: 100%;
    height: 260px;
  }
}

@media (max-width: 480px) {
  .templatesGrid {
    grid-template-columns: repeat(1, 1fr);
  }

  .templateCard {
    width: 280px; /* Larger cards on mobile for better visibility */
    margin: 0 auto;
  }
}

.templateCard {
  display: flex;
  flex-direction: column;
  background: var(--color-background-secondary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.25s ease;
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  width: 215px; /* Base width */
  height: 270px; /* Slightly increased height */
  position: relative;
}

.templateImage {
  width: 100%;
  height: 140px; /* Fixed consistent height */
  background: var(--color-background-tertiary);
  position: relative;
  overflow: hidden; /* Ensure image doesn't overflow */
  border-bottom: 1px solid var(--color-border);
}

.templateImage img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Changed to cover to ensure consistent image display */
  object-position: center; /* Center the image */
  transition: transform 0.3s ease;
}

.templateCard:hover .templateImage img {
  transform: scale(1.05); /* Subtle zoom effect on hover */
}

.templateInfo {
  padding: 16px;
  height: 120px; /* Fixed height for info section */
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.templateInfo h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600; /* Increased font weight */
  color: var(--color-text-primary);
  line-height: 1.3;
  min-height: 20px; /* Minimum height instead of fixed */
  max-height: 40px; /* Allow for two lines if needed */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Allow up to 2 lines */
  -webkit-box-orient: vertical;
  white-space: normal; /* Allow wrapping */
}

.templateInfo p {
  margin: 0;
  font-size: 13px;
  color: var(--color-text-secondary);
  line-height: 1.4;
  height: auto; /* Auto height */
  max-height: 36px; /* Limit height */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Reduced to 2 lines to make room for title */
  -webkit-box-orient: vertical;
}

.templateCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary-light, #4dabf5);
}

.templateCard.disabled {
  opacity: 0.7;
  pointer-events: none;
  cursor: not-allowed;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--color-background-primary-rgb), 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.selected {
  border: 2px solid #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  height: 300px;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  height: 300px;
  text-align: center;
  background-color: var(--color-background-secondary);
  border-radius: 12px;
  margin: 0 16px;
}

.deleteButton {
  margin-top: auto;
  padding: 6px 12px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  align-self: flex-end;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 70px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.deleteButton:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.deleteButton:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

.deleteButton:disabled {
  background-color: #e0e0e0;
  color: #9e9e9e;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Custom class for template cards with longer titles */
.customTemplateCard .templateInfo h3 {
  font-weight: 600;
  font-size: 15px;
  max-height: 40px; /* Allow more space for title */
  -webkit-line-clamp: 2;
}

/* Media queries for responsive design */
@media (max-width: 992px) {
  .templateCard {
    width: 200px;
  }

  .templateInfo {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .templatesGrid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    padding: 12px;
  }

  .templateCard {
    width: 100%;
    max-width: 220px;
    margin: 0 auto;
  }

  .templateInfo {
    padding: 12px;
  }

  .templateInfo h3 {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .templatesGrid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
  }

  .container {
    padding: 12px;
  }

  .templateCard {
    width: 100%;
    max-width: 100%;
    height: 260px;
  }

  .templateImage {
    height: 130px;
  }

  .templateInfo {
    padding: 10px;
    height: 110px;
  }

  .deleteButton {
    padding: 5px 10px;
    font-size: 11px;
  }
}
