.toast-viewer-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
}

/* Ensure the viewer fits within its container */
.toast-viewer-wrapper .toastui-editor-contents {
  width: 100%;
  height: 100%;
  padding: 0;
  font-size: inherit;
  font-family: var(--font-family-primary);
  line-height: 1.5;
  color: var(--color-text-primary);
}

/* Adjust headings */
.toast-viewer-wrapper .toastui-editor-contents h1,
.toast-viewer-wrapper .toastui-editor-contents h2,
.toast-viewer-wrapper .toastui-editor-contents h3,
.toast-viewer-wrapper .toastui-editor-contents h4,
.toast-viewer-wrapper .toastui-editor-contents h5,
.toast-viewer-wrapper .toastui-editor-contents h6 {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: var(--color-text-primary);
  font-weight: bold;
}

/* Adjust paragraphs */
.toast-viewer-wrapper .toastui-editor-contents p {
  margin: 0.5em 0;
}

/* Adjust lists */
.toast-viewer-wrapper .toastui-editor-contents ul,
.toast-viewer-wrapper .toastui-editor-contents ol {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

.toast-viewer-wrapper .toastui-editor-contents ul li,
.toast-viewer-wrapper .toastui-editor-contents ol li {
  margin-bottom: 0.3em;
}

/* Task lists */
.toast-viewer-wrapper .toastui-editor-contents ul.task-list {
  list-style-type: none;
  padding-left: 0.5em;
}

.toast-viewer-wrapper .toastui-editor-contents ul.task-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5em;
}

.toast-viewer-wrapper
  .toastui-editor-contents
  ul.task-list
  li
  input[type='checkbox'] {
  margin-right: 0.5em;
  margin-top: 0.3em;
}

/* Adjust code blocks */
.toast-viewer-wrapper .toastui-editor-contents pre {
  background-color: var(--color-grey-100);
  padding: 0.5em;
  border-radius: 4px;
  margin: 0.5em 0;
  color: var(--color-text-primary);
  font-family: var(--font-family-code);
}

.toast-viewer-wrapper .toastui-editor-contents code {
  background-color: var(--color-grey-100);
  padding: 0.1em 0.3em;
  border-radius: 3px;
  font-family: var(--font-family-code);
  color: var(--color-text-primary);
}

/* Adjust tables */
.toast-viewer-wrapper .toastui-editor-contents table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
  table-layout: fixed;
  display: table;
  max-width: 100%;
  overflow-x: auto;
}

/* Fix table overflow on small screens */
.toast-viewer-wrapper .toastui-editor-contents div.table-container {
  overflow-x: auto;
  max-width: 100%;
  display: block;
  margin: 0.5em 0;
}

.toast-viewer-wrapper .toastui-editor-contents thead {
  display: table-header-group;
  vertical-align: middle;
  border-color: inherit;
}

.toast-viewer-wrapper .toastui-editor-contents tbody {
  display: table-row-group;
  vertical-align: middle;
  border-color: inherit;
}

.toast-viewer-wrapper .toastui-editor-contents tr {
  display: table-row;
  vertical-align: inherit;
  border-color: inherit;
}

.toast-viewer-wrapper .toastui-editor-contents th,
.toast-viewer-wrapper .toastui-editor-contents td {
  border: 1px solid var(--color-border);
  padding: 0.5em;
  word-break: break-word;
  overflow-wrap: break-word;
  text-align: left;
  vertical-align: middle;
}

/* Light theme table headers */
.toast-viewer-wrapper .toastui-editor-contents th {
  background-color: var(
    --color-grey-200
  ); /* Using theme color for consistency */
  color: var(--color-text-primary); /* Using theme color for consistency */
  font-weight: bold;
}

/* Images */
.toast-viewer-wrapper .toastui-editor-contents img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em auto;
}

/* Links */
.toast-viewer-wrapper .toastui-editor-contents a {
  color: var(--color-primary);
  text-decoration: underline;
  transition: color 0.2s ease;
}

.toast-viewer-wrapper .toastui-editor-contents a:hover {
  color: var(--color-primary-dark);
}

/* Horizontal rule */
.toast-viewer-wrapper .toastui-editor-contents hr {
  border: none;
  border-top: 1px solid var(--color-border);
  margin: 1.5em 0;
}

/* Dark theme adjustments */
.toast-viewer-wrapper.dark-theme .toastui-editor-contents {
  color: var(--color-text-primary-dark, #eee);
}

.toast-viewer-wrapper.dark-theme .toastui-editor-contents h1,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents h2,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents h3,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents h4,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents h5,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents h6 {
  color: #ffffff; /* Explicit white for better visibility in dark mode */
  font-weight: bold;
}

.toast-viewer-wrapper.dark-theme .toastui-editor-contents pre,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents code {
  background-color: #2d2d2d; /* Darker background for better contrast in dark mode */
  color: #e0e0e0; /* Light gray text for better visibility in dark mode */
  font-family: var(--font-family-code);
}

.toast-viewer-wrapper.dark-theme .toastui-editor-contents th,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents td {
  border-color: var(--color-border-dark, #444);
}

/* Dark theme table headers */
.toast-viewer-wrapper.dark-theme .toastui-editor-contents th {
  background-color: #3a3a3a; /* Darker background for better contrast in dark mode */
  color: #ffffff; /* White text for better visibility in dark mode */
  font-weight: bold;
}

.toast-viewer-wrapper.dark-theme .toastui-editor-contents a {
  color: var(--color-primary-dark, #6ba5e0);
}

.toast-viewer-wrapper.dark-theme .toastui-editor-contents a:hover {
  color: var(--color-primary-light, #8bbcf0);
}

.toast-viewer-wrapper.dark-theme .toastui-editor-contents hr {
  border-top-color: var(--color-border-dark, #444);
}

/* KaTeX Math Styling */
.toast-viewer-wrapper .katex-display-block {
  /* margin: 1rem 0; */
  text-align: center;
  overflow-x: auto;
  /* padding: 0.5rem; */
  /* background-color: var(--color-background-secondary, #f8f9fa); */
  border-radius: 4px;
  /* border-left: 4px solid var(--color-primary, #007bff); */
}

.toast-viewer-wrapper .katex {
  font-size: 1.1em;
  color: var(--color-text-primary);
}

.toast-viewer-wrapper .katex-display {
  margin: 0.5rem 0;
}

/* Dark theme KaTeX styling */
.toast-viewer-wrapper.dark-theme .katex-display-block {
  /* background-color: #2d2d2d; */
  border-left-color: var(--color-primary-dark, #6ba5e0);
}

.toast-viewer-wrapper.dark-theme .katex {
  color: #e0e0e0;
}

/* Inline KaTeX styling */
.toast-viewer-wrapper .katex-inline {
  display: inline;
}

.toast-viewer-wrapper .katex-inline .katex {
  display: inline;
}
