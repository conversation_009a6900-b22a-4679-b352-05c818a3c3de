import React from 'react';
import styles from './Activities.module.css';
import close from '../../../assets/images/Cross.png';

export interface Task {
  id: number;
  text: string;
  completed: boolean;
}

export interface ActivityItem {
  id: number;
  name: string;
  action: string;
  comment?: string;
  tasks?: Task[];
}

interface ActivitiesProps {
  activities: ActivityItem[];
  onClose: () => void;
  onToggleTaskCompletion: (activityId: number, taskId: number) => void;
  onToggleCompleteAll: (activityId: number, completed: boolean) => void; // Add this prop
}

const Activities: React.FC<ActivitiesProps> = ({
  activities,
  onClose,
  onToggleTaskCompletion,
  onToggleCompleteAll,
}) => {
  return (
    <div className={styles.activities}>
      <div className={styles.activitiesHeader}>
        <h2>Activities</h2>
        <button className={styles.closeButton} onClick={onClose}>
          <img src={close} alt="Close" />
        </button>
      </div>
      {activities.map((activity) => (
        <div key={activity.id} className={styles.activityItem}>
          <div className={styles.activityUser}>
            <span className={styles.userIcon}></span>
            <span className={styles.userName}>{activity.name}</span>
            <span className={styles.userAction}>{activity.action}</span>
          </div>
          {activity.comment && (
            <div className={styles.activityComment}>
              <p>Commented</p>
              <p>{activity.comment}</p>
            </div>
          )}
          {activity.tasks && (
            <div className={styles.activityTasks}>
              <div className={styles.assigned}>
                <p>Assigned Tasks</p>
                <label className={styles.completeAll}>
                  <input
                    type="checkbox"
                    checked={activity.tasks.every(task => task.completed)}
                    onChange={(e) =>
                      onToggleCompleteAll(activity.id, e.target.checked)
                    }
                  />{" "}
                  Complete All
                </label>
              </div>
              {activity.tasks.map((task) => (
                <div key={task.id} className={styles.taskItem} style={{
                  border: '1px'
                }}>
                  <input
                    type="checkbox"
                    checked={task.completed}
                    onChange={() =>
                      onToggleTaskCompletion(activity.id, task.id)
                    }
                  />
                  <span className={task.completed ? styles.completed : ''}>
                    {task.text}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default Activities;
