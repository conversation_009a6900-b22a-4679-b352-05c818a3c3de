export interface PredefinedFile {
  id: string;
  name: string;
  description: string;
  category: string;
  fileType: string;
  mimeType: string;
  size: string;
  pageCount?: number;
  lastModified: string;
  tags: string[];
  suggestedQuestions: string[];
  previewUrl?: string;
  thumbnailUrl?: string;
  hasContext: boolean; // Indicates if AI model already has context for this file
  contextDescription?: string; // Description of what context the AI has
  fullPath?: string; // Full path to the file on the server (for API files)
}

export interface PredefinedFileCategory {
  id: string;
  name: string;
  description: string;
  files: PredefinedFile[];
}
