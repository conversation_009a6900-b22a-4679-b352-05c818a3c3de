import { toast } from 'react-hot-toast';

/**
 * Show appropriate notification based on network status change
 */
export const notifyNetworkStatusChange = (isOnline: boolean): void => {
  if (isOnline) {
    toast.success('You are back online!', {
      id: 'network-status',
      duration: 3000,
      icon: '🌐',
    });
  } else {
    toast.error('You are offline. Some features may be limited.', {
      id: 'network-status',
      duration: 5000,
      icon: '📶',
    });
  }
};

/**
 * Check if the connection is slow
 */
export const isSlowConnection = (): boolean => {
  const connection =
    'connection' in navigator ? (navigator as any).connection : null;

  if (!connection) return false;

  return (
    connection.effectiveType === 'slow-2g' ||
    connection.effectiveType === '2g' ||
    (connection.downlink > 0 && connection.downlink < 0.5)
  );
};

/**
 * Show notification for slow connection
 */
export const notifySlowConnection = (): void => {
  toast(
    'Your internet connection is slow. Some features may take longer to load.',
    {
      id: 'slow-connection',
      duration: 5000,
      icon: '🐢',
      style: {
        background: '#fff3cd', // light yellow
        color: '#856404', // dark yellow-brown text
        border: '1px solid #ffeeba', // yellow border
      },
    }
  );
};
