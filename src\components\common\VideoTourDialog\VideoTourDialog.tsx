import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import YouTubeEmbed from '../YouTubeEmbed/YouTubeEmbed';
import styles from './VideoTourDialog.module.css';

interface VideoTourDialogProps {
  open: boolean;
  onClose: () => void;
  videoId: string;
  title: string;
  description?: string;
}

/**
 * A dialog component for displaying video tours
 */
const VideoTourDialog: React.FC<VideoTourDialogProps> = ({
  open,
  onClose,
  videoId,
  title,
  description,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      className={styles.dialog}
    >
      <DialogTitle className={styles.dialogTitle}>
        <div className={styles.titleContent}>
          <Typography variant="h6">{title}</Typography>
        </div>
        <IconButton
          aria-label="close"
          onClick={onClose}
          className={styles.closeButton}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent className={styles.dialogContent}>
        {description && (
          <Typography variant="body1" className={styles.description}>
            {description}
          </Typography>
        )}
        <div className={styles.videoContainer}>
          <YouTubeEmbed videoId={videoId} autoplay={true} />
        </div>
      </DialogContent>
      
      <DialogActions className={styles.dialogActions}>
        <Button
          onClick={onClose}
          color="primary"
          variant="contained"
          className={styles.closeButton}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default VideoTourDialog;
