.dialogPaper {
  border-radius: 16px;
  overflow: hidden;
  background-color: var(--color-background-primary);
  width: 1280px !important; /* Increased from 1200px to accommodate all cards */
  max-width: 95vw !important; /* Ensure it doesn't overflow viewport */
  max-height: 90vh;
}

.dialogContent {
  padding: 0 !important;
  overflow-x: hidden;
}

.closeButtonContainer {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

.closeButton {
  background-color: var(--color-background-secondary) !important;
  color: var(--color-text-primary) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.closeButton:hover {
  background-color: var(--color-background-tertiary) !important;
  transform: scale(1.05);
}
