import React, { useState } from 'react';
import { EnhancedMarkdownRenderer } from '../common/chips/Markdown';

const EnhancedMarkdownTest: React.FC = () => {
  const [selectedExample, setSelectedExample] = useState(0);

  const examples = [
    {
      title: 'Greek Letters & LaTeX',
      content: `# Greek Letters and LaTeX Test

## Auto-conversion of Greek Letters
- Alpha and Beta should convert to: $\\alpha$ and $\\beta$
- Gamma, Delta, Epsilon: $\\gamma$, $\\delta$, $\\epsilon$
- Mixed case: alpha, BETA, Gamma should all convert

## LaTeX Math
Inline math: $E = mc^2$ and $\\sum_{i=1}^{n} x_i$

Display math:
$$\\int_0^1 f(x)dx = \\lim_{n \\to \\infty} \\sum_{i=1}^{n} f(x_i) \\Delta x$$

## Currency Protection
- Price: $100.50 (should not render as math)
- Range: $10-$20 (should not render as math)
- With commas: $1,000.99 (should not render as math)
- Mixed: The formula $E = mc^2$ costs $50 to compute`
    },
    {
      title: 'Tables & Checkboxes',
      content: `# Tables and Checkboxes

## Enhanced Table
| Feature | Status | Priority |
|---------|--------|----------|
| LaTeX Support | ✅ Complete | High |
| Table Styling | ✅ Complete | Medium |
| Checkboxes | ✅ Complete | Low |
| Code Highlighting | ✅ Complete | High |

## Task Lists
- [x] Implement LaTeX preprocessing
- [x] Add Greek letter conversion
- [x] Protect currency symbols
- [ ] Add inline HTML support
- [ ] Add theme switching
- [x] Enhance table styling

## Strikethrough
~~This text is crossed out~~ but this is normal.`
    },
    {
      title: 'Code Blocks & Syntax Highlighting',
      content: `# Code Blocks and Syntax Highlighting

## JavaScript/TypeScript
\`\`\`javascript
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

const result = fibonacci(10);
console.log(result);
\`\`\`

## Python
\`\`\`python
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quicksort(left) + middle + quicksort(right)
\`\`\`

## Auto-detected Language (no language specified)
\`\`\`
SELECT users.name, COUNT(orders.id) as order_count
FROM users
LEFT JOIN orders ON users.id = orders.user_id
GROUP BY users.id
ORDER BY order_count DESC;
\`\`\`

## Inline Code
Use \`const variable = "value"\` for inline code snippets.`
    },
    {
      title: 'Links & HTML',
      content: `# Links and HTML Elements

## External Links
- [Google](https://google.com) (opens in new tab)
- [GitHub](https://github.com) (opens in new tab)

## Internal Links
- [Documentation](/docs/readme)
- [Help Page](/help)

## Inline HTML (basic support)
This is <b>bold text</b> and this is <i>italic text</i>.

<div style="background-color: #f0f0f0; padding: 10px; border-radius: 5px;">
This is a custom HTML div with styling.
</div>

## Blockquotes
> This is a blockquote with **bold** and *italic* text.
> 
> It can span multiple lines and include other markdown elements.`
    },
    {
      title: 'Complex Mixed Content',
      content: `# Complex Mixed Content Example

## Mathematical Formulas with Greek Letters
The quadratic formula uses Alpha and Beta coefficients:
$$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$

Where Alpha = $a$, Beta = $b$, and the discriminant is $\\Delta = b^2 - 4ac$.

## Data Analysis Table
| Algorithm | Time Complexity | Space | Cost |
|-----------|----------------|-------|------|
| QuickSort | $O(n \\log n)$ | $O(\\log n)$ | $50 |
| MergeSort | $O(n \\log n)$ | $O(n)$ | $75 |
| HeapSort | $O(n \\log n)$ | $O(1)$ | $60 |

## Implementation Checklist
- [x] Implement sorting algorithms
- [x] Add complexity analysis
- [x] Performance testing ($1,000 budget)
- [ ] Documentation review
- [ ] Code optimization

## Code Example with Math
\`\`\`python
import numpy as np

def gaussian_function(x, mu=0, sigma=1):
    """
    Gaussian function: f(x) = (1/σ√(2π)) * e^(-½((x-μ)/σ)²)
    """
    coefficient = 1 / (sigma * np.sqrt(2 * np.pi))
    exponent = -0.5 * ((x - mu) / sigma) ** 2
    return coefficient * np.exp(exponent)

# Example usage
x_values = np.linspace(-3, 3, 100)
y_values = gaussian_function(x_values)
\`\`\`

The mathematical representation: $f(x) = \\frac{1}{\\sigma\\sqrt{2\\pi}} e^{-\\frac{1}{2}\\left(\\frac{x-\\mu}{\\sigma}\\right)^2}$`
    }
  ];

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Enhanced Markdown Renderer Test</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <label htmlFor="example-select" style={{ marginRight: '10px' }}>
          Select Example:
        </label>
        <select
          id="example-select"
          value={selectedExample}
          onChange={(e) => setSelectedExample(Number(e.target.value))}
          style={{
            padding: '8px 12px',
            borderRadius: '4px',
            border: '1px solid #ccc',
            fontSize: '14px'
          }}
        >
          {examples.map((example, index) => (
            <option key={index} value={index}>
              {example.title}
            </option>
          ))}
        </select>
      </div>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: '1fr 1fr', 
        gap: '20px',
        minHeight: '600px'
      }}>
        <div>
          <h3>Source Markdown:</h3>
          <pre style={{
            backgroundColor: '#f5f5f5',
            padding: '15px',
            borderRadius: '8px',
            overflow: 'auto',
            fontSize: '12px',
            lineHeight: '1.4',
            border: '1px solid #ddd',
            maxHeight: '600px'
          }}>
            {examples[selectedExample].content}
          </pre>
        </div>
        
        <div>
          <h3>Rendered Output:</h3>
          <div style={{
            border: '1px solid #ddd',
            borderRadius: '8px',
            padding: '15px',
            backgroundColor: '#fff',
            maxHeight: '600px',
            overflow: 'auto'
          }}>
            <EnhancedMarkdownRenderer
              content={examples[selectedExample].content}
              enableGreekConversion={true}
              enableCurrencyProtection={true}
              enableLatexPreprocessing={true}
              baseUrl="https://example.com"
            />
          </div>
        </div>
      </div>

      <div style={{ marginTop: '30px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <h3>Features Demonstrated:</h3>
        <ul>
          <li><strong>LaTeX Preprocessing:</strong> Fixes malformed patterns like $$$ and \\alpha$$</li>
          <li><strong>Greek Letter Conversion:</strong> Auto-converts "Alpha", "Beta" etc. to LaTeX symbols</li>
          <li><strong>Currency Protection:</strong> Prevents $100, $1,000.50 from being rendered as math</li>
          <li><strong>Enhanced Tables:</strong> Better styling with hover effects and responsive design</li>
          <li><strong>Syntax Highlighting:</strong> Auto-detects languages and provides syntax highlighting</li>
          <li><strong>Task Lists:</strong> Proper checkbox rendering with custom styling</li>
          <li><strong>Link Enhancement:</strong> External links open in new tabs with indicators</li>
          <li><strong>Fallback Rendering:</strong> Graceful error handling with detailed error messages</li>
          <li><strong>Responsive Design:</strong> Mobile-friendly layouts and typography</li>
        </ul>
      </div>
    </div>
  );
};

export default EnhancedMarkdownTest;
