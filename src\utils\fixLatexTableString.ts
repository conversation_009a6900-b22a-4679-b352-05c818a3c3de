export function fixLatexTableString(input: string) {
  return (
    input
      // Fix \ ext → \text
      .replace(/\\\s*ext/g, '\\text')
      // Fix \ begin → \begin and \ end → \end
      .replace(/\\\s*begin/g, '\\begin')
      .replace(/\\\s*end/g, '\\end')
      // Replace multiple backslashes with proper ones
      .replace(/\\\\\\hline/g, '\\\\ \\hline')
      .replace(/\\\\\\\\/g, '\\\\') // extra newlines
      // Optional: trim each row
      .replace(/\s+&\s+/g, ' & ')
      .replace(/\\\\\s*/g, '\\\\\n') // for better formatting
      // Final cleanup
      .trim()
  );
}

const rawLatex = `...your malformed LaTeX here...`;
const fixedLatex = fixLatexTableString(rawLatex);

console.log(fixedLatex);
