import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
import {
  useCreateNotificationMutation,
  useGetAllNotificationsQuery,
  useMarkNotificationAsReadMutation,
  ApiNotificationResponse,
} from '../services/notificationService';
import {
  setNotifications,
  addNotification,
  markAsRead,
  markAllAsRead,
  setLoading,
  setError,
  setCreating,
  setMarkingAsRead,
  selectNotifications,
  selectUnreadCount,
  selectActionRequiredNotifications,
} from '../store/slices/notificationSlice';
import { WorkflowNotification } from '../types/workflow/index';
import { getCurrentUserId } from '../utils/auth/userHelpers';
import {
  generateNotificationMessage,
  showWorkflowToast,
  isActionRequired,
} from '../utils/notificationUtils';

/**
 * Enhanced notifications hook that integrates with real backend APIs
 */
export const useNotificationsAPI = () => {
  const dispatch = useDispatch();
  const currentUserId = getCurrentUserId();

  // Redux state
  const notifications = useSelector(selectNotifications);
  const unreadCount = useSelector(selectUnreadCount);
  const actionRequiredNotifications = useSelector(
    selectActionRequiredNotifications
  );
  const { isLoading, error, isCreating, isMarkingAsRead } = useSelector(
    (state: RootState) => state.notifications
  );

  // API hooks
  const {
    data: notificationsData,
    error: fetchError,
    isLoading: isFetching,
    refetch: refetchNotifications,
  } = useGetAllNotificationsQuery(currentUserId || '', {
    skip: !currentUserId,
    // pollingInterval: 30000, // Poll every 30 seconds for real-time updates
  });

  const [createNotification, { isLoading: isCreatingNotification }] =
    useCreateNotificationMutation();
  const [markAsReadAPI, { isLoading: isMarkingAsReadAPI }] =
    useMarkNotificationAsReadMutation();

  // Transform backend notification to frontend format
  const transformNotification = useCallback(
    (
      apiNotification: ApiNotificationResponse,
      currentUserId: string
    ): WorkflowNotification => {
      // Find the recipient data for the current user
      const userRecipient = apiNotification.recipients.find(
        (r) => r.userId === currentUserId
      );

      // Determine notification type based on content
      let type: 'assignment' | 'status_change' | 'workflow_created' =
        'workflow_created';
      if (apiNotification.content.includes('assigned to review')) {
        type = 'assignment';
      } else if (
        apiNotification.content.includes('has been approved') ||
        apiNotification.content.includes('has been rejected') ||
        apiNotification.content.includes('has been conditionally approved')
      ) {
        type = 'status_change';
      }

      return {
        id: apiNotification.id,
        type: apiNotification.type || type,
        workflowId: apiNotification.workflowId,
        workflowTitle: apiNotification.title || 'Workflow',
        message: apiNotification.content,
        actionRequired:
          type === 'assignment' || apiNotification.actionRequired || false,
        status: apiNotification.status,
        createdAt: apiNotification.createdOn,
        read: !!userRecipient?.readAt,
        readAt: userRecipient?.readAt || undefined,
        userId: currentUserId,
        priority: apiNotification.priority || 'Medium',
        title: apiNotification.title,
        content: apiNotification.content,
      };
    },
    []
  );

  // Update Redux state when API data changes
  useEffect(() => {
    if (notificationsData?.data && currentUserId) {
      const filteredNotifications = notificationsData.data.filter(
        (notification) =>
          notification.recipients.some(
            (recipient) => recipient.userId === currentUserId
          )
      );

      const transformedNotifications = filteredNotifications.map(
        (notification) => transformNotification(notification, currentUserId)
      );

      dispatch(setNotifications(transformedNotifications));
    } else {
      dispatch(setNotifications([]));
    }
  }, [notificationsData, transformNotification, dispatch, currentUserId]);

  // Update loading states
  useEffect(() => {
    dispatch(setLoading(isFetching));
  }, [isFetching, dispatch]);

  useEffect(() => {
    dispatch(setCreating(isCreatingNotification));
  }, [isCreatingNotification, dispatch]);

  useEffect(() => {
    dispatch(setMarkingAsRead(isMarkingAsReadAPI));
  }, [isMarkingAsReadAPI, dispatch]);

  // Update error state
  useEffect(() => {
    if (fetchError) {
      dispatch(setError('Failed to fetch notifications'));
    } else {
      dispatch(setError(null));
    }
  }, [fetchError, dispatch]);

  // Helper function to determine notification target
  const determineNotificationTarget = useCallback(
    (
      targetUserId: string,
      workflowCreatorId?: string,
      notificationType?: 'assignment' | 'status_change' | 'workflow_created'
    ): 'participants' | 'creator' | 'both' => {
      // For workflow creation, always notify participants only
      if (notificationType === 'workflow_created') {
        return 'participants';
      }

      // For assignments, notify participants
      if (notificationType === 'assignment') {
        return 'participants';
      }

      // For status changes, determine based on target
      if (notificationType === 'status_change') {
        if (workflowCreatorId && targetUserId === workflowCreatorId) {
          return 'creator'; // Notify creator about status changes
        }
        return 'participants'; // Notify participants about assignments/updates
      }

      // Default fallback
      return 'participants';
    },
    []
  );

  // Create notification via API with targeting support
  const createNotificationAPI = useCallback(
    async (
      workflowId: string,
      title: string,
      content: string,
      notifyTarget: 'participants' | 'creator' | 'both' = 'participants'
    ) => {
      try {
        const result = await createNotification({
          workflowId,
          title,
          content,
          notifyTarget,
        }).unwrap();

        // Only refetch if we have a current user (query is not skipped)
        if (currentUserId) {
          refetchNotifications();
        }

        return result;
      } catch (error) {
        console.error('Failed to create notification:', error);
        dispatch(setError('Failed to create notification'));
        throw error;
      }
    },
    [createNotification, refetchNotifications, dispatch, currentUserId]
  );

  // Mark notification as read via API
  const markNotificationAsReadAPI = useCallback(
    async (notificationId: string) => {
      if (!currentUserId) return;

      try {
        await markAsReadAPI({
          notificationId,
          userId: currentUserId,
        }).unwrap();

        // Update local state immediately for better UX
        dispatch(markAsRead(notificationId));

        // Refetch to ensure consistency (only if query is active)
        if (currentUserId) {
          refetchNotifications();
        }
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
        dispatch(setError('Failed to mark notification as read'));
      }
    },
    [markAsReadAPI, currentUserId, dispatch, refetchNotifications]
  );

  // Create assignment notification (maintains backward compatibility)
  const createAssignmentNotification = useCallback(
    async (
      workflowId: string,
      workflowTitle: string,
      assignedUserId: string,
      creatorName?: string,
      priority?: 'Low' | 'Medium' | 'High'
    ) => {
      const message = generateNotificationMessage(
        'assignment',
        workflowTitle,
        creatorName
      );

      // Assignment notifications go to participants (the assigned user)
      const notifyTarget = 'participants';

      try {
        await createNotificationAPI(
          workflowId,
          workflowTitle,
          message,
          notifyTarget
        );
      } catch (error) {
        console.error('Failed to create assignment notification:', error);
      }
    },
    [createNotificationAPI]
  );

  // Create status change notification with proper targeting
  const createStatusChangeNotification = useCallback(
    async (
      workflowId: string,
      workflowTitle: string,
      status: 'approved' | 'rejected' | 'conditional' | 'in-progress',
      targetUserId: string,
      creatorName?: string,
      priority?: 'Low' | 'Medium' | 'High',
      workflowCreatorId?: string
    ) => {
      const message = generateNotificationMessage(
        'status_change',
        workflowTitle,
        creatorName,
        status
      );

      // Determine notification target based on who should receive it
      let notifyTarget: 'participants' | 'creator' | 'both';

      if (workflowCreatorId && targetUserId === workflowCreatorId) {
        // Notify creator about status changes from participants
        notifyTarget = 'creator';
      } else {
        // Notify participants about assignments or workflow updates
        notifyTarget = 'participants';
      }

      try {
        await createNotificationAPI(
          workflowId,
          workflowTitle,
          message,
          notifyTarget
        );

        // Show toast for current user
        if (targetUserId === currentUserId) {
          showWorkflowToast('status_change', message, priority);
        }
      } catch (error) {
        console.error('Failed to create status change notification:', error);
      }
    },
    [createNotificationAPI, currentUserId]
  );

  // Create workflow created notification (maintains backward compatibility)
  const createWorkflowCreatedNotification = useCallback(
    async (
      workflowId: string,
      workflowTitle: string,
      participantUserIds: string[],
      creatorName?: string,
      priority?: 'Low' | 'Medium' | 'High'
    ) => {
      const message = generateNotificationMessage(
        'workflow_created',
        workflowTitle,
        creatorName
      );

      // Workflow creation notifications go to participants only (not creator)
      const notifyTarget = 'participants';

      try {
        await createNotificationAPI(
          workflowId,
          workflowTitle,
          message,
          notifyTarget
        );
      } catch (error) {
        console.error('Failed to create workflow created notification:', error);
      }
    },
    [createNotificationAPI]
  );

  // Mark all notifications as read
  const markAllNotificationsAsRead = useCallback(async () => {
    const unreadNotifications = notifications.filter((n) => !n.read);

    try {
      // Mark all unread notifications as read via API
      await Promise.all(
        unreadNotifications.map((notification) =>
          markNotificationAsReadAPI(notification.id)
        )
      );

      // Update local state
      dispatch(markAllAsRead());
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }, [notifications, markNotificationAsReadAPI, dispatch]);

  // Refresh notifications manually
  const refreshNotifications = useCallback(() => {
    if (currentUserId) {
      refetchNotifications();
    }
  }, [refetchNotifications, currentUserId]);

  return {
    // State
    notifications,
    unreadCount,
    actionRequiredNotifications,
    isLoading,
    error,
    isCreating,
    isMarkingAsRead,

    // API Actions
    createNotificationAPI,
    markNotificationAsReadAPI,
    refreshNotifications,

    // Backward Compatible Actions
    createAssignmentNotification,
    createStatusChangeNotification,
    createWorkflowCreatedNotification,
    markAllNotificationsAsRead,

    // Utility
    refetchNotifications,
  };
};
