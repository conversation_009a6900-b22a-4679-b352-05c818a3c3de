/**
 * Payment Feature Flags
 * 
 * This file controls which payment features are enabled/disabled
 * to prevent 404 errors and allow gradual feature rollout.
 */

/**
 * Feature flags for payment functionality
 */
export const PAYMENT_FEATURES = {
  // Core features (should always be enabled)
  PLANS_API: true,
  PAYMENT_INITIATION: true,
  
  // Optional features (can be disabled if backend doesn't support them)
  PAYMENT_HISTORY: false, // Disabled due to 404 errors
  PAYMENT_METHODS: false, // Not available in current backend
  CUSTOMER_MANAGEMENT: false, // Not available in current backend
  PAYMENT_DETAILS: false, // Not available in current backend
  
  // Legacy Stripe features (disabled)
  LEGACY_PAYMENT_INTENT: false,
  LEGACY_PAYMENT_CONFIRMATION: false,
} as const;

/**
 * Get feature flag value
 */
export const isFeatureEnabled = (feature: keyof typeof PAYMENT_FEATURES): boolean => {
  return PAYMENT_FEATURES[feature];
};

/**
 * Check if payment history should be fetched
 */
export const shouldFetchPaymentHistory = (): boolean => {
  return isFeatureEnabled('PAYMENT_HISTORY');
};

/**
 * Check if payment methods should be fetched
 */
export const shouldFetchPaymentMethods = (): boolean => {
  return isFeatureEnabled('PAYMENT_METHODS');
};

/**
 * Get available payment features for debugging
 */
export const getAvailableFeatures = () => {
  const available = Object.entries(PAYMENT_FEATURES)
    .filter(([_, enabled]) => enabled)
    .map(([feature, _]) => feature);
    
  const disabled = Object.entries(PAYMENT_FEATURES)
    .filter(([_, enabled]) => !enabled)
    .map(([feature, _]) => feature);
    
  return {
    available,
    disabled,
    total: Object.keys(PAYMENT_FEATURES).length
  };
};

/**
 * Log current feature status
 */
export const logPaymentFeatureStatus = () => {
  const features = getAvailableFeatures();
  
  console.log('=== Payment Feature Status ===');
  console.log('✅ Available features:', features.available);
  console.log('❌ Disabled features:', features.disabled);
  console.log(`Total: ${features.available.length}/${features.total} features enabled`);
  console.log('===============================');
};

/**
 * Instructions for enabling payment history
 */
export const ENABLE_PAYMENT_HISTORY_INSTRUCTIONS = `
To enable payment history when the backend is ready:

1. Verify the backend endpoint works:
   - Test: GET /api/v1/payments/get-all?page=1&size=10
   - Should return 200 status, not 404

2. Update feature flag:
   - Change PAYMENT_HISTORY: false to PAYMENT_HISTORY: true in paymentFeatureFlags.ts

3. Update usePayment hook:
   - Change shouldFetchHistory from false to shouldFetchPaymentHistory()
   - Change skip from true to !shouldFetchHistory

4. Test the integration:
   - Check browser console for errors
   - Verify payment history loads correctly
`;

// Export for console debugging
(window as any).logPaymentFeatureStatus = logPaymentFeatureStatus;
(window as any).getAvailableFeatures = getAvailableFeatures;
(window as any).ENABLE_PAYMENT_HISTORY_INSTRUCTIONS = ENABLE_PAYMENT_HISTORY_INSTRUCTIONS;
