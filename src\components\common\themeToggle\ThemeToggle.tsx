import React from 'react';
import { useTheme } from '../../../contexts/ThemeContext';
import { IconButton, Tooltip } from '@mui/material';
import { DarkMode, LightMode } from '@mui/icons-material';
import './ThemeToggle.css';

interface ThemeToggleProps {
  showTooltip?: boolean;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ showTooltip = true }) => {
  const { isDarkMode, toggleTheme } = useTheme();

  const button = (
    <IconButton 
      onClick={toggleTheme} 
      className="theme-toggle-button"
      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      {isDarkMode ? (
        <LightMode className="theme-toggle-icon light" />
      ) : (
        <DarkMode className="theme-toggle-icon dark" />
      )}
    </IconButton>
  );

  if (showTooltip) {
    return (
      <Tooltip title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}>
        {button}
      </Tooltip>
    );
  }

  return button;
};

export default ThemeToggle;
