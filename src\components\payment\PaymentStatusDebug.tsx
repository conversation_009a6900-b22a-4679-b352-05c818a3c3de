/**
 * Payment Status Debug Component
 * 
 * This component helps debug payment integration issues
 * and shows the status of different payment features.
 */

import React from 'react';
import { Box, Typography, Alert, Chip, Paper } from '@mui/material';
import { usePayment } from '../../hooks/payment/usePayment';
import { getAvailableFeatures } from '../../utils/payment/paymentFeatureFlags';

const PaymentStatusDebug: React.FC = () => {
  const {
    plans,
    plansLoading,
    hasError,
    errorMessage,
    hasHistoryError,
    historyErrorMessage,
    paymentHistory,
    isLoading,
  } = usePayment();

  const features = getAvailableFeatures();

  return (
    <Box sx={{ p: 2, maxWidth: 800 }}>
      <Typography variant="h5" gutterBottom>
        Payment Integration Debug Status
      </Typography>

      {/* Core Functionality Status */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Core Functionality
        </Typography>
        
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2">
            Plans Loading: {plansLoading ? '🔄 Loading...' : '✅ Complete'}
          </Typography>
        </Box>
        
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2">
            Plans Available: {plans.length > 0 ? `✅ ${plans.length} plans` : '❌ No plans'}
          </Typography>
        </Box>
        
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2">
            Core Errors: {hasError ? `❌ ${errorMessage}` : '✅ No errors'}
          </Typography>
        </Box>

        {plans.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" fontWeight="bold">Available Plans:</Typography>
            {plans.map((plan) => (
              <Chip
                key={plan.id}
                label={`${plan.name} - $${plan.price}`}
                size="small"
                sx={{ mr: 1, mt: 1 }}
                color={plan.isPopular ? 'primary' : 'default'}
              />
            ))}
          </Box>
        )}
      </Paper>

      {/* Optional Features Status */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Optional Features
        </Typography>
        
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2">
            Payment History: {hasHistoryError ? `❌ ${historyErrorMessage}` : `✅ ${paymentHistory.length} records`}
          </Typography>
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" fontWeight="bold">Feature Flags:</Typography>
          <Box sx={{ mt: 1 }}>
            {features.available.map((feature) => (
              <Chip
                key={feature}
                label={feature}
                size="small"
                color="success"
                sx={{ mr: 1, mb: 1 }}
              />
            ))}
          </Box>
          <Box>
            {features.disabled.map((feature) => (
              <Chip
                key={feature}
                label={feature}
                size="small"
                color="default"
                variant="outlined"
                sx={{ mr: 1, mb: 1 }}
              />
            ))}
          </Box>
        </Box>
      </Paper>

      {/* Status Summary */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Status Summary
        </Typography>
        
        {!hasError && plans.length > 0 ? (
          <Alert severity="success">
            ✅ Payment system is working! Users can view and select plans.
            {hasHistoryError && ' (Payment history is temporarily unavailable)'}
          </Alert>
        ) : (
          <Alert severity="error">
            ❌ Payment system has issues: {errorMessage}
          </Alert>
        )}

        {hasHistoryError && !hasError && (
          <Alert severity="warning" sx={{ mt: 1 }}>
            ⚠️ Payment history is unavailable but core functionality works.
            This is expected if the backend doesn't support payment history yet.
          </Alert>
        )}
      </Paper>

      {/* Instructions */}
      <Paper sx={{ p: 2, mt: 2, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom>
          Next Steps
        </Typography>
        
        <Typography variant="body2" component="div">
          <strong>If plans are working:</strong>
          <ul>
            <li>Users can select plans and proceed to payment</li>
            <li>Payment history errors don't affect core functionality</li>
            <li>Enable payment history when backend endpoint is ready</li>
          </ul>
          
          <strong>If plans are not working:</strong>
          <ul>
            <li>Check network tab for API errors</li>
            <li>Verify backend is running and accessible</li>
            <li>Check authentication token</li>
          </ul>
        </Typography>
      </Paper>
    </Box>
  );
};

export default PaymentStatusDebug;
