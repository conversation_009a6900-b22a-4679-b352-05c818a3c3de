/**
 * Touch interaction utilities for tablet optimization
 */

export interface TouchTarget {
  minSize: number;
  padding: number;
}

export const TOUCH_TARGETS = {
  small: { minSize: 32, padding: 8 },
  medium: { minSize: 44, padding: 12 },
  large: { minSize: 56, padding: 16 },
} as const;

/**
 * Check if device supports touch
 */
export const isTouchDevice = (): boolean => {
  return (
    'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    (navigator as any).msMaxTouchPoints > 0
  );
};

/**
 * Check if device is likely a tablet
 */
export const isTabletDevice = (): boolean => {
  const width = window.innerWidth;
  const height = window.innerHeight;
  const maxDimension = Math.max(width, height);
  const minDimension = Math.min(width, height);

  return isTouchDevice() && minDimension >= 768 && maxDimension <= 1024;
};

/**
 * Detect Samsung devices
 */
export const isSamsungDevice = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  return (
    userAgent.includes('samsung') ||
    userAgent.includes('sm-') ||
    userAgent.includes('galaxy')
  );
};

/**
 * Detect Android devices
 */
export const isAndroidDevice = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('android');
};

/**
 * Check if device has known drag-and-drop issues
 */
export const hasDragDropIssues = (): boolean => {
  return isSamsungDevice() || (isAndroidDevice() && isTouchDevice());
};

/**
 * Get optimal touch target size based on device
 */
export const getOptimalTouchTarget = (): TouchTarget => {
  if (isTabletDevice()) {
    return TOUCH_TARGETS.medium;
  } else if (isTouchDevice()) {
    return TOUCH_TARGETS.large;
  }
  return TOUCH_TARGETS.small;
};

/**
 * Add haptic feedback if supported
 */
export const triggerHapticFeedback = (
  type: 'light' | 'medium' | 'heavy' = 'light'
): void => {
  if ('vibrate' in navigator) {
    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30],
    };
    navigator.vibrate(patterns[type]);
  }
};

/**
 * Prevent default touch behaviors for better control
 */
export const preventTouchDefaults = (element: HTMLElement): void => {
  element.style.touchAction = 'none';
  element.style.userSelect = 'none';
  element.style.webkitUserSelect = 'none';
  (element.style as any).webkitTouchCallout = 'none';
};

/**
 * Enable smooth scrolling for touch devices
 */
export const enableSmoothScrolling = (element: HTMLElement): void => {
  (element.style as any).webkitOverflowScrolling = 'touch';
  element.style.scrollBehavior = 'smooth';
};

/**
 * Calculate distance between two touch points
 */
export const getTouchDistance = (touch1: Touch, touch2: Touch): number => {
  const dx = touch1.clientX - touch2.clientX;
  const dy = touch1.clientY - touch2.clientY;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * Get center point between two touches
 */
export const getTouchCenter = (
  touch1: Touch,
  touch2: Touch
): { x: number; y: number } => {
  return {
    x: (touch1.clientX + touch2.clientX) / 2,
    y: (touch1.clientY + touch2.clientY) / 2,
  };
};

/**
 * Debounce function for touch events
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout>;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle function for touch events
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

/**
 * Get responsive grid columns based on screen size
 */
export const getResponsiveColumns = (containerWidth: number): number => {
  if (containerWidth < 768) {
    return 1; // Mobile
  } else if (containerWidth < 1024) {
    return 2; // Tablet portrait
  } else if (containerWidth < 1200) {
    return 3; // Tablet landscape
  } else {
    return 4; // Desktop
  }
};

/**
 * Calculate optimal card size for tablet
 */
export const getOptimalCardSize = (
  containerWidth: number,
  columns: number
): { width: number; height: number } => {
  const padding = 20;
  const gap = 16;
  const availableWidth = containerWidth - padding * 2 - gap * (columns - 1);
  const cardWidth = Math.floor(availableWidth / columns);

  // Maintain aspect ratio for cards
  const aspectRatio = 0.75; // 4:3 ratio
  const cardHeight = Math.floor(cardWidth * aspectRatio);

  return { width: cardWidth, height: cardHeight };
};
