import { loadStripe, Stripe, StripeElements } from '@stripe/stripe-js';
import { STRIPE_PUBLISHABLE_KEY } from '../../services/config';
import { PaymentError, PaymentPlan } from '../../types/payment';
import {
  PAYMENT_ERROR_CODES,
  PAYMENT_VALIDATION,
} from '../../services/constants/paymentServiceConstants';

// Stripe instance cache
let stripePromise: Promise<Stripe | null> | null = null;

/**
 * Get Stripe instance (cached)
 */
export const getStripe = (): Promise<Stripe | null> => {
  if (!stripePromise) {
    if (!STRIPE_PUBLISHABLE_KEY) {
      console.error('Stripe publishable key is not configured');
      return Promise.resolve(null);
    }
    stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);
  }
  return stripePromise;
};

/**
 * Format currency amount for display
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'usd',
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount);
};

/**
 * Convert dollars to cents for Stripe
 */
export const dollarsToCents = (dollars: number): number => {
  return Math.round(dollars * 100);
};

/**
 * Convert cents to dollars from Stripe
 */
export const centsToDollars = (cents: number): number => {
  return cents / 100;
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): boolean => {
  return PAYMENT_VALIDATION.EMAIL_REGEX.test(email);
};

/**
 * Validate postal code format (US)
 */
export const validatePostalCode = (postalCode: string): boolean => {
  return PAYMENT_VALIDATION.POSTAL_CODE_REGEX.test(postalCode);
};

/**
 * Validate payment amount
 */
export const validateAmount = (amount: number): boolean => {
  return (
    amount >= PAYMENT_VALIDATION.MIN_AMOUNT &&
    amount <= PAYMENT_VALIDATION.MAX_AMOUNT
  );
};

/**
 * Map Stripe error to PaymentError
 */
export const mapStripeError = (stripeError: any): PaymentError => {
  const errorCode = stripeError.code || 'unknown_error';
  const errorMessage = stripeError.message || 'An unexpected error occurred';

  let errorType: PaymentError['type'] = 'api_error';

  // Map Stripe error codes to our error types
  switch (errorCode) {
    case 'card_declined':
    case 'insufficient_funds':
    case 'expired_card':
    case 'incorrect_cvc':
    case 'incorrect_number':
    case 'invalid_expiry_month':
    case 'invalid_expiry_year':
      errorType = 'card_error';
      break;
    case 'parameter_invalid_empty':
    case 'parameter_invalid_integer':
    case 'parameter_invalid_string_empty':
      errorType = 'validation_error';
      break;
    case 'api_connection_error':
    case 'api_error':
      errorType = 'network_error';
      break;
    default:
      errorType = 'api_error';
  }

  return {
    type: errorType,
    code: errorCode,
    message: errorMessage,
    param: stripeError.param,
  };
};

/**
 * Get user-friendly error message
 */
export const getErrorMessage = (error: PaymentError): string => {
  switch (error.code) {
    case PAYMENT_ERROR_CODES.CARD_DECLINED:
      return 'Your card was declined. Please try a different payment method.';
    case PAYMENT_ERROR_CODES.INSUFFICIENT_FUNDS:
      return 'Your card has insufficient funds. Please try a different payment method.';
    case PAYMENT_ERROR_CODES.EXPIRED_CARD:
      return 'Your card has expired. Please try a different payment method.';
    case PAYMENT_ERROR_CODES.INCORRECT_CVC:
      return "Your card's security code is incorrect. Please check and try again.";
    case PAYMENT_ERROR_CODES.PROCESSING_ERROR:
      return 'There was an error processing your payment. Please try again.';
    case PAYMENT_ERROR_CODES.AUTHENTICATION_REQUIRED:
      return 'Your payment requires additional authentication. Please complete the verification.';
    case PAYMENT_ERROR_CODES.NETWORK_ERROR:
      return 'Network error. Please check your connection and try again.';
    default:
      return error.message || 'An unexpected error occurred. Please try again.';
  }
};

/**
 * Generate payment description
 */
export const generatePaymentDescription = (plan: PaymentPlan): string => {
  const interval = plan.interval || 'month';
  return `${plan.name} Plan - ${formatCurrency(plan.price, plan.currency)} per ${interval}`;
};

/**
 * Check if payment method requires confirmation
 */
export const requiresConfirmation = (paymentIntentStatus: string): boolean => {
  return ['requires_action', 'requires_source_action'].includes(
    paymentIntentStatus
  );
};

/**
 * Check if payment is successful
 */
export const isPaymentSuccessful = (paymentIntentStatus: string): boolean => {
  return paymentIntentStatus === 'succeeded';
};

/**
 * Check if payment failed
 */
export const isPaymentFailed = (paymentIntentStatus: string): boolean => {
  return ['payment_failed', 'canceled'].includes(paymentIntentStatus);
};

/**
 * Get Stripe Elements appearance configuration
 */
export const getStripeElementsAppearance = (isDarkMode: boolean = false) => {
  return {
    theme: isDarkMode ? 'night' : ('stripe' as const),
    variables: {
      colorPrimary: '#0570de',
      colorBackground: isDarkMode ? '#1a1a1a' : '#ffffff',
      colorText: isDarkMode ? '#ffffff' : '#30313d',
      colorDanger: '#df1b41',
      fontFamily: 'Inter, system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '8px',
    },
  };
};

/**
 * Cleanup Stripe elements
 */
export const cleanupStripeElements = (
  elements: StripeElements | null
): void => {
  if (elements) {
    try {
      elements.getElement('card')?.destroy();
    } catch (error) {
      console.warn('Error cleaning up Stripe elements:', error);
    }
  }
};

/**
 * Format card brand for display
 */
export const formatCardBrand = (brand: string): string => {
  const brandMap: Record<string, string> = {
    visa: 'Visa',
    mastercard: 'Mastercard',
    amex: 'American Express',
    discover: 'Discover',
    diners: 'Diners Club',
    jcb: 'JCB',
    unionpay: 'UnionPay',
    unknown: 'Unknown',
  };

  return brandMap[brand] || brand.charAt(0).toUpperCase() + brand.slice(1);
};

/**
 * Generate payment metadata
 */
export const generatePaymentMetadata = (
  plan: PaymentPlan,
  userEmail?: string,
  additionalData?: Record<string, string>
): Record<string, string> => {
  return {
    plan_id: plan.id,
    plan_name: plan.name,
    plan_price: plan.price.toString(),
    plan_currency: plan.currency,
    plan_interval: plan.interval,
    user_email: userEmail || '',
    timestamp: new Date().toISOString(),
    ...additionalData,
  };
};
