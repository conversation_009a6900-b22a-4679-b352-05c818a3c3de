import { BaseQueryFn, createApi } from '@reduxjs/toolkit/query/react';
import { AI_BASE_URL } from './config';
import { fileUploadBaseQuery } from './baseQuery';
import {
  PROCESS_FILE,
  UPDATE_TITLE,
  SET_MODEL,
  GET_CURRENT_MODEL,
} from './constants/chatServicesConstants';

export const chatApi = createApi({
  reducerPath: 'chatApi',
  baseQuery: fileUploadBaseQuery({
    baseUrl: AI_BASE_URL,
  }) as BaseQueryFn<any, unknown, unknown, {}, {}>,
  endpoints: (builder) => ({
    uploadFile: builder.mutation<
      void,
      {
        formData: FormData;
        onProgress?: (
          progress: number,
          status: 'uploading' | 'processing'
        ) => void;
      }
    >({
      query: ({ formData, onProgress }) => ({
        url: PROCESS_FILE,
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onProgress,
      }),
    }),
    updateTitle: builder.mutation<
      void,
      { project_id: string; new_title: string }
    >({
      query: (data) => ({
        url: UPDATE_TITLE,
        method: 'PUT',
        body: data,
        headers: {
          'Content-Type': 'application/json',
        },
      }),
    }),
    choseModal: builder.mutation<
      void,
      { project_id: string; model_name: string }
    >({
      query: (data) => ({
        url: SET_MODEL,
        method: 'POST',
        body: data,
        headers: {
          'Content-Type': 'application/json',
        },
      }),
    }),
    getCurrentModel: builder.query<any, string>({
      query: (projectId) => ({
        url: `${GET_CURRENT_MODEL}?project_id=${projectId}`,
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
    }),
  }),
});

export const {
  useUploadFileMutation,
  useUpdateTitleMutation,
  useChoseModalMutation,
  useGetCurrentModelQuery,
} = chatApi;
