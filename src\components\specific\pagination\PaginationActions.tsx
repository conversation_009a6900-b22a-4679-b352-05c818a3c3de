import React, { useState } from 'react';
import CustomButton from '../../common/button/CustomButton';
import IconButton from '../../common/button/IconButton';
import styles from './PaginationActions.module.css';
import previous from '../../../assets/images/svg/previous.svg';
import next from '../../../assets/images/svg/next.svg';
import conditionalApprove from '../../../assets/images/svg/conditionalApprove.svg';
import reject from '../../../assets/images/svg/Reject.svg';
import approve from '../../../assets/images/svg/Approve.svg';
import ConditionalApprove from './conditionalApprove/ConditionalApprove';
import RejectComponent from './reject/Reject';
import ApproveComponent from './approve/Approve';
import Modal from './Modal';

interface PaginationActionsProps {
  totalPages: number;
  onConditionalApprove: () => void;
  onReject: () => void;
  onApprove: () => void;
}

const PaginationActions: React.FC<PaginationActionsProps> = ({
  totalPages,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [currentGroup, setCurrentGroup] = useState(0);

  const [activeButton, setActiveButton] = useState<string | null>(null);
  const [showConditionalApprove, setShowConditionalApprove] = useState(false);
  const [showReject, setShowReject] = useState(false);
  const [showApprove, setShowApprove] = useState(false);

  const pagesPerGroup = 3;
  const startPage = currentGroup * pagesPerGroup + 1;
  const endPage = Math.min((currentGroup + 1) * pagesPerGroup, totalPages);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      if (page < startPage || page > endPage) {
        const newGroup = Math.floor((page - 1) / pagesPerGroup);
        setCurrentGroup(newGroup);
      }
    }
  };

  const handleNextGroup = () => {
    if ((currentGroup + 1) * pagesPerGroup < totalPages) {
      setCurrentGroup(currentGroup + 1);
      setCurrentPage(Math.min(currentPage + pagesPerGroup, totalPages));
    }
  };

  const handlePreviousGroup = () => {
    if (currentGroup > 0) {
      setCurrentGroup(currentGroup - 1);
      setCurrentPage(Math.max(currentPage - pagesPerGroup, 1));
    }
  };

  const handleButtonClick = (buttonName: string) => {
    setActiveButton(buttonName);
    if (buttonName === 'conditionalApprove') {
      setShowConditionalApprove(true);
      console.log('Conditional Approve Button Clicked');
    } else if (buttonName === 'reject') {
      setShowReject(true);
      console.log('Reject Button Clicked');
    } else if (buttonName === 'approve') {
      setShowApprove(true);
      console.log('Approve Button Clicked');
    }
  };

  const handleCloseModal = () => {
    setShowConditionalApprove(false);
    setShowReject(false);
    setShowApprove(false);
  };

  const renderPageNumbers = () => {
    const pageNumbers = [];

    if (startPage > 1) {
      pageNumbers.push(
        <CustomButton
          key={1}
          type="secondary"
          style={{
            borderTop: 'none',
            borderBottom: 'none',
            borderLeft: '0.5px solid #D1DFEC',
            borderRight: '0.5px solid #D1DFEC',
            borderRadius: '0px',
            color: '#677480',
          }}
          label="1"
          onClick={() => handlePageChange(1)}
        />
      );
      if (startPage > 2) {
        pageNumbers.push(<span key="ellipsis-start">...</span>);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(
        <CustomButton
          key={i}
          type="secondary"
          style={{
            borderTop: 'none',
            borderBottom: 'none',
            borderLeft: '0.5px solid #D1DFEC',
            borderRight: '0.5px solid #D1DFEC',
            borderRadius: '0px',
            color: '#677480',
          }}
          label={i.toString()}
          onClick={() => handlePageChange(i)}
          isActive={i === currentPage}
        />
      );
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pageNumbers.push(<span key="ellipsis-end">...</span>);
      }
      pageNumbers.push(
        <CustomButton
          key={totalPages}
          type="secondary"
          style={{
            borderTop: 'none',
            borderBottom: 'none',
            borderLeft: '0.5px solid #D1DFEC',
            borderRight: '0.5px solid #D1DFEC',
            borderRadius: '0px',
            color: '#677480',
          }}
          label={totalPages.toString()}
          onClick={() => handlePageChange(totalPages)}
        />
      );
    }

    return pageNumbers;
  };

  return (
    <div className={styles.container}>
      <div className={styles.pagination}>
        <IconButton
          icon={<img src={previous} alt="Previous" />}
          type="secondary"
          onClick={handlePreviousGroup}
          disabled={currentGroup === 0}
          size="medium"
          title="Previous page"
        />
        {renderPageNumbers()}
        <IconButton
          icon={<img src={next} alt="Next" />}
          type="secondary"
          onClick={handleNextGroup}
          disabled={(currentGroup + 1) * pagesPerGroup >= totalPages}
          size="medium"
          title="Next page"
        />
      </div>
      <div className={styles.actions}>
        <CustomButton
          leftIcon={
            <img
              src={conditionalApprove}
              alt="Conditional Approve"
              className={
                activeButton === 'conditionalApprove'
                  ? styles.clickedIcon1
                  : styles.defaultIcon
              }
            />
          }
          type={activeButton === 'conditionalApprove' ? 'primary' : 'secondary'}
          label="Conditional Approve"
          onClick={() => handleButtonClick('conditionalApprove')}
        />
        <CustomButton
          leftIcon={
            <img
              src={reject}
              alt="Reject"
              className={
                activeButton === 'reject'
                  ? styles.clickedIcon
                  : styles.defaultIcon
              }
            />
          }
          type={activeButton === 'reject' ? 'primary' : 'secondary'}
          label="Reject"
          onClick={() => handleButtonClick('reject')}
        />
        <CustomButton
          leftIcon={
            <img
              src={approve}
              alt="Approve"
              className={
                activeButton === 'approve'
                  ? styles.clickedIcon
                  : styles.defaultIcon
              }
            />
          }
          type={activeButton === 'approve' ? 'primary' : 'secondary'}
          label="Approve"
          onClick={() => handleButtonClick('approve')}
        />
      </div>
      <Modal isOpen={showConditionalApprove} onClose={handleCloseModal}>
        <ConditionalApprove />
      </Modal>
      <Modal isOpen={showReject} onClose={handleCloseModal}>
        <RejectComponent />
      </Modal>
      <Modal isOpen={showApprove} onClose={handleCloseModal}>
        <ApproveComponent />
      </Modal>
    </div>
  );
};

export default PaginationActions;
