import { ApiNotificationResponse } from '../services/notificationService';
import { WorkflowNotification } from '../types/workflow/index';

/**
 * Transform backend API notification response to frontend notification format
 */
export const transformApiNotificationToWorkflowNotification = (
  apiNotification: ApiNotificationResponse
): WorkflowNotification => {
  return {
    id: apiNotification.id,
    type: apiNotification.type || 'workflow_created',
    workflowId: apiNotification.workflowId,
    workflowTitle: apiNotification.title || 'Workflow',
    message: apiNotification.content,
    actionRequired: apiNotification.actionRequired || false,
    status: apiNotification.status,
    createdAt: apiNotification.createdAt,
    read: !!apiNotification.readAt,
    readAt: apiNotification.readAt,
    userId: apiNotification.userId,
    priority: apiNotification.priority,
    title: apiNotification.title,
    content: apiNotification.content,
  };
};

/**
 * Transform frontend notification to API create request format
 */
export const transformWorkflowNotificationToApiRequest = (
  notification: Partial<WorkflowNotification>
) => {
  return {
    workflowId: notification.workflowId || '',
    title: notification.workflowTitle || notification.title || 'Notification',
    content: notification.message || notification.content || '',
  };
};

/**
 * Batch transform multiple API notifications
 */
export const transformApiNotificationsArray = (
  apiNotifications: ApiNotificationResponse[]
): WorkflowNotification[] => {
  return apiNotifications.map(transformApiNotificationToWorkflowNotification);
};

/**
 * Check if notification data is valid
 */
export const isValidNotificationData = (notification: any): boolean => {
  return !!(
    notification &&
    notification.id &&
    notification.workflowId &&
    notification.userId &&
    notification.createdAt
  );
};

/**
 * Sort notifications by creation date (newest first)
 */
export const sortNotificationsByDate = (
  notifications: WorkflowNotification[]
): WorkflowNotification[] => {
  return [...notifications].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
};

/**
 * Filter notifications by read status
 */
export const filterNotificationsByReadStatus = (
  notifications: WorkflowNotification[],
  unreadOnly: boolean = false
): WorkflowNotification[] => {
  if (unreadOnly) {
    return notifications.filter(n => !n.read);
  }
  return notifications;
};

/**
 * Get notification count by status
 */
export const getNotificationCounts = (notifications: WorkflowNotification[]) => {
  return {
    total: notifications.length,
    unread: notifications.filter(n => !n.read).length,
    actionRequired: notifications.filter(n => n.actionRequired && !n.read).length,
    byType: {
      assignment: notifications.filter(n => n.type === 'assignment').length,
      status_change: notifications.filter(n => n.type === 'status_change').length,
      workflow_created: notifications.filter(n => n.type === 'workflow_created').length,
    },
  };
};
