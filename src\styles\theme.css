:root,
[data-theme='light'] {
  /* Colors */
  --color-primary: #1b5ea1;
  --color-primary-light: #2d7bc7;
  --color-primary-dark: #0e2f51;
  --color-primary-contrast: #ffffff;

  --color-secondary: #9c27b0;
  --color-secondary-light: #ba68c8;
  --color-secondary-dark: #7b1fa2;
  --color-secondary-contrast: #ffffff;

  /* Error Colors */
  --color-error-main: #d32f2f;
  --color-error-light: #ef5350;
  --color-error-dark: #c62828;
  --color-error-contrast: #ffffff;

  /* Warning Colors */
  --color-warning-main: #ed6c02;
  --color-warning-light: #ff9800;
  --color-warning-dark: #e65100;
  --color-warning-contrast: #ffffff;
  --color-warning-bg: #fff3e0;

  /* Info Colors */
  --color-info-main: #0288d1;
  --color-info-light: #03a9f4;
  --color-info-dark: #01579b;
  --color-info-contrast: #ffffff;

  /* Success Colors */
  --color-success-main: #2e7d32;
  --color-success-light: #4caf50;
  --color-success-dark: #1b5e20;
  --color-success-contrast: #ffffff;

  /* Grey Scale */
  --color-grey-50: #fafafa;
  --color-grey-100: #f5f5f5;
  --color-grey-200: #eeeeee;
  --color-grey-300: #e0e0e0;
  --color-grey-400: #bdbdbd;
  --color-grey-500: #9e9e9e;
  --color-grey-600: #757575;
  --color-grey-700: #616161;
  --color-grey-800: #424242;
  --color-grey-900: #212121;

  --color-text-primary: #0e2f51;
  --color-text-secondary: #667085;
  --color-text-disabled: rgba(0, 0, 0, 0.38);

  /* Background Colors */
  --color-background-primary: #f9fafb;
  --color-background-primary-rgb: 249, 250, 251;
  --color-background-secondary: #ffffff;
  --color-background-secondary-rgb: 255, 255, 255;
  --color-background-tertiary: #f5f5f5;
  --color-background-tertiary-rgb: 245, 245, 245;
  --color-background-card: #ffffff;
  --color-background-modal: #ffffff;
  --color-background-dropdown: #ffffff;
  --color-border: #e0e0e0;

  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    Roboto, Oxygen, Ubuntu, sans-serif;
  --font-family-code: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  /* Spacing */
  --spacing-0: 0;
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;

  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
}

/* Dark Theme */
[data-theme='dark'] {
  /* Colors */
  --color-primary: #4d8fd1;
  --color-primary-light: #6ba5e0;
  --color-primary-dark: #1b5ea1;
  --color-primary-contrast: #ffffff;

  --color-secondary: #ce93d8;
  --color-secondary-light: #e1bee7;
  --color-secondary-dark: #ba68c8;
  --color-secondary-contrast: #000000;

  /* Error Colors */
  --color-error-main: #f44336;
  --color-error-light: #e57373;
  --color-error-dark: #d32f2f;
  --color-error-contrast: #ffffff;

  /* Warning Colors */
  --color-warning-main: #ffa726;
  --color-warning-light: #ffb74d;
  --color-warning-dark: #f57c00;
  --color-warning-contrast: #000000;
  --color-warning-bg: #4d3200;

  /* Info Colors */
  --color-info-main: #29b6f6;
  --color-info-light: #4fc3f7;
  --color-info-dark: #0288d1;
  --color-info-contrast: #000000;

  /* Success Colors */
  --color-success-main: #66bb6a;
  --color-success-light: #81c784;
  --color-success-dark: #388e3c;
  --color-success-contrast: #000000;

  /* Grey Scale - Inverted for dark mode */
  --color-grey-50: #212121;
  --color-grey-100: #424242;
  --color-grey-200: #616161;
  --color-grey-300: #757575;
  --color-grey-400: #9e9e9e;
  --color-grey-500: #bdbdbd;
  --color-grey-600: #e0e0e0;
  --color-grey-700: #eeeeee;
  --color-grey-800: #f5f5f5;
  --color-grey-900: #fafafa;

  /* Text Colors */
  --color-text-primary: #ffffff;
  --color-text-secondary: #b0bec5;
  --color-text-disabled: rgba(255, 255, 255, 0.5);

  /* Background Colors */
  --color-background-primary: #121212;
  --color-background-primary-rgb: 18, 18, 18;
  --color-background-secondary: #1e1e1e;
  --color-background-secondary-rgb: 30, 30, 30;
  --color-background-tertiary: #2d2d2d;
  --color-background-tertiary-rgb: 45, 45, 45;
  --color-background-card: #2d2d2d;
  --color-background-modal: #2d2d2d;
  --color-background-dropdown: #2d2d2d;
  --color-border: #424242;
}
