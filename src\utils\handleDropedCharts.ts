import { Layout } from 'react-grid-layout';
import { ViewMode } from 'gantt-task-react';

interface DroppedData {
  type: string;
  data: any;
  options: any;
}

interface HandleDropedChartsParams {
  sketchbookId: string;
  droppedData: DroppedData;
  event: React.DragEvent;
  pageSize: {
    width: number;
    height: number;
  };
  gridCols: number;
  gridRows: number;
  activePage: string | number;
  charts: any[];
  layout: Layout[];
}

const formatDataForBackend = (chartType: string, data: any) => {
  if (data._originalData) {
    return data._originalData;
  }
  switch (chartType) {
    case 'bubble':
      return data.map((point: any) => ({
        x: point.x,
        y: point.y,
        r: point.r,
      }));
    case 'scatter':
      return data.map((point: any) => ({
        x: point.x,
        y: point.y,
      }));
    case 'timeline':
      return data;
    case 'gantt':
      if (Array.isArray(data)) {
        return data.map((task: any) => ({
          ...task,
          start:
            typeof task.start === 'string'
              ? task.start
              : task.start.toISOString(),
          end: typeof task.end === 'string' ? task.end : task.end.toISOString(),
        }));
      }
      return data;
    default:
      return data;
  }
};

export const handleDropedCharts = ({
  sketchbookId,
  droppedData,
  event,
  pageSize,
  gridCols,
  gridRows,
  activePage,
  charts,
  layout,
}: HandleDropedChartsParams) => {
  console.log(droppedData, 'droppedData');
  try {
    const newChartId = `chart-${Date.now()}`;
    const rect = event.currentTarget.getBoundingClientRect();
    const gridCol = Math.floor(
      (event.clientX - rect.left) / (pageSize.width / gridCols)
    );
    const gridRow = Math.floor(
      (event.clientY - rect.top) / (pageSize.height / gridRows)
    );

    const newLayoutItem: Layout = {
      i: newChartId,
      x: Math.min(Math.max(0, gridCol), gridCols - 3),
      y: Math.max(0, gridRow),
      w: 3,
      h: 2,
    };

    const generateChartPayload = (chartType: string) => {
      let baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: 'top' as const,
          },
          title: {
            display: true,
            text: 'Chart',
          },
        },
        scales: {
          x: {
            type: 'category',
            display: true,
            title: {
              display: true,
              text: 'X Axis',
            },
          },
          y: {
            type: 'linear',
            display: true,
            title: {
              display: true,
              text: 'Y Axis',
            },
            beginAtZero: true,
          },
        },
      };

      const commonDataset = {
        label: 'Sample Dataset',
        backgroundColor:
          chartType === 'line' ? 'rgba(255, 99, 132, 0.2)' : '#FF638480',
        borderColor: '#FF6384',
        borderWidth: 1,
      };

      let chartData;
      switch (chartType) {
        case 'bar':
        case 'line':
        case 'area':
          chartData = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [
              {
                ...commonDataset,
                data: [12, 19, 3, 5, 2, 3],
                fill: chartType === 'area',
                tension: chartType === 'area' ? 0.4 : undefined,
                backgroundColor: ['#9966FF'],
              },
            ],
          };
          break;

        case 'pie':
        case 'doughnut':
          chartData = {
            labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple'],
            datasets: [
              {
                ...commonDataset,
                data: [11, 16, 7, 3, 14],
                backgroundColor: [
                  '#FF6384',
                  '#36A2EB',
                  '#FFCE56',
                  '#4BC0C0',
                  '#9966FF',
                ],
              },
            ],
          };
          // delete (baseOptions as any).scales; // Remove scales for these chart types
          break;

        case 'radar':
          chartData = {
            labels: ['Speed', 'Agility', 'Power', 'Stamina', 'Technique'],
            datasets: [
              {
                ...commonDataset,
                data: [85, 75, 90, 80, 70],
                backgroundColor: ['#36A2EB'],
                pointBackgroundColor: 'rgb(255, 99, 132)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgb(255, 99, 132)',
              },
            ],
          };
          delete (baseOptions as any).scales;
          break;

        case 'scatter':
          chartData = {
            labels: ['Point 1', 'Point 2', 'Point 3', 'Point 4'],
            datasets: [
              {
                label: 'Dataset 1',
                data: [
                  { x: 10, y: 20 },
                  { x: 15, y: 25 },
                  { x: 20, y: 30 },
                  { x: 25, y: 35 },
                ],
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
              },
            ],
          };

          break;
        case 'bubble':
          chartData = {
            labels: ['point 1', 'point 2', 'point 3'], // Usually ignored in bubble charts
            datasets: [
              {
                label: 'Dataset 1',
                data: [
                  { x: 10, y: 20, r: 15 }, // Point 1: x = 10, y = 20, radius = 15
                  { x: 15, y: 25, r: 10 }, // Point 2: x = 15, y = 25, radius = 10
                  { x: 20, y: 30, r: 20 }, // Point 3: x = 20, y = 30, radius = 20
                ],
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'], // Colors for each bubble
              },
            ],
          };
          break;

        case 'horizontal':
          chartData = {
            labels: ['A', 'B', 'C', 'D'],
            datasets: [
              {
                ...commonDataset, // Add back the common dataset properties
                data: [65, 59, 80, 81],
                backgroundColor: [
                  '#FF6384',
                  '#36A2EB',
                  '#FFCE56',
                  '#4BC0C0', // Added one more color for the fourth bar
                ],
              },
            ],
          };
          baseOptions = {
            ...baseOptions,
            indexAxis: 'y', // This makes the chart horizontal
            scales: {
              x: {
                beginAtZero: true, // Start x-axis from 0
              },
              y: {
                ticks: {
                  crossAlign: 'far', // Improves label alignment
                },
              },
            },
          };
          break;

        case 'timeline':
          (baseOptions as any).scales = {
            x: {
              type: 'time',
              time: { unit: 'day' },
              title: {
                display: true,
                text: 'Date',
              },
            },
            y: {
              type: 'linear',
              display: true,
              title: {
                display: true,
                text: 'Value',
              },
              beginAtZero: true,
              reverse: true,
            },
          };
          chartData = {
            labels: ['2024-01-01', '2024-02-01', '2024-03-01'],
            datasets: [
              {
                label: 'Timeline Events',
                data: [
                  { x: '2024-01-01', y: 10, duration: 0 },
                  { x: '2024-02-01', y: 20, duration: 0 },
                  { x: '2024-03-01', y: 15, duration: 0 },
                ],
                backgroundColor: '#36A2EB',
                borderColor: '#36A2EB',
                fill: false,
                isTimeline: true,
              },
              {
                label: 'Remaining Tasks',
                data: [
                  { x: '2024-01-01', y: 50, duration: 0 },
                  { x: '2024-01-02', y: 40, duration: 0 },
                  { x: '2024-01-03', y: 30, duration: 0 },
                  { x: '2024-01-04', y: 20, duration: 0 },
                  { x: '2024-01-05', y: 10, duration: 0 },
                  { x: '2024-01-06', y: 0, duration: 0 },
                ],
                backgroundColor: 'rgba(255,99,132,0.2)',
                borderColor: 'rgba(255,99,132,1)',
                fill: true,
                isTimeline: true,
              },
            ],
            isTimeline: true,
          };
          break;

        case 'burndown':
          (baseOptions as any).scales = {
            x: {
              type: 'time',
              time: { unit: 'day' },
              title: {
                display: true,
                text: 'Sprint Days',
              },
            },
            y: {
              type: 'linear',
              display: true,
              title: {
                display: true,
                text: 'Story Points Remaining',
              },
              beginAtZero: true,
              min: 0,
              max: 50,
            },
          };
          chartData = {
            labels: [
              '2024-01-01',
              '2024-01-02',
              '2024-01-03',
              '2024-01-04',
              '2024-01-05',
              '2024-01-06',
            ],
            datasets: [
              {
                label: 'Ideal Burndown',
                data: [
                  { x: '2024-01-01', y: 50 },
                  { x: '2024-01-06', y: 0 },
                ],
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                borderDash: [5, 5],
                fill: false,
                pointRadius: 0,
              },
              {
                label: 'Actual Burndown',
                data: [
                  { x: '2024-01-01', y: 50 },
                  { x: '2024-01-02', y: 45 },
                  { x: '2024-01-03', y: 35 },
                  { x: '2024-01-04', y: 30 },
                  { x: '2024-01-05', y: 15 },
                  { x: '2024-01-06', y: 0 },
                ],
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
              },
            ],
          };
          (baseOptions as any).plugins = {
            ...(baseOptions as any).plugins,
            title: {
              display: true,
              text: 'Sprint Burndown Chart',
            },
            tooltip: {
              mode: 'index',
              intersect: false,
            },
          };
          break;

        case 'gauge':
          chartData = {
            labels: ['Gauge'],
            data: { percent: 0.5 },
            options: {
              id: `gauge-${Date.now()}`,
              nrOfLevels: 20,
              arcWidth: 0.3,
              arcPadding: 0.05,
              cornerRadius: 6,
              colors: ['#FF5F6D', '#FFC371'],
              needleColor: '#464A4F',
              textColor: '#464A4F',
              animate: true,
              animDelay: 0,
              animateDuration: 2000,
            },
          };
          (baseOptions as any) = {};
          break;
        case 'polarArea':
          chartData = {
            labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple'],
            datasets: [
              {
                label: 'Dataset 1',
                data: [11, 16, 7, 3, 14],
                backgroundColor: [
                  'rgba(255, 99, 132, 0.5)',
                  'rgba(54, 162, 235, 0.5)',
                  'rgba(255, 206, 86, 0.5)',
                  'rgba(75, 192, 192, 0.5)',
                  'rgba(153, 102, 255, 0.5)',
                ],
                borderColor: [
                  'rgb(255, 99, 132)',
                  'rgb(54, 162, 235)',
                  'rgb(255, 206, 86)',
                  'rgb(75, 192, 192)',
                  'rgb(153, 102, 255)',
                ],
                borderWidth: 1,
              },
            ],
          };

          baseOptions = {
            responsive: true,
            plugins: {
              legend: {
                display: true,
                position: 'top',
              },
              title: {
                display: true,
                text: 'Polar Area Chart',
              },
            },
            scales: {
              r: {
                type: 'radialLinear',
                display: true,
                beginAtZero: true,
                ticks: {
                  stepSize: 2,
                },
              },
            },
            animation: {
              animateRotate: true,
              animateScale: true,
            },
          };
          break;

        case 'gantt':
          chartData = {
            tasks: [
              {
                id: 'task1',
                name: 'Project Planning',
                start: '2024-01-01T00:00:00.000Z',
                end: '2024-01-15T00:00:00.000Z',
                progress: 45,
                type: 'project',
                hideChildren: false,
                displayOrder: 1,
              },
              {
                id: 'task2',
                name: 'Research',
                start: '2024-01-02T00:00:00.000Z',
                end: '2024-01-08T00:00:00.000Z',
                progress: 100,
                type: 'task',
                project: 'task1',
                displayOrder: 2,
              },
              {
                id: 'task3',
                name: 'Design',
                start: '2024-01-08T00:00:00.000Z',
                end: '2024-01-12T00:00:00.000Z',
                progress: 25,
                type: 'task',
                project: 'task1',
                dependencies: ['task2'],
                displayOrder: 3,
              },
              {
                id: 'task4',
                name: 'Implementation',
                start: '2024-01-12T00:00:00.000Z',
                end: '2024-01-15T00:00:00.000Z',
                progress: 0,
                type: 'task',
                project: 'task1',
                dependencies: ['task3'],
                displayOrder: 4,
              },
            ],
            options: {
              viewMode: 'Week' as ViewMode,
              locale: 'en-GB',
              listCellWidth: '155px',
              columnWidth: 60,
              barCornerRadius: 3,
              fontSize: '12px',
              barHeight: 40,
              headerHeight: 50,
              rtl: false,
            },
          };
          baseOptions = chartData.options;
          break;

        case 'textarea':
          chartData = {
            data: {
              markdown: '# New Text Block\nEnter your text here...',
              style: {
                fontSize: '14px',
                lineHeight: '1.5',
                padding: '12px',
              },
            },
          };
          baseOptions = {};
          break;

        default:
          chartData = {
            labels: ['Sample'],
            datasets: [
              {
                ...commonDataset,
                data: [50],
                backgroundColor: ['#36A2EB'],
              },
            ],
          };
      }
      return {
        ...chartData,
        options: chartData.options || baseOptions,
      };
    };
    const chartPayload: any = generateChartPayload(droppedData.type);
    const pageIndex = Number(activePage) - 1;

    const simplifiedLayout: any = layout.map(({ x, y, i, w, h }) => ({
      x,
      y,
      i,
      w,
      h,
    }));

    const payload = {
      type: droppedData.type,
      pageIndex,
      sketchbookId,
      title:
        droppedData.type === 'textarea'
          ? 'Text Area'
          : `Sample ${droppedData.type.charAt(0).toUpperCase() + droppedData.type.slice(1)} Chart`,
      layouts: [...simplifiedLayout, newLayoutItem],
      // Ensure we're sending the correct page ID
      pageId: activePage,

      ...(droppedData.type === 'gantt'
        ? {
            tasks: chartPayload.tasks,
            options: chartPayload.options,
          }
        : droppedData.type === 'textarea'
          ? {
              data: {
                markdown:
                  chartPayload.data.markdown || 'Enter your text here...',
              },
              options: chartPayload.options || {
                style: {
                  fontSize: '14px',
                  lineHeight: '1.5',
                  padding: '12px',
                  border: '1px solid #ccc',
                  backgroundColor: '#fff',
                },
              },
            }
          : {
              labels:
                droppedData.type === 'gauge' ? ['Gauge'] : chartPayload.labels,
              datasets:
                droppedData.type === 'gauge'
                  ? { data: chartPayload.data.percent }
                  : droppedData.type === 'scatter'
                    ? chartPayload.datasets.map((dataset: any) => ({
                        ...dataset,
                        data: formatDataForBackend(
                          droppedData.type,
                          dataset.data
                        ),
                        backgroundColor: dataset.backgroundColor,
                        borderColor: dataset.borderColor,
                        borderWidth: dataset.borderWidth,
                      }))
                    : droppedData.type === 'bubble'
                      ? chartPayload.datasets.map((dataset: any) => ({
                          ...dataset,
                          data: formatDataForBackend(
                            droppedData.type,
                            dataset.data
                          ),
                          backgroundColor: dataset.backgroundColor,
                          borderColor: dataset.borderColor,
                          borderWidth: dataset.borderWidth,
                        }))
                      : chartPayload.datasets.map((dataset: any) => ({
                          ...dataset,
                          data: formatDataForBackend(
                            droppedData.type,
                            dataset.data
                          ),
                        })),
              options: chartPayload.options,
            }),
    };

    const newChart = {
      id: newChartId,
      type: droppedData.type,
      chartType: 'custom-chart',
      data:
        droppedData.type === 'gantt'
          ? { tasks: chartPayload.tasks }
          : droppedData.type === 'gauge'
            ? { percent: chartPayload.data.percent }
            : droppedData.type === 'textarea'
              ? {
                  markdown:
                    chartPayload.data.markdown || 'Enter your text here...',
                }
              : {
                  labels: chartPayload.labels,
                  datasets: chartPayload.datasets,
                },
      options:
        droppedData.type === 'textarea'
          ? chartPayload.options || {
              style: {
                fontSize: '14px',
                lineHeight: '1.5',
                padding: '12px',
                border: '1px solid #ccc',
                backgroundColor: '#fff',
              },
            }
          : chartPayload.options,
      width: (newLayoutItem.w * pageSize.width) / gridCols - 10,
      height: (newLayoutItem.h * pageSize.height) / gridRows - 10,
    };

    const chartData = [...charts, newChart];

    return { success: true, payload, chartId: [newChartId], chartData };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};
