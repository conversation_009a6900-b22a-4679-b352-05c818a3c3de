import { useState, useEffect, useCallback } from 'react';
import { useNetwork } from '../contexts/NetworkContext';
import { processPendingRequests } from '../utils/offlineRequestHandler';
import { getAllFromStore, OFFLINE_STORES } from '../utils/offlineStorage';

/**
 * Hook for handling offline synchronization
 * 
 * @param options Configuration options
 * @returns Synchronization state and functions
 */
export const useOfflineSync = (options: {
  autoSync?: boolean;
  syncInterval?: number;
  syncOnReconnect?: boolean;
} = {}) => {
  const {
    autoSync = true,
    syncInterval = 60000, // 1 minute
    syncOnReconnect = true,
  } = options;

  const { online, serverReachable, isOfflineModeEnabled } = useNetwork();
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [pendingItemsCount, setPendingItemsCount] = useState(0);
  const [syncStats, setSyncStats] = useState({
    successful: 0,
    failed: 0,
    remaining: 0,
  });

  // Get the count of pending items
  const getPendingItemsCount = useCallback(async () => {
    try {
      const pendingRequests = await getAllFromStore(OFFLINE_STORES.PENDING_REQUESTS);
      setPendingItemsCount(pendingRequests.length);
      return pendingRequests.length;
    } catch (error) {
      console.error('Failed to get pending items count:', error);
      return 0;
    }
  }, []);

  // Sync pending requests
  const syncPendingRequests = useCallback(async () => {
    if (isSyncing || !online || !serverReachable || isOfflineModeEnabled) {
      return;
    }

    try {
      setIsSyncing(true);
      const stats = await processPendingRequests();
      setSyncStats(stats);
      setLastSyncTime(new Date());
      await getPendingItemsCount();
    } catch (error) {
      console.error('Error syncing pending requests:', error);
    } finally {
      setIsSyncing(false);
    }
  }, [isSyncing, online, serverReachable, isOfflineModeEnabled, getPendingItemsCount]);

  // Sync when we reconnect
  useEffect(() => {
    if (syncOnReconnect && online && serverReachable && !isOfflineModeEnabled) {
      syncPendingRequests();
    }
  }, [online, serverReachable, isOfflineModeEnabled, syncOnReconnect, syncPendingRequests]);

  // Set up interval for auto-sync
  useEffect(() => {
    if (!autoSync) return;

    const intervalId = setInterval(() => {
      if (online && serverReachable && !isOfflineModeEnabled) {
        syncPendingRequests();
      }
    }, syncInterval);

    return () => clearInterval(intervalId);
  }, [autoSync, syncInterval, online, serverReachable, isOfflineModeEnabled, syncPendingRequests]);

  // Get initial pending items count
  useEffect(() => {
    getPendingItemsCount();
  }, [getPendingItemsCount]);

  return {
    isSyncing,
    lastSyncTime,
    pendingItemsCount,
    syncStats,
    syncPendingRequests,
    getPendingItemsCount,
  };
};

export default useOfflineSync;
