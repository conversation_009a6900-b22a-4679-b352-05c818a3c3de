import { useState } from 'react';
import {
  ChartSelection,
  ImageSelection,
  TextSelection,
} from '../pages/chatpage/ChatPage';

interface UseSelectionsProps {
  mongoProjectId: string;
}

export const useSelections = ({ mongoProjectId }: UseSelectionsProps) => {
  const [selectedCharts, setSelectedCharts] = useState<ChartSelection[]>([]);
  const [selectedImages, setSelectedImages] = useState<ImageSelection[]>([]);
  const [selectedTexts, setSelectedTexts] = useState<TextSelection[]>([]);

  const handleSelectChart = (chartId: string) => {
    setSelectedCharts((prevSelected) => {
      const isSelected = prevSelected.some((chart) => chart.id === chartId);
      if (isSelected) {
        return prevSelected.filter((chart) => chart.id !== chartId);
      }
      return [...prevSelected, { id: chartId, project_id: mongoProjectId }];
    });
  };

  const handleSelectImage = (imageId: string) => {
    setSelectedImages((prevSelected) => {
      const isSelected = prevSelected.some((image) => image.id === imageId);
      if (isSelected) {
        return prevSelected.filter((image) => image.id !== imageId);
      }
      return [...prevSelected, { id: imageId, project_id: mongoProjectId }];
    });
  };

  const handleSelectText = (responseId: string) => {
    setSelectedTexts((prevSelected) => {
      const isSelected = prevSelected.some((item) => item.id === responseId);
      if (isSelected) {
        return prevSelected.filter((item) => item.id !== responseId);
      }
      return [...prevSelected, { id: responseId, project_id: mongoProjectId }];
    });
  };

  return {
    selectedCharts,
    selectedImages,
    selectedTexts,
    handleSelectChart,
    handleSelectImage,
    handleSelectText,
  };
};
