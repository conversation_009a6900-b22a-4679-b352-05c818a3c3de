import toast from 'react-hot-toast';

export const dataURLToBlob = (dataURL: string) => {
  try {
    const parts = dataURL.split(',');
    const byteString = atob(parts[1]);
    const mimeString = parts[0].split(':')[1].split(';')[0];
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    return new Blob([ab], { type: mimeString });
  } catch (error) {
    console.error('Failed to convert data URL to Blob:', error);
    toast.error('Failed to process the image.');
    return null;
  }
};

export const isValidBase64Image = (dataURL: string) => {
  const regex = /^data:image\/(png|jpeg|jpg);base64,/;
  return regex.test(dataURL);
};
