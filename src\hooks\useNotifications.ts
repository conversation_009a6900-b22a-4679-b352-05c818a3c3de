import { useDispatch, useSelector } from 'react-redux';
import { useCallback } from 'react';
import {
  addNotification,
  markAsRead,
  markAllAsRead,
  clearOldNotifications,
  updateNotificationStatus,
  selectNotifications,
  selectUnreadCount,
  selectActionRequiredNotifications,
} from '../store/slices/notificationSlice';
import {
  generateNotificationMessage,
  generateNotificationId,
  showWorkflowToast,
  isActionRequired,
} from '../utils/notificationUtils';
import { WorkflowNotification } from '../types/workflow/index';
import useLocalStorage from './useLocalStorage';
import { useNotificationsAPI } from './useNotificationsAPI';

export const useNotifications = (enableAPI = false) => {
  const dispatch = useDispatch();
  const [currentUser] = useLocalStorage('user', null);

  // Only use API when explicitly enabled to prevent excessive calls
  const apiNotifications = enableAPI ? useNotificationsAPI() : null;

  // Fallback to Redux selectors for backward compatibility
  const notifications = useSelector(selectNotifications);
  const unreadCount = useSelector(selectUnreadCount);
  const actionRequiredNotifications = useSelector(
    selectActionRequiredNotifications
  );

  // Create notification for user assignment - uses API if enabled, fallback to local
  const createAssignmentNotification = useCallback(
    (
      workflowId: string,
      workflowTitle: string,
      assignedUserId: string,
      creatorName?: string,
      priority?: 'Low' | 'Medium' | 'High'
    ) => {
      if (apiNotifications) {
        // Delegate to API-based implementation
        return apiNotifications.createAssignmentNotification(
          workflowId,
          workflowTitle,
          assignedUserId,
          creatorName,
          priority
        );
      }

      // Fallback to local implementation
      if (!assignedUserId || assignedUserId === currentUser?.id) return;

      const message = generateNotificationMessage(
        'assignment',
        workflowTitle,
        creatorName
      );

      const notification: WorkflowNotification = {
        id: generateNotificationId(workflowId, assignedUserId, 'assignment'),
        type: 'assignment',
        workflowId,
        workflowTitle,
        message,
        actionRequired: true,
        createdAt: new Date().toISOString(),
        read: false,
        userId: assignedUserId,
        creatorName,
        priority,
      };

      dispatch(addNotification(notification));

      // Show toast notification if it's for the current user
      if (assignedUserId === currentUser?.id) {
        showWorkflowToast('assignment', message, priority);
      }
    },
    [apiNotifications, currentUser, dispatch]
  );

  // Create notification for workflow status change - uses API if enabled, fallback to local
  const createStatusChangeNotification = useCallback(
    (
      workflowId: string,
      workflowTitle: string,
      status: 'approved' | 'rejected' | 'conditional' | 'in-progress',
      targetUserId: string,
      creatorName?: string,
      priority?: 'Low' | 'Medium' | 'High',
      workflowCreatorId?: string
    ) => {
      if (apiNotifications) {
        // Delegate to API-based implementation
        return apiNotifications.createStatusChangeNotification(
          workflowId,
          workflowTitle,
          status,
          targetUserId,
          creatorName,
          priority,
          workflowCreatorId
        );
      }

      // Fallback to local implementation
      if (!targetUserId) return;

      const message = generateNotificationMessage(
        'status_change',
        workflowTitle,
        creatorName,
        status
      );

      const actionRequired = isActionRequired(
        'status_change',
        status,
        targetUserId,
        workflowCreatorId
      );

      const notification: WorkflowNotification = {
        id: generateNotificationId(workflowId, targetUserId, 'status_change'),
        type: 'status_change',
        workflowId,
        workflowTitle,
        message,
        actionRequired,
        status,
        createdAt: new Date().toISOString(),
        read: false,
        userId: targetUserId,
        creatorName,
        priority,
      };

      dispatch(addNotification(notification));

      // Show toast notification if it's for the current user
      if (targetUserId === currentUser?.id) {
        showWorkflowToast('status_change', message, priority);
      }
    },
    [apiNotifications, currentUser, dispatch]
  );

  // Create notification for workflow creation - uses API if enabled, fallback to local
  const createWorkflowCreatedNotification = useCallback(
    (
      workflowId: string,
      workflowTitle: string,
      participantUserIds: string[],
      creatorName?: string,
      priority?: 'Low' | 'Medium' | 'High'
    ) => {
      if (apiNotifications) {
        // Delegate to API-based implementation
        return apiNotifications.createWorkflowCreatedNotification(
          workflowId,
          workflowTitle,
          participantUserIds,
          creatorName,
          priority
        );
      }

      // Fallback to local implementation
      const message = generateNotificationMessage(
        'workflow_created',
        workflowTitle,
        creatorName
      );

      participantUserIds.forEach((userId) => {
        if (userId === currentUser?.id) return; // Don't notify creator

        const notification: WorkflowNotification = {
          id: generateNotificationId(workflowId, userId, 'workflow_created'),
          type: 'workflow_created',
          workflowId,
          workflowTitle,
          message,
          actionRequired: false,
          createdAt: new Date().toISOString(),
          read: false,
          userId,
          creatorName,
          priority,
        };

        dispatch(addNotification(notification));
      });

      // Show toast for current user if they're a participant
      if (participantUserIds.includes(currentUser?.id)) {
        showWorkflowToast('workflow_created', message, priority);
      }
    },
    [apiNotifications, currentUser, dispatch]
  );

  // Batch create notifications for multiple users
  const createBatchNotifications = useCallback(
    (notifications: Omit<WorkflowNotification, 'id' | 'createdAt'>[]) => {
      notifications.forEach((notificationData) => {
        const notification: WorkflowNotification = {
          ...notificationData,
          id: generateNotificationId(
            notificationData.workflowId,
            notificationData.userId,
            notificationData.type
          ),
          createdAt: new Date().toISOString(),
        };

        dispatch(addNotification(notification));

        // Show toast if it's for current user
        if (notification.userId === currentUser?.id) {
          showWorkflowToast(
            notification.type,
            notification.message,
            notification.priority
          );
        }
      });
    },
    [dispatch, currentUser]
  );

  // Mark notification as read - uses API if enabled, fallback to local
  const markNotificationAsRead = useCallback(
    (notificationId: string) => {
      if (apiNotifications) {
        return apiNotifications.markNotificationAsReadAPI(notificationId);
      }
      // Fallback to local implementation
      dispatch(markAsRead(notificationId));
    },
    [apiNotifications, dispatch]
  );

  // Mark all notifications as read - uses API if enabled, fallback to local
  const markAllNotificationsAsRead = useCallback(() => {
    if (apiNotifications) {
      return apiNotifications.markAllNotificationsAsRead();
    }
    // Fallback to local implementation
    dispatch(markAllAsRead());
  }, [apiNotifications, dispatch]);

  // Update workflow status in notifications
  const updateWorkflowNotificationStatus = useCallback(
    (
      workflowId: string,
      status: 'approved' | 'rejected' | 'conditional' | 'in-progress'
    ) => {
      dispatch(updateNotificationStatus({ workflowId, status }));
    },
    [dispatch]
  );

  // Clean up old notifications (older than specified days)
  const cleanupOldNotifications = useCallback(
    (daysToKeep: number = 30) => {
      dispatch(clearOldNotifications(daysToKeep));
    },
    [dispatch]
  );

  // Get notifications for specific workflow
  const getWorkflowNotifications = useCallback(
    (workflowId: string) => {
      return notifications.filter((n) => n.workflowId === workflowId);
    },
    [notifications]
  );

  // Get notifications for current user
  const getCurrentUserNotifications = useCallback(() => {
    return notifications.filter((n) => n.userId === currentUser?.id);
  }, [notifications, currentUser]);

  return {
    // State
    notifications,
    unreadCount,
    actionRequiredNotifications,
    isLoading: apiNotifications?.isLoading || false,
    error: apiNotifications?.error || null,
    isCreating: apiNotifications?.isCreating || false,
    isMarkingAsRead: apiNotifications?.isMarkingAsRead || false,

    // Actions
    createAssignmentNotification,
    createStatusChangeNotification,
    createWorkflowCreatedNotification,
    createBatchNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    updateWorkflowNotificationStatus,
    cleanupOldNotifications,

    // API Actions (only available when API is enabled)
    createNotificationAPI: apiNotifications?.createNotificationAPI,
    refreshNotifications: apiNotifications?.refreshNotifications,

    // Getters
    getWorkflowNotifications,
    getCurrentUserNotifications,
  };
};
