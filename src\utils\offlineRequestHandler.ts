import { toast } from 'react-hot-toast';
import { addToStore, getAllFromStore, deleteFromStore, OFFLINE_STORES } from './offlineStorage';
import { getNetworkStatus } from './networkUtils';

// Types for offline requests
export interface OfflineRequest {
  id?: number;
  url: string;
  method: string;
  body?: any;
  headers?: Record<string, string>;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

// Default options for offline requests
const DEFAULT_OPTIONS = {
  maxRetries: 3,
  retryDelay: 5000, // 5 seconds
};

/**
 * Queue a request to be executed when online
 */
export const queueOfflineRequest = async (
  request: Omit<OfflineRequest, 'timestamp' | 'retryCount' | 'maxRetries'>,
  options = DEFAULT_OPTIONS
): Promise<void> => {
  const offlineRequest: OfflineRequest = {
    ...request,
    timestamp: Date.now(),
    retryCount: 0,
    maxRetries: options.maxRetries,
  };

  try {
    await addToStore(OFFLINE_STORES.PENDING_REQUESTS, offlineRequest);
    toast.success('Request saved for later when you\'re back online', {
      id: 'offline-request',
      duration: 3000,
    });
  } catch (error) {
    console.error('Failed to queue offline request:', error);
    toast.error('Failed to save request for offline use', {
      id: 'offline-request-error',
    });
  }
};

/**
 * Execute a single offline request
 */
export const executeOfflineRequest = async (
  request: OfflineRequest
): Promise<{ success: boolean; response?: any; error?: any }> => {
  try {
    const response = await fetch(request.url, {
      method: request.method,
      headers: {
        'Content-Type': 'application/json',
        ...request.headers,
      },
      body: request.body ? JSON.stringify(request.body) : undefined,
    });

    if (!response.ok) {
      throw new Error(`Request failed with status ${response.status}`);
    }

    const data = await response.json();
    return { success: true, response: data };
  } catch (error) {
    return { success: false, error };
  }
};

/**
 * Process all pending offline requests
 */
export const processPendingRequests = async (): Promise<{
  successful: number;
  failed: number;
  remaining: number;
}> => {
  // Check if we're online
  const { online } = getNetworkStatus();
  if (!online) {
    return { successful: 0, failed: 0, remaining: 0 };
  }

  try {
    // Get all pending requests
    const pendingRequests: OfflineRequest[] = await getAllFromStore(OFFLINE_STORES.PENDING_REQUESTS);
    
    if (pendingRequests.length === 0) {
      return { successful: 0, failed: 0, remaining: 0 };
    }

    let successful = 0;
    let failed = 0;
    let remaining = 0;

    // Process each request
    for (const request of pendingRequests) {
      const { success } = await executeOfflineRequest(request);

      if (success) {
        // If successful, remove from queue
        await deleteFromStore(OFFLINE_STORES.PENDING_REQUESTS, request.id!);
        successful++;
      } else {
        // If failed, increment retry count
        request.retryCount++;
        
        if (request.retryCount >= request.maxRetries) {
          // If max retries reached, remove from queue
          await deleteFromStore(OFFLINE_STORES.PENDING_REQUESTS, request.id!);
          failed++;
        } else {
          // Otherwise, keep in queue for next attempt
          remaining++;
        }
      }
    }

    // Show toast notification with results
    if (successful > 0) {
      toast.success(`Synchronized ${successful} offline ${successful === 1 ? 'change' : 'changes'}`, {
        id: 'sync-success',
        duration: 3000,
      });
    }

    if (failed > 0) {
      toast.error(`Failed to synchronize ${failed} ${failed === 1 ? 'item' : 'items'}`, {
        id: 'sync-error',
        duration: 5000,
      });
    }

    return { successful, failed, remaining };
  } catch (error) {
    console.error('Error processing pending requests:', error);
    return { successful: 0, failed: 0, remaining: 0 };
  }
};

/**
 * Wrapper for fetch that handles offline mode
 */
export const offlineFetch = async <T>(
  url: string,
  options: RequestInit & { offlineEnabled?: boolean } = {}
): Promise<T> => {
  const { online } = getNetworkStatus();
  const offlineEnabled = options.offlineEnabled !== false;
  
  // If we're online, make the request normally
  if (online) {
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      // If request fails and offline is enabled, queue it
      if (offlineEnabled && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(options.method || 'GET')) {
        await queueOfflineRequest({
          url,
          method: options.method || 'GET',
          body: options.body ? JSON.parse(options.body.toString()) : undefined,
          headers: options.headers as Record<string, string>,
        });
        
        throw new Error('Request failed, but it has been queued for later');
      }
      throw error;
    }
  } else if (offlineEnabled) {
    // If we're offline and it's a mutation, queue it
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(options.method || 'GET')) {
      await queueOfflineRequest({
        url,
        method: options.method || 'GET',
        body: options.body ? JSON.parse(options.body.toString()) : undefined,
        headers: options.headers as Record<string, string>,
      });
      
      throw new Error('You are offline. Your changes will be saved and synchronized when you\'re back online.');
    } else {
      // For GET requests, try to get from cache
      throw new Error('You are offline. This data cannot be loaded right now.');
    }
  } else {
    throw new Error('You are offline and this operation is not available offline.');
  }
};
