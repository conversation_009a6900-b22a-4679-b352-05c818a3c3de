import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './baseQuery';
import {
  GET_PROJECT_BY_ID,
  DELETE_PROJECT_BY_ID,
  GET_PROJECTS_BY_USER_ID,
} from './constants/projectServiceConstants';

interface PaginatedQueryParams {
  userId: string;
  page: number;
  size: number;
}

export const projectService = createApi({
  reducerPath: 'projectService',
  baseQuery: baseQuery,
  endpoints: (builder) => ({
    getRecentProjects: builder.query<any, string>({
      query: (userId) => ({
        url: GET_PROJECTS_BY_USER_ID + userId,
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
    }),
    getRecentProjectsPaginated: builder.query<any, PaginatedQueryParams>({
      query: ({ userId, page, size }) => ({
        url: `${GET_PROJECTS_BY_USER_ID}${userId}?page=${page}&size=${size}`,
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
    }),
    getProjectById: builder.query<any, string>({
      query: (projectId) => ({
        url: GET_PROJECT_BY_ID + projectId,
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
    }),
    deleteProjectById: builder.mutation<any, string>({
      query: (projectId) => ({
        url: DELETE_PROJECT_BY_ID + projectId,
        method: 'PUT',
      }),
    }),
    getDemoFiles: builder.query<any, void>({
      query: () => ({
        url: '/api/v1/files/list',
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
    }),
  }),
});

export const {
  useGetRecentProjectsQuery,
  useGetProjectByIdQuery,
  useDeleteProjectByIdMutation,
  useGetRecentProjectsPaginatedQuery,
  useGetDemoFilesQuery,
} = projectService;
