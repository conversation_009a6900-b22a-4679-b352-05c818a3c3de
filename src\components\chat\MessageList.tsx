import React from 'react';
import CircularLoader from '../../assets/loader/CircularLoader';
import style from '../../assets/styles/ChatPage.module.css';
import Chip from '../common/chips/Chip';
import LongResponseIndicator from '../common/LongResponseIndicator';
import {
  Bar,
  Line,
  Pie,
  Doughnut,
  Radar,
  PolarArea,
  Bubble,
  Scatter,
} from 'react-chartjs-2';
import Checkbox from '@mui/material/Checkbox';
import { TextSelection } from '../../types/chat';

type ChartType =
  | 'bar'
  | 'line'
  | 'pie'
  | 'doughnut'
  | 'radar'
  | 'polarArea'
  | 'bubble'
  | 'scatter'
  | 'area'
  | 'horizontal';

const ChartComponentMap: Record<ChartType, React.ComponentType<any>> = {
  bar: Bar,
  line: Line,
  pie: Pie,
  doughnut: Doughnut,
  radar: Radar,
  polarArea: PolarArea,
  bubble: Bubble,
  scatter: Scatter,
  area: Line,
  horizontal: Bar,
};

interface MessageListProps {
  currentStreamingMessageId?: string | number | null;
  uploadedFile?: any;
  messages: any[];
  selectedCharts: any[];
  selectedImages: any[];
  selectedTexts: TextSelection[];
  handleSelectChart: (chart: any) => void;
  handleSelectImage: (image: any) => void;
  handleSelectText: (responseId: string) => void;
  codeInput?: boolean;
  loadingFinalResponse?: boolean;
  isLongResponse?: boolean;
  onSendSelected: (text: string) => void;
}

const MessageList: React.FC<MessageListProps> = ({
  loadingFinalResponse,
  codeInput,
  isLongResponse,
  messages,
  selectedCharts,
  selectedImages,
  selectedTexts,
  handleSelectChart,
  handleSelectImage,
  handleSelectText,
  currentStreamingMessageId,
  onSendSelected,
}) => {
  const renderMessage = (message: any, index: number) => {
    if (message.type === 'chart' && message.chartData != null) {
      const chartId = message.chartData?.chartId;
      const chartType: ChartType = message.chartData?.type as ChartType;
      const ChartComponent = ChartComponentMap[chartType] || Bar;

      const isSelected = selectedCharts.some((chart) => chart.id === chartId);

      return (
        <div
          key={index}
          className={style.messageContainer}
          style={{
            flexDirection: message.sender === 'user' ? 'row-reverse' : 'row',
            gap: message.sender === 'user' ? '10px' : '0px',
          }}
        >
          {message.image ? (
            <img
              src={message.image}
              alt={`${message.sender} avatar`}
              className={style.avatar}
            />
          ) : (
            <div className={style.avatar}></div>
          )}
          <div
            className={style.chartContainer}
            style={{ border: isSelected ? '1px solid black' : '' }}
          >
            <div className={style.chartSelectionHeader}>
              <Checkbox
                checked={isSelected}
                onChange={() => handleSelectChart(chartId)}
                size="small"
                sx={{
                  color: '#666',
                  '&.Mui-checked': {
                    color: '#174e86',
                  },
                }}
              />
            </div>
            <ChartComponent
              data={message.chartData.data}
              options={{
                ...message.chartData.options,
                plugins: {
                  ...message.chartData.options?.plugins,
                  legend: {
                    ...message.chartData.options?.plugins?.legend,
                    labels: {
                      ...message.chartData.options?.plugins?.legend?.labels,
                      color:
                        document.documentElement.getAttribute('data-theme') ===
                        'dark'
                          ? '#ffffff'
                          : '#333333',
                    },
                  },
                  title: {
                    ...message.chartData.options?.plugins?.title,
                    color:
                      document.documentElement.getAttribute('data-theme') ===
                      'dark'
                        ? '#ffffff'
                        : '#333333',
                  },
                },
                scales: message.chartData.options?.scales
                  ? {
                      ...message.chartData.options.scales,
                      x: {
                        ...message.chartData.options.scales?.x,
                        ticks: {
                          ...message.chartData.options.scales?.x?.ticks,
                          color:
                            document.documentElement.getAttribute(
                              'data-theme'
                            ) === 'dark'
                              ? '#b0bec5'
                              : '#666666',
                        },
                        grid: {
                          ...message.chartData.options.scales?.x?.grid,
                          color:
                            document.documentElement.getAttribute(
                              'data-theme'
                            ) === 'dark'
                              ? 'rgba(255, 255, 255, 0.1)'
                              : 'rgba(0, 0, 0, 0.1)',
                        },
                      },
                      y: {
                        ...message.chartData.options.scales?.y,
                        ticks: {
                          ...message.chartData.options.scales?.y?.ticks,
                          color:
                            document.documentElement.getAttribute(
                              'data-theme'
                            ) === 'dark'
                              ? '#b0bec5'
                              : '#666666',
                        },
                        grid: {
                          ...message.chartData.options.scales?.y?.grid,
                          color:
                            document.documentElement.getAttribute(
                              'data-theme'
                            ) === 'dark'
                              ? 'rgba(255, 255, 255, 0.1)'
                              : 'rgba(0, 0, 0, 0.1)',
                        },
                      },
                    }
                  : undefined,
              }}
            />
          </div>
        </div>
      );
    }

    if (message.type == 'chartImage') {
      const imageId = message.content.imageId;
      const isSelected = selectedImages.some((image) => image.id === imageId);
      return (
        <div
          className={style.messageContainer}
          style={{
            flexDirection: message.sender === 'user' ? 'row-reverse' : 'row',
            gap: message.sender === 'user' ? '10px' : '0px',
          }}
        >
          <div className={style.avatar}></div>
          <div
            className={style.imageChartContainer}
            style={{ border: isSelected ? '1px solid black' : '' }}
          >
            <div className={style.chartSelectionHeader}>
              <Checkbox
                checked={isSelected}
                onChange={() => handleSelectImage(imageId)}
                size="small"
                sx={{
                  color: '#666',
                  '&.Mui-checked': {
                    color: '#174e86',
                  },
                }}
              />
            </div>
            <div className={style.chartImagViewer}>
              <img
                src={message.content.image}
                style={{ width: '500px', height: '300px' }}
              />
            </div>
          </div>
        </div>
      );
    }
    return (
      <div
        key={index}
        className={style.messageContainer}
        style={{
          flexDirection: message.sender === 'user' ? 'row-reverse' : 'row',
          gap: message.sender === 'user' ? '10px' : '0px',
        }}
      >
        {message.image ? (
          <img
            src={message.image}
            alt={`${message.sender} avatar`}
            className={style.avatar}
          />
        ) : (
          <div className={style.avatar}></div>
        )}
        {message.content === 'Processing your request...' ||
        message.content === 'Processing your file...' ? (
          <>
            <CircularLoader
              text={
                message.content === 'Processing your request...'
                  ? 'Processing your request...'
                  : 'Processing your file...'
              }
            />
          </>
        ) : (
          <div id="chatMessage" className={style.messageContent}>
            {/* Show long response indicator if this is the current message and it's taking long */}

            <Chip
              loadingFinalResponse={
                loadingFinalResponse && message.id === currentStreamingMessageId
              }
              codeInput={codeInput && message.id === currentStreamingMessageId}
              sender={message.sender}
              timestamp={message.timestamp}
              id={message.timestamp}
              noOfPages={message.noOfPages}
              icon={message.icon}
              label={message.content}
              type={message.type}
              isSuggestedQuestions={message.isSuggestedQuestions}
              handleOnClick={message.handleOnClick}
              onSelect={handleSelectText}
              responseId={message.responseId}
              isSelected={selectedTexts.some(
                (text) => text.id === message.responseId
              )}
              showExport={true}
              onSendSelected={onSendSelected}
              preDefinedPrompt={message.preDefinedPrompt}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {messages.map((message, index) => (
        <div
          key={index}
          className={message.sender === 'user' ? style.sender : style.receiver}
        >
          {renderMessage(message, index)}
        </div>
      ))}
    </>
  );
};

export default MessageList;
