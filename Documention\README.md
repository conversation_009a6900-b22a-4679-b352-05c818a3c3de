# Neuquip

A modern React TypeScript application for interactive chat, document analysis, data visualization, and workflow management with AI integration.

## Overview

Neuquip is a comprehensive web application that combines AI-powered document analysis, interactive data visualization, and collaborative workflow management. It features a modern UI with dark/light theme support, responsive design, and a rich set of visualization tools.

## Key Features

### Chat System

- Real-time messaging with AI-powered responses
- Document analysis with automatic chart and data extraction
- File upload supporting multiple formats (PDF, DOCX, CSV, TXT, etc.)
- Suggested questions and contextual responses
- Ability to save insights to sketchbooks

### Sketchbook

- Interactive canvas for data visualization and presentation
- Multiple page support with customizable page sizes (A4, A5, Letter, Legal)
- Rich visualization options:
  - Bar, line, pie, doughnut, radar, polar area charts
  - Scatter and bubble plots
  - Gantt charts and timelines
  - Organization charts
  - Flow diagrams with node connections
- Drag-and-drop interface with resizable elements
- Template system for quick starts
- Export and sharing capabilities
- Markdown and rich text editing with Toast UI Editor
- Dark theme support for all chart types

### Workflow Management

- Create approval workflows for documents and sketchbooks
- Assign team members with specific roles
- Set priorities, due dates, and required actions
- Track approval status (approved, rejected, conditionally approved)
- Add notes and comments for collaboration
- Email notifications and status tracking

### User Management

- User profiles with customizable images
- Role-based access control
- Project organization and management
- Awaiting actions dashboard

## Tech Stack

### Frontend

- React 18 with TypeScript
- Vite for fast development and building
- Redux Toolkit for state management
- React Router for navigation
- Material-UI (MUI) for component library
- React Hook Form with Zod for form validation

### Data Visualization

- Chart.js with react-chartjs-2
- ReactFlow for flow diagrams
- AG Grid for data tables
- Gantt charts with gantt-task-react
- React Grid Layout for responsive layouts

### Rich Text Editing

- Toast UI Editor for markdown editing
- Lexical framework integration
- React Markdown for rendering

### API Communication

- RTK Query for data fetching and caching
- Axios for HTTP requests

### Utilities

- React Dropzone for file uploads
- React Color and React Colorful for color picking
- React Datepicker for date selection
- PDF handling with jspdf and pdf-lib
- File export with html2canvas and file-saver

## Project Structure

```
src/
├── assets/            # Static assets (images, styles, loaders)
├── components/        # React components
│   ├── chat/          # Chat-related components
│   ├── common/        # Reusable UI components (buttons, inputs, etc.)
│   ├── layout/        # Layout components (headers, navigation)
│   ├── specific/      # Feature-specific components
│   ├── templates/     # Template components
│   └── workflow/      # Workflow-related components
├── constants/         # Application constants
├── contexts/          # React contexts (Dialog, Theme)
├── hooks/             # Custom React hooks
│   ├── chat/          # Chat-related hooks
│   ├── sketchbook/    # Sketchbook-related hooks
│   └── workflow/      # Workflow-related hooks
├── mocks/             # Mock data for development
├── pages/             # Page components
│   ├── admin/         # Admin dashboard
│   ├── awaitedactions/# Awaited actions pages
│   ├── chatpage/      # Chat interface
│   ├── homeFinal/     # Home page
│   ├── loginpage/     # Authentication
│   ├── sketchbookpage/# Sketchbook interface
│   └── workflow/      # Workflow management
├── services/          # API services
│   └── constants/     # API endpoint constants
├── store/             # Redux store
│   ├── selectors/     # Redux selectors
│   └── slices/        # Redux slices
├── styles/            # Global styles
├── theme/             # Theme configuration
├── types/             # TypeScript type definitions
└── utils/             # Utility functions
```

## Installation

### Prerequisites

- Node.js (Latest LTS version)
- npm or yarn

### Setup

1. Clone the repository:

   ```bash
   git clone [your-repository-url]
   cd NEUQUIPFE
   ```

2. Install dependencies:

   ```bash
   npm install
   # or
   yarn install
   ```

3. Environment Setup:
   Create a `.env` file in the root directory with required configurations:

   ```
   VITE_API_BASE_URL=your_api_base_url
   VITE_AI_BASE_URL=your_ai_service_url
   ```

4. Start development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production (with increased memory allocation)
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

## Feature Documentation

### Chat System

The chat interface allows users to interact with an AI assistant that can analyze documents, extract data, and generate visualizations. Users can upload files, ask questions, and receive contextual responses with charts and data tables.

Key components:

- `ChatPage`: Main chat interface
- `ChatInput`: Input component for messages and file uploads
- `MessageList`: Displays chat messages and responses
- `ChatHeader`: Controls for chat settings and sketchbook integration

### Sketchbook

The sketchbook provides a canvas for creating visual presentations with charts, text, and images. It supports both page mode (for document-style layouts) and flow mode (for connected diagrams).

Key components:

- `SketchBookPage`: Main sketchbook interface
- `SketchbooksHome`: Dashboard for managing sketchbooks
- `DropableEditor`: Canvas for placing and arranging elements
- `ChartPropertyController`: Controls for customizing charts and elements
- `SketchBookController`: Sidebar with available elements and tools

### Workflow Management

The workflow system enables users to create approval processes for documents and sketchbooks, assign team members, and track approval status.

Key components:

- `WorkFlow`: Main workflow creation interface
- `WorkflowTeam`: Team member assignment and management
- `WorkflowNotes`: Notes and comments for collaboration

## Dark Mode Support

The application supports both light and dark themes with consistent styling across all components, including charts, editors, and UI elements.

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## Developer

Manzoor Chopan

## License

[Add license information here]
