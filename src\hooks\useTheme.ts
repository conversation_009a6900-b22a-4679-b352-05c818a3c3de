import { theme } from '../theme';

export const useTheme = () => {
  return theme;
};

// Helper type to get nested theme values with dot notation
type DotNotation<
  T extends Record<string, any>,
  Depth extends number[] = [],
> = Depth['length'] extends 3 // Limit recursion to 3 levels
  ? never
  : {
      [K in keyof T]: T[K] extends Record<string, any>
        ?
            | `${K & string}`
            | `${K & string}.${DotNotation<T[K], [...Depth, 1]> & string}`
        : K;
    }[keyof T];

// Helper type for theme values
export type ThemeValue = DotNotation<typeof theme>;
