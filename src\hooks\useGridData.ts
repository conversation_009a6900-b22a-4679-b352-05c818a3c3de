import { useState, useEffect, useRef, useCallback } from 'react';
import { useDataGrid } from './useDataGrid';
import { ViewMode } from '../components/common/viewToggle/ViewToggle';

interface UseGridDataProps<T> {
  data: T[] | undefined;
  isLoading: boolean;
  isError: boolean | unknown;
  searchField: keyof T;
  isPaginated?: boolean;
  fetchNextPage?: () => void;
  isFetchingNextPage?: boolean;
  hasNextPage?: boolean;
  resetPage?: () => void;
  sortField?: keyof T;
  sortDirection?: 'asc' | 'desc';
  currentPage?: number;
  externalViewMode?: ViewMode;
  externalSetViewMode?: (mode: ViewMode) => void;
}

export function useGridData<T>({
  data,
  isLoading,
  isError,
  searchField,
  isPaginated = false,
  fetchNextPage,
  isFetchingNextPage,
  hasNextPage,
  resetPage,
  currentPage = 1,
  externalViewMode,
  externalSetViewMode,
}: UseGridDataProps<T>) {
  const [accumulatedData, setAccumulatedData] = useState<T[]>([]);
  const isFirstLoad = useRef(true);
  const lastPage = useRef(currentPage);

  // Reset accumulated data when needed
  const resetAccumulatedData = useCallback(() => {
    isFirstLoad.current = true;
    lastPage.current = 1;
    setAccumulatedData([]);
    resetPage?.();
  }, [resetPage]);

  useEffect(() => {
    if (!data) return;

    setAccumulatedData((prev) => {
      const newData = data.filter(
        (item: any) => !prev.some((p: any) => p.id === item.id)
      );
      const updatedData = [...prev, ...newData];

      return updatedData;
    });
  }, [data, currentPage]);

  const { viewMode, setViewMode, handleSearch, filteredData } = useDataGrid({
    data: accumulatedData,
    isLoading: isLoading && isFirstLoad.current,
    isError: !!isError,
    searchField,
    externalViewMode,
    externalSetViewMode,
  });

  const handleLoadMore = useCallback(() => {
    if (isPaginated && !isFetchingNextPage && hasNextPage && fetchNextPage) {
      fetchNextPage();
    }
  }, [isPaginated, isFetchingNextPage, hasNextPage, fetchNextPage]);

  return {
    viewMode,
    setViewMode,
    handleSearch,
    filteredData,
    handleLoadMore,
    isPaginated,
    isFetchingNextPage,
    hasMore: isPaginated ? hasNextPage : false,
    resetAccumulatedData,
    totalItems: accumulatedData.length,
  };
}
