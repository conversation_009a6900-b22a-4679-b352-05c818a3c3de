import React, { useRef, useEffect } from 'react';
import { Editor } from '@toast-ui/react-editor';
import '@toast-ui/editor/dist/toastui-editor.css';
import '@toast-ui/editor/dist/theme/toastui-editor-dark.css';
import './ToastEditor.css';
import './ToastEditorOverride.css';

interface ToastEditorProps {
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  height?: number;
  theme?: 'light' | 'dark'; // Kept for backward compatibility
  toolbarItems?: string[][];
}

const ToastEditor: React.FC<ToastEditorProps> = ({
  value,
  onChange,
  onBlur,
  height = 300,
  theme: _, // Unused, always using light theme
}) => {
  const editorRef = useRef<any>(null);

  // Initialize with content and handle value changes
  useEffect(() => {
    if (editorRef.current) {
      const instance = editorRef.current.getInstance();
      const currentMarkdown = instance.getMarkdown();

      // Only update if the value is different to avoid infinite loops
      if (value !== currentMarkdown) {
        instance.setMarkdown(value || '', false); // false prevents triggering change event
      }
    }
  }, [value]);

  // Handle editor change
  const handleChange = () => {
    if (editorRef.current) {
      const instance = editorRef.current.getInstance();
      const markdown = instance.getMarkdown();
      onChange(markdown);
    }
  };

  // Handle blur event
  const handleBlur = () => {
    if (onBlur) {
      onBlur();
    }
  };

  return (
    <div className="toast-editor-wrapper">
      <Editor
        ref={editorRef}
        initialValue={value}
        previewStyle="vertical"
        height={`${height}px`}
        initialEditType="wysiwyg"
        useCommandShortcut={true}
        onChange={handleChange}
        onBlur={handleBlur}
        theme="light" // Always use light theme
        toolbarItems={[
          ['heading', 'bold', 'italic', 'strike'],
          ['hr', 'quote'],
          ['ul', 'ol', 'task', 'indent', 'outdent'],
          ['table'],
          ['scrollSync'],
        ]}
        hideModeSwitch={false}
      />
    </div>
  );
};

export default ToastEditor;
