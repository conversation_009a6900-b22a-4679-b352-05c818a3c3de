/**
 * Payment Integration Test Utilities
 * 
 * This file contains utilities to test the payment integration
 * with the new backend APIs.
 */

import { PaymentPlan, CreatePaymentIntentRequest } from '../../types/payment';
import { getCurrentUserId, getCurrentUserEmail } from '../auth/userHelpers';

/**
 * Test data for payment integration
 */
export const TEST_PAYMENT_PLAN: PaymentPlan = {
  id: 'test-plan-id',
  name: 'Test Plan',
  price: 99,
  currency: 'usd',
  tokens: 1000,
  description: 'Test plan for integration testing',
  maxGraphs: 10,
  isPopular: false,
  isDeleted: false,
  // Legacy fields for backward compatibility
  interval: 'month',
  features: ['Test Feature 1', 'Test Feature 2'],
};

/**
 * Create a test payment intent request
 */
export const createTestPaymentIntentRequest = (): CreatePaymentIntentRequest | null => {
  const userId = getCurrentUserId();
  const userEmail = getCurrentUserEmail();

  if (!userId) {
    console.error('User not authenticated - cannot create payment intent request');
    return null;
  }

  return {
    planId: TEST_PAYMENT_PLAN.id,
    userId: userId,
    customerEmail: userEmail || '<EMAIL>',
    metadata: {
      test: 'true',
      planName: TEST_PAYMENT_PLAN.name,
      planPrice: TEST_PAYMENT_PLAN.price.toString(),
    },
  };
};

/**
 * Validate payment plan structure
 */
export const validatePaymentPlan = (plan: PaymentPlan): boolean => {
  const requiredFields = ['id', 'name', 'price', 'currency', 'tokens', 'description', 'maxGraphs'];
  
  for (const field of requiredFields) {
    if (!(field in plan) || plan[field as keyof PaymentPlan] === undefined) {
      console.error(`Missing required field: ${field}`);
      return false;
    }
  }

  // Validate data types
  if (typeof plan.price !== 'number' || plan.price < 0) {
    console.error('Invalid price value');
    return false;
  }

  if (typeof plan.tokens !== 'number' || plan.tokens < 0) {
    console.error('Invalid tokens value');
    return false;
  }

  if (typeof plan.maxGraphs !== 'number' || plan.maxGraphs < 0) {
    console.error('Invalid maxGraphs value');
    return false;
  }

  return true;
};

/**
 * Test API endpoint URLs
 */
export const testApiEndpoints = () => {
  const endpoints = {
    getPlans: '/api/v1/plans/get-all',
    getPlanDetails: '/api/v1/plans/get-by-id',
    initiatePayment: '/api/v1/payments/initiate',
    getPaymentHistory: '/api/v1/payments/get-all',
  };

  console.log('Payment API Endpoints:', endpoints);
  return endpoints;
};

/**
 * Mock payment response for testing
 */
export const createMockPaymentResponse = () => {
  return {
    clientSecret: 'pi_test_1234567890_secret_test',
    paymentIntentId: 'pi_test_1234567890',
    subscriptionId: 'sub_test_1234567890',
  };
};

/**
 * Log payment integration status
 */
export const logPaymentIntegrationStatus = () => {
  const userId = getCurrentUserId();
  const userEmail = getCurrentUserEmail();
  
  console.log('=== Payment Integration Status ===');
  console.log('User ID:', userId);
  console.log('User Email:', userEmail);
  console.log('Authentication Status:', !!userId);
  console.log('Test Plan Valid:', validatePaymentPlan(TEST_PAYMENT_PLAN));
  console.log('API Endpoints:', testApiEndpoints());
  console.log('================================');
};
