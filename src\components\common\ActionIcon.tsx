import styled from '@emotion/styled';

export const ActionIcon = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
  border: 1px solid transparent;
  border-radius: 6px;
  background-color: transparent;
  color: var(--color-text-primary);
  cursor: pointer;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
  height: 24px;
  width: 24px;
  box-sizing: border-box;
  flex-shrink: 0;
  position: relative;

  &:hover {
    background-color: var(--color-background-tertiary);
    border-color: var(--color-border);
  }

  &:active {
    background-color: var(--color-primary-light);
    color: var(--color-primary-contrast);
  }

  &:disabled {
    color: var(--color-text-disabled);
    cursor: not-allowed;
  }

  & svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  @media (max-width: 768px) {
    padding: 5px;
    height: 22px;
    width: 22px;

    & svg {
      width: 14px;
      height: 14px;
    }
  }

  @media (max-width: 480px) {
    padding: 4px;
    height: 20px;
    width: 20px;

    & svg {
      width: 12px;
      height: 12px;
    }
  }
`;
