import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './baseQuery';
import { NOTIFICATION_ENDPOINTS } from './constants/notificationServiceConstants';

// API Response Types - Updated to match actual backend response
export interface ApiNotificationRecipient {
  userId: string;
  readAt: string | null;
}

export interface ApiNotificationResponse {
  id: string;
  workflowId: string;
  title: string;
  content: string;
  emailSent: boolean;
  recipients: ApiNotificationRecipient[];
  createdOn: string;
  updatedOn: string;
  type?: 'assignment' | 'status_change' | 'workflow_created';
  priority?: 'Low' | 'Medium' | 'High';
  status?: 'approved' | 'rejected' | 'conditional' | 'in-progress';
  actionRequired?: boolean;
}

export interface CreateNotificationRequest {
  workflowId: string;
  title: string;
  content: string;
  notifyTarget?: 'participants' | 'creator' | 'both';
}

export interface CreateNotificationResponse {
  id: string;
  message?: string;
}

export interface GetNotificationsResponse {
  timestamp: string;
  path: string;
  status: number;
  success: boolean;
  message: string;
  error: string;
  data: ApiNotificationResponse[];
}

export interface MarkAsReadResponse {
  success: boolean;
  message?: string;
}

export const notificationApi = createApi({
  reducerPath: 'notificationApi',
  baseQuery,
  tagTypes: ['Notification'],
  endpoints: (builder) => ({
    // Create a new notification
    createNotification: builder.mutation<
      CreateNotificationResponse,
      CreateNotificationRequest
    >({
      query: ({ notifyTarget = 'participants', ...notificationData }) => {
        const queryParam = `?notify=${notifyTarget}`;
        return {
          url: `${NOTIFICATION_ENDPOINTS.CREATE_NOTIFICATION}${queryParam}`,
          method: 'POST',
          body: notificationData,
        };
      },
      invalidatesTags: ['Notification'],
    }),

    // Get all notifications for a user
    getAllNotifications: builder.query<GetNotificationsResponse, string>({
      query: (userId) => ({
        url: `${NOTIFICATION_ENDPOINTS.GET_ALL_NOTIFICATIONS}/${userId}`,
        method: 'GET',
      }),
      providesTags: ['Notification'],
    }),

    // Mark notification as read
    markNotificationAsRead: builder.mutation<
      MarkAsReadResponse,
      { notificationId: string; userId: string }
    >({
      query: ({ notificationId, userId }) => ({
        url: `${NOTIFICATION_ENDPOINTS.MARK_AS_READ}/${notificationId}/mark-as-read/${userId}`,
        method: 'PUT',
      }),
      invalidatesTags: ['Notification'],
    }),
  }),
});

export const {
  useCreateNotificationMutation,
  useGetAllNotificationsQuery,
  useMarkNotificationAsReadMutation,
} = notificationApi;
