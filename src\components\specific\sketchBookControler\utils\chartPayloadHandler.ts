export const chartPayloadHandler = (
  chart: any,
  selectedChartType: string,
  sketchbookId: string,
  activePage: any
) => {
  switch (selectedChartType) {
    case 'bar':
    case 'line':
    case 'pie':
    case 'doughnut':
    case 'radar':
    case 'horizontal':
    case 'area':
      return {
        datasets: chart.data.datasets.map((dataset: any) => ({
          ...dataset,
          borderColor: Array.isArray(dataset.borderColor)
            ? dataset.borderColor[0]
            : dataset.borderColor,
          backgroundColor: Array.isArray(dataset.backgroundColor)
            ? dataset.backgroundColor
            : [dataset.backgroundColor],
          label: dataset.label || chart.data.labels[dataset.data.length - 1],
        })),
        labels: chart.data.labels,
        options: chart.options,
        pageIndex: activePage,
        sketchbookId: sketchbookId,
        type: chart.options.indexAxis === 'y' ? 'horizontal' : chart.type,
      };
    case 'polarArea':
      return {
        datasets: chart.data.datasets.map((dataset: any) => ({
          ...dataset,
          borderColor: Array.isArray(dataset.borderColor)
            ? dataset.borderColor
            : [dataset.borderColor],
          backgroundColor: Array.isArray(dataset.backgroundColor)
            ? dataset.backgroundColor
            : [dataset.backgroundColor],
        })),
        labels: chart.data.labels,
        options: chart.options,
        pageIndex: activePage,
        sketchbookId: sketchbookId,
        title: chart?.plugins?.title?.text,
        type: chart.type,
      };
    case 'bubble':
    case 'scatter':
      return {
        // datasets: chart.data.datasets,
        datasets: chart.data.datasets.map((dataset: any) => ({
          ...dataset,
          borderColor: '',
        })),
        labels: chart.data.labels,
        options: chart.options,
        pageIndex: activePage,
        sketchbookId: sketchbookId,
        title: chart?.plugins?.title?.text,
        type: chart.type,
      };
    case 'textarea':
      return {
        type: 'textarea',
        pageIndex: activePage,
        sketchbookId: sketchbookId,
        title: chart?.plugins?.title?.text || 'Text Area',
        data: {
          markdown: chart.data.markdown,
          style: chart.data.style,
        },
        options: chart.options,
      };
    case 'gauge':
      return {
        type: 'gauge',
        pageIndex: activePage,
        sketchbookId: sketchbookId,
        title: chart?.plugins?.title?.text || 'Gauge Chart',
        datasets: {
          data: chart.data.percent,
          text: chart.data.text,
        },
        options: {
          nrOfLevels: chart.options.nrOfLevels || 20,
          arcWidth: chart.options.arcWidth || 0.3,
          arcPadding: chart.options.arcPadding || 0.05,
          cornerRadius: chart.options.cornerRadius || 6,
          colors: chart.options.colors || ['#FF5F6D', '#FFC371'],
          needleColor: chart.options.needleColor || '#464A4F',
          textColor: chart.options.textColor || '#464A4F',
          animate: chart.options.animate !== false,
          animDelay: chart.options.animDelay || 0,
          animateDuration: chart.options.animateDuration || 2000,
        },
      };
    case 'timeline':
      // Match the structure of the initial save payload
      console.log('Timeline chart payload handler input:', chart);
      const timelinePayload = {
        type: 'timeline',
        pageIndex: activePage,
        sketchbookId: sketchbookId,
        title: chart?.title || 'Timeline Chart',
        // Keep labels at the top level
        labels: chart.data.labels,
        // Keep datasets at the top level
        datasets: chart.data.datasets.map((dataset: any) => ({
          label: dataset.label,
          data: dataset.data.map((point: any) => {
            // Keep dates as strings to match initial save format
            const formattedPoint = {
              x:
                typeof point.x === 'number'
                  ? new Date(point.x).toISOString().split('T')[0]
                  : point.x,
              y: point.y,
              duration: point.duration || 0,
            };
            return formattedPoint;
          }),
          backgroundColor: dataset.backgroundColor,
          borderColor: dataset.borderColor,
          fill: dataset.fill,
          isTimeline: true,
        })),
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              position: 'top',
            },
            title: {
              display: true,
              text: chart?.options?.plugins?.title?.text || 'Chart',
            },
          },
          scales: {
            x: {
              type: 'time',
              time: {
                unit: chart.options?.scales?.x?.time?.unit || 'day',
              },
              title: {
                display: true,
                text: 'Date',
              },
            },
            y: {
              type: 'linear',
              display: true,
              title: {
                display: true,
                text: 'Value',
              },
              beginAtZero: true,
              reverse: chart.options?.scales?.y?.reverse ?? false,
            },
          },
        },
      };
      console.log('Timeline chart payload handler output:', timelinePayload);
      return timelinePayload;
    case 'burndown':
      // Log the input chart for debugging
      console.log(
        'Burndown chart payload handler input:',
        JSON.stringify(chart)
      );

      const burndownPayload = {
        type: 'burndown',
        pageIndex: activePage,
        sketchbookId: sketchbookId,
        title: chart?.title || 'Sprint Burndown Chart',
        labels: chart.data.labels,
        datasets: [
          {
            label: 'Ideal Burndown',
            data: chart.data.datasets[0].data,
            borderColor:
              chart.data.datasets[0].borderColor || 'rgba(54, 162, 235, 1)',
            backgroundColor:
              chart.data.datasets[0].backgroundColor ||
              'rgba(54, 162, 235, 0.2)',
            borderWidth: 2,
            borderDash: [5, 5],
            fill:
              chart.data.datasets[0].fill !== undefined
                ? chart.data.datasets[0].fill
                : false,
            pointRadius: 0,
          },
          {
            label: 'Actual Burndown',
            data: chart.data.datasets[1].data,
            backgroundColor:
              chart.data.datasets[1].backgroundColor ||
              'rgba(255, 99, 132, 0.2)',
            borderColor:
              chart.data.datasets[1].borderColor || 'rgba(255, 99, 132, 1)',
            borderWidth: 2,
            fill:
              chart.data.datasets[1].fill !== undefined
                ? chart.data.datasets[1].fill
                : true,
            tension: 0.4,
          },
        ],
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              position: 'top',
            },
            title: {
              display: true,
              text:
                chart?.options?.plugins?.title?.text || 'Sprint Burndown Chart',
            },
            tooltip: {
              mode: 'index',
              intersect: false,
            },
          },
          scales: {
            x: {
              type: 'time',
              time: {
                unit: 'day',
              },
              title: {
                display: true,
                text: 'Sprint Days',
              },
            },
            y: {
              type: 'linear',
              display: true,
              title: {
                display: true,
                text: 'Story Points Remaining',
              },
              beginAtZero: true,
              min: 0,
              max: Math.max(
                ...chart.data.datasets[0].data.map((point: any) => point.y),
                ...chart.data.datasets[1].data.map((point: any) => point.y)
              ),
            },
          },
        },
      };

      // Log the output payload for debugging
      console.log(
        'Burndown chart payload handler output:',
        JSON.stringify(burndownPayload)
      );
      return burndownPayload;
    case 'gantt':
      return {
        type: 'gantt',
        pageIndex: activePage,
        sketchbookId: sketchbookId,
        title: chart?.title || 'Gantt Chart',
        tasks: chart.data.tasks.map((task: any) => ({
          id: task.id,
          name: task.name,
          start: task.start,
          end: task.end,
          progress: task.progress,
          type: task.type,
          hideChildren: task.hideChildren || false,
          displayOrder: task.displayOrder,
          project: task?.project,
          dependencies: task.dependencies || [],
        })),
        options: {
          viewMode: chart.options?.viewMode || 'Week',
          locale: chart.options?.locale || 'en-GB',
          listCellWidth: chart.options?.listCellWidth || '155px',
          columnWidth: chart.options?.columnWidth || 60,
          barCornerRadius: chart.options?.barCornerRadius || 3,
          fontSize: chart.options?.fontSize || '12px',
          barHeight: chart.options?.barHeight || 40,
          headerHeight: chart.options?.headerHeight || 50,
          rtl: chart.options?.rtl || false,
        },
      };
    default:
      return chart;
  }
};
