.pageContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-background-light) 0%, var(--color-primary-light) 100%);
  padding: 2rem 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.backLink {
  position: absolute;
  top: 2rem;
  left: 2rem;
  color: var(--color-text-secondary);
  font-weight: 500;
  text-transform: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.backLink:hover {
  background: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
}

.pageTitle {
  color: var(--color-text-primary);
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.pageSubtitle {
  color: var(--color-text-secondary);
  font-size: 1.125rem;
  line-height: 1.6;
}

.formContainer {
  margin-bottom: 3rem;
  animation: slideUp 0.6s ease-out;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 3rem 2rem;
  text-align: center;
}

.errorAlert {
  width: 100%;
  max-width: 500px;
  border-radius: 12px;
}

.backButton {
  background: var(--color-primary);
  color: white;
  padding: 0.75rem 2rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.backButton:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
}

.securityNotice {
  background: var(--color-background);
  border: 1px solid var(--color-success-border);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.securityText {
  color: var(--color-text-secondary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.securityText:last-child {
  margin-bottom: 0;
}

.supportInfo {
  text-align: center;
  padding: 1rem;
  border-top: 1px solid var(--color-border-light);
}

.supportText {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.supportLink {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.supportLink:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode support */
[data-theme="dark"] .pageContainer {
  background: linear-gradient(135deg, var(--color-background-dark) 0%, rgba(var(--color-primary-rgb), 0.1) 100%);
}

[data-theme="dark"] .securityNotice {
  background: var(--color-background-dark);
  border-color: var(--color-border-dark);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .supportInfo {
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .backLink:hover {
  background: rgba(var(--color-primary-rgb), 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
  .pageContainer {
    padding: 1rem 0;
  }

  .container {
    padding: 0 0.5rem;
  }

  .header {
    margin-bottom: 2rem;
    padding: 1rem 0;
  }

  .backLink {
    position: static;
    margin-bottom: 1rem;
    align-self: flex-start;
  }

  .pageTitle {
    font-size: 2rem;
  }

  .pageSubtitle {
    font-size: 1rem;
  }

  .formContainer {
    margin-bottom: 2rem;
  }

  .securityNotice {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .securityText {
    font-size: 0.875rem;
    flex-direction: column;
    gap: 0.25rem;
  }
}

@media (max-width: 480px) {
  .pageContainer {
    padding: 0.5rem 0;
  }

  .header {
    padding: 0.5rem 0;
  }

  .pageTitle {
    font-size: 1.75rem;
  }

  .errorContainer {
    padding: 2rem 1rem;
  }

  .securityNotice {
    padding: 0.75rem;
  }

  .securityText {
    font-size: 0.8rem;
  }
}

/* Loading states */
.container:has(.loadingSpinner) {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

/* Focus states for accessibility */
.backLink:focus,
.backButton:focus,
.supportLink:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .pageContainer {
    background: white;
  }
  
  .backLink,
  .supportInfo {
    display: none;
  }
}
