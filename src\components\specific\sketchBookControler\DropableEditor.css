/* Ensure the layout background is styled */

.dropable-container {
  overflow: auto;
  background-color: #f5f5f5;
  max-height: 80vh;
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: default;
  background-image: radial-gradient(#ccc 1px, transparent 1px);
  background-size: 10px 10px;
  cursor: grab;
}

/* Style the chart container to fit the grid space */
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  border: 1px solid #47b565;
  background-color: rgb(254, 254, 254);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* contain: size layout; */
  will-change: transform;
  backface-visibility: hidden;
}

/* Adjust the chart display within the container */
.chart-container canvas {
  max-width: 100%; /* Ensure the chart width does not overflow */
  max-height: 100%;
  cursor: default;
}

/* Style the draggable handle for better UX */
.drag-handle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.1);
  cursor: move;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  cursor: grab;
}

.drag-icon {
  color: #666;
  font-size: 18px;
}

.react-resizable-handle {
  cursor: se-resize;
}

.transform-wrapper {
  transition: transform 0.3s ease-out !important;
}

.transform-content {
  transition: transform 0.3s ease-out !important;
}

.zoom-pan-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: grab;
}

.zoom-pan-container:active {
  cursor: grabbing;
}

.zoom-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 1000;
}

.zoom-controls button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  margin: 0 4px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.zoom-controls button:hover {
  background: #f5f5f5;
}

.zoom-controls button svg {
  width: 18px;
  height: 18px;
}

.transform-component-wrapper {
  cursor: grab;
}

.transform-component-wrapper:active {
  cursor: grabbing;
}

.editor-container {
  margin-top: 270px;
  width: 559px;
  height: 793px;
  background-color: white;
  box-shadow: 0 0 10px rgba(253, 238, 238, 0.1);
  overflow: hidden;
  position: relative;
}

.dotted-container {
  border: 2px dotted #ccc;
  margin: 20px;
  overflow: hidden;
}

.page-container {
  background-color: white;
  box-shadow: 0 0 10px rgba(253, 238, 238, 0.1);
  overflow: hidden;
  position: relative;
  /* contain: size layout; */
  will-change: transform;
  position: relative;
  overflow: hidden;
}

.chart-container.selected {
  border: 2px solid #007bff;
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 5px;
  background-color: #f0f0f0;
}

.drag-handle {
  cursor: grab;
  font-size: 18px;
  color: #888;
}

.remove-chart {
  position: absolute;
  top: 5px;
  right: 5px;
  background: none;
  border: none;
  color: #ff4d4d;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  margin: 0;
  z-index: 10;
}

.remove-chart:hover {
  color: #ff0000;
}

.chart-wrapper {
  position: relative;
  width: 100%;
  height: calc(100% - 20px); /* Account for drag handle */
  overflow: hidden;
}

.chart-wrapper canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
}

.react-grid-item {
  display: flex;
  flex-direction: column;
  touch-action: manipulation;
  cursor: move;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

/* Tablet optimizations for grid items */
@media (min-width: 768px) and (max-width: 1024px) {
  .react-grid-item {
    touch-action: manipulation;
    min-height: 60px;
    min-width: 60px;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }

  .react-grid-item:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10;
  }

  .react-grid-item.react-draggable-dragging {
    transform: scale(1.05);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    touch-action: none;
  }

  .react-grid-item.react-resizable-resizing {
    touch-action: none;
    z-index: 1000;
  }

  .react-grid-item .react-resizable-handle {
    width: 24px;
    height: 24px;
    background-size: 20px 20px;
    opacity: 0.8;
    border-radius: 4px;
    background-color: rgba(0, 123, 255, 0.1);
    border: 2px solid rgba(0, 123, 255, 0.3);
  }

  .react-grid-item:hover .react-resizable-handle,
  .react-grid-item.react-resizable-resizing .react-resizable-handle {
    opacity: 1;
    background-color: rgba(0, 123, 255, 0.2);
    border-color: rgba(0, 123, 255, 0.5);
  }

  .react-grid-item .react-resizable-handle::after {
    content: '';
    position: absolute;
    right: 3px;
    bottom: 3px;
    width: 8px;
    height: 8px;
    background: linear-gradient(
      -45deg,
      transparent 0%,
      transparent 40%,
      rgba(0, 123, 255, 0.8) 40%,
      rgba(0, 123, 255, 0.8) 60%,
      transparent 60%
    );
  }

  .drag-handle {
    font-size: 20px;
    padding: 8px;
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .drag-handle:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  .drag-handle:active {
    cursor: grabbing;
    background-color: rgba(0, 0, 0, 0.15);
  }

  .remove-chart {
    font-size: 18px;
    padding: 8px;
    min-height: 44px;
    min-width: 44px;
    top: 8px;
    right: 8px;
    background-color: rgba(220, 53, 69, 0.1);
    border-radius: 50%;
    transition: all 0.2s ease;
  }

  .remove-chart:hover {
    background-color: rgba(220, 53, 69, 0.2);
    transform: scale(1.1);
  }

  .context-menu-button {
    position: absolute;
    top: 8px;
    right: 56px;
    font-size: 18px;
    padding: 8px;
    min-height: 44px;
    min-width: 44px;
    background-color: rgba(0, 123, 255, 0.1);
    border: none;
    border-radius: 50%;
    color: rgba(0, 123, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;
  }

  .context-menu-button:hover {
    background-color: rgba(0, 123, 255, 0.2);
    transform: scale(1.1);
    color: rgba(0, 123, 255, 1);
  }
}

/* Touch-specific styles for all devices */
@media (pointer: coarse) {
  .react-grid-item {
    touch-action: manipulation;
  }

  .react-grid-item.react-draggable-dragging,
  .react-grid-item.react-resizable-resizing {
    touch-action: none;
  }

  .drag-handle {
    min-height: 44px;
    min-width: 44px;
  }

  .remove-chart {
    min-height: 44px;
    min-width: 44px;
  }

  .react-grid-item .react-resizable-handle {
    min-width: 24px;
    min-height: 24px;
  }

  .context-menu-button {
    position: absolute;
    top: 8px;
    right: 56px;
    font-size: 16px;
    padding: 8px;
    min-height: 44px;
    min-width: 44px;
    background-color: rgba(0, 123, 255, 0.1);
    border: none;
    border-radius: 50%;
    color: rgba(0, 123, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;
  }

  .context-menu-button:active {
    background-color: rgba(0, 123, 255, 0.3);
    transform: scale(0.95);
  }
}

.react-grid-item.react-grid-placeholder {
  background: #ddd;
  opacity: 0.2;
  transition-duration: 100ms;
  z-index: 2;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.react-grid-item.react-draggable-dragging {
  transition: none;
  z-index: 3;
}

.react-grid-item.resizing {
  z-index: 2;
}

.react-grid-item .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  cursor: se-resize;
  z-index: 2;
}

/* Remove transition during drag */
.react-grid-item.react-draggable-dragging {
  transition: none !important;
}

/* Remove transition during resize */
.react-grid-item.resizing {
  transition: none !important;
}

.chart-container textarea {
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text-primary);
  transition:
    color 0.3s ease,
    background-color 0.3s ease;
  background-color: var(--color-background-secondary);
}

.chart-container textarea:focus {
  outline: none;
  box-shadow: inset 0 0 0 2px var(--color-primary);
}

/* Add specific styles for A0 pages */
.page-container[data-size='a0'] {
  background-size: 50px 50px; /* Larger grid size for A0 */
}

.react-flow-container {
  background-image: radial-gradient(var(--color-border) 1px, transparent 1px);
  background-size: 15px 15px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  transition: border-color 0.3s ease;
}

.react-flow-container .react-flow__controls {
  bottom: 20px;
  right: 20px;
}

.react-flow-container .react-flow__background {
  background-color: var(--color-background-secondary);
  transition: background-color 0.3s ease;
}

.react-flow__panel.left {
  left: 0;
  width: fit-content;
}

/* Add these styles to your CSS file */
.chart-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Specific styles for gauge charts */
.chart-container[data-type='gauge'] .chart-wrapper {
  padding: 10px;
}

.chart-container[data-type='gauge'] .chart-wrapper > div {
  width: 100% !important;
  height: 100% !important;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.chart-wrapper img {
  max-width: 100%;
  max-height: 100%;
  display: block;
  margin: auto;
}

/* Enhanced Editor Controls - Make them more visible and interactive */
.editor-controls {
  position: fixed;
  top: 70px; /* Move down to avoid potential header overlap */
  left: 20px;
  z-index: 9999; /* Ensure it's above all other elements */
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  background-color: rgba(
    23,
    78,
    134,
    0.95
  ); /* Increased opacity for better visibility */
  backdrop-filter: blur(5px);
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  max-width: calc(100% - 40px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation:
    attention 3s infinite,
    glow 1.5s infinite alternate;
  outline: 3px solid #ffcc00;
}

@keyframes attention {
  0% {
    transform: scale(1);
    box-shadow: 0 0 15px rgba(23, 78, 134, 0.5);
  }
  50% {
    transform: scale(1.03);
    box-shadow: 0 0 25px rgba(255, 255, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 15px rgba(23, 78, 134, 0.5);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px rgba(255, 204, 0, 0.5);
  }
  to {
    box-shadow:
      0 0 25px rgba(255, 204, 0, 0.8),
      0 0 40px rgba(255, 255, 255, 0.4);
  }
}

.control-group {
  display: flex;
  gap: 10px;
  border-right: 2px solid rgba(255, 255, 255, 0.4);
  padding-right: 12px;
  margin-right: 12px;
}

.control-group:last-child {
  border-right: none;
  padding-right: 0;
  margin-right: 0;
}

.editor-controls button {
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition:
    transform 0.2s ease,
    background-color 0.2s ease,
    box-shadow 0.2s ease;
  font-size: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.editor-controls button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  transition: opacity 0.2s ease;
}

.editor-controls button:hover {
  background-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
  animation: wiggle 0.5s ease;
}

@keyframes wiggle {
  0% {
    transform: translateY(-3px) rotate(0deg);
  }
  25% {
    transform: translateY(-3px) rotate(-5deg);
  }
  75% {
    transform: translateY(-3px) rotate(5deg);
  }
  100% {
    transform: translateY(-3px) rotate(0deg);
  }
}

.editor-controls button:hover::after {
  opacity: 1;
}

.editor-controls button:active {
  background-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.editor-controls button.active {
  background-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.7);
}

.editor-controls button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.grid-controls {
  display: flex;
  align-items: center;
}

.grid-controls label {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 15px;
  color: white;
  white-space: nowrap;
  font-weight: bold;
}

.grid-controls input[type='range'] {
  width: 130px;
  height: 8px;
  -webkit-appearance: none;
  appearance: none;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  outline: none;
}

.grid-controls input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.grid-controls input[type='range']::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

/* Show a clear label for the editor controls */
.editor-controls::after {
  content: 'Editor Controls ↑';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(23, 78, 134, 0.95);
  color: white;
  padding: 8px 15px;
  border-radius: 0 0 8px 8px;
  font-size: 14px;
  font-weight: bold;
  white-space: nowrap;
  margin-top: 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

/* Add tooltips for each button */
[title] {
  position: relative;
}

[title]:hover::before {
  content: attr(title);
  position: absolute;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  z-index: 10000;
}

/* Responsive Adjustments - Maintain visibility on small screens */
@media (max-width: 768px) {
  .editor-controls {
    flex-direction: column;
    max-width: none;
    width: auto;
    overflow: visible;
    top: 10px;
    left: 10px;
  }

  .control-group {
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding-bottom: 8px;
    margin-bottom: 8px;
    padding-right: 0;
    margin-right: 0;
  }

  .grid-controls {
    display: flex;
  }
}

/* Help Tooltip that appears on first load */
.help-tooltip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(23, 78, 134, 0.9);
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1100;
  max-width: 400px;
  text-align: center;
  animation: fadeInUp 0.5s ease forwards;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.help-tooltip p {
  margin: 0 0 15px 0;
  font-size: 16px;
  line-height: 1.4;
}

.help-tooltip button {
  background-color: white;
  color: rgba(23, 78, 134, 1);
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s ease;
}

.help-tooltip button:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(-50%, calc(-50% + 20px));
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

/* Debug message styling */
.debug-message {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 10px 15px;
  border-radius: 6px;
  font-size: 14px;
  z-index: 9999;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  max-width: 80%;
  text-align: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, 10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

/* Make focus state very visible */
.editor-controls:focus {
  outline: 4px solid #ffcc00;
  box-shadow: 0 0 0 8px rgba(255, 204, 0, 0.3);
}

.editor-controls button:focus {
  outline: 3px solid white;
  z-index: 10;
  transform: scale(1.1);
}

/* Debug toggle button */
.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ff5722;
  color: white;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  z-index: 9999;
  transition: all 0.3s ease;
}

.debug-toggle:hover {
  background-color: #ff7043;
  transform: scale(1.1);
}

.debug-toggle:active {
  transform: scale(0.95);
}

/* Debug panel */
.debug-panel {
  position: fixed;
  bottom: 70px;
  right: 20px;
  width: 300px;
  background-color: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  z-index: 9998;
  font-family: monospace;
  max-height: 50vh;
  overflow-y: auto;
}

.debug-panel h3 {
  margin-top: 0;
  color: #ff5722;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 8px;
}

.debug-panel div {
  margin-bottom: 8px;
  word-break: break-word;
}

.debug-panel button {
  background-color: #444;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 8px;
  cursor: pointer;
}

.debug-panel button:hover {
  background-color: #666;
}

/* Make the editor controls EXTREMELY visible when using debug mode */
.debug-toggle + .editor-controls,
.debug-panel + .editor-controls {
  outline: 5px solid red !important;
  animation: flashOutline 1s infinite alternate !important;
}

@keyframes flashOutline {
  from {
    outline-color: red;
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
  }
  to {
    outline-color: yellow;
    box-shadow: 0 0 30px rgba(255, 255, 0, 0.7);
  }
}

/* Emergency Fallback Controls - Ultra Compact Version */
.emergency-controls {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 2px;
  padding: 2px 3px;
  background-color: rgba(23, 78, 134, 0.7);
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  z-index: 1000;
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(2px);
  width: auto;
  font-family: inherit;
}

.emergency-controls button {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 400;
  padding: 1px 4px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 2px;
  cursor: pointer;
  font-size: 9px;
  transition: all 0.1s ease;
  min-width: 32px;
  height: 18px;
  text-align: center;
  box-shadow: none;
}

.emergency-controls button:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.emergency-controls button:active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(1px);
}

/* Remove the animation to make it less obtrusive */
@keyframes soft-pulse {
  0%,
  100% {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }
}
