import { useState, useEffect } from 'react';
import {
  isSamsungDevice,
  isAndroidDevice,
  hasDragDropIssues,
} from '../utils/touchUtils';

interface TabletDetection {
  isTablet: boolean;
  isTabletPortrait: boolean;
  isTabletLandscape: boolean;
  isTouchDevice: boolean;
  isSamsungDevice: boolean;
  isAndroidDevice: boolean;
  hasDragDropIssues: boolean;
  screenSize: 'mobile' | 'tablet' | 'desktop';
}

export const useTabletDetection = (): TabletDetection => {
  const [detection, setDetection] = useState<TabletDetection>({
    isTablet: false,
    isTabletPortrait: false,
    isTabletLandscape: false,
    isTouchDevice: false,
    isSamsungDevice: false,
    isAndroidDevice: false,
    hasDragDropIssues: false,
    screenSize: 'desktop',
  });

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      // Check if device supports touch
      const isTouchDevice =
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        (navigator as any).msMaxTouchPoints > 0;

      // Tablet detection based on screen size and touch capability
      const isTabletSize = width >= 768 && width <= 1024;
      const isTablet = isTabletSize && isTouchDevice;

      // Orientation detection
      const isTabletPortrait = isTablet && height > width;
      const isTabletLandscape = isTablet && width > height;

      // Screen size categorization
      let screenSize: 'mobile' | 'tablet' | 'desktop' = 'desktop';
      if (width < 768) {
        screenSize = 'mobile';
      } else if (width >= 768 && width <= 1024) {
        screenSize = 'tablet';
      }

      // Device detection
      const isSamsung = isSamsungDevice();
      const isAndroid = isAndroidDevice();
      const hasDragIssues = hasDragDropIssues();

      setDetection({
        isTablet,
        isTabletPortrait,
        isTabletLandscape,
        isTouchDevice,
        isSamsungDevice: isSamsung,
        isAndroidDevice: isAndroid,
        hasDragDropIssues: hasDragIssues,
        screenSize,
      });
    };

    // Initial check
    checkDevice();

    // Listen for resize events
    window.addEventListener('resize', checkDevice);
    window.addEventListener('orientationchange', checkDevice);

    return () => {
      window.removeEventListener('resize', checkDevice);
      window.removeEventListener('orientationchange', checkDevice);
    };
  }, []);

  return detection;
};
