import React from 'react';
import CircularLoader from '../../assets/loader/CircularLoader';
import { useTheme } from '../../contexts/ThemeContext';

interface LongResponseIndicatorProps {
  isLongResponse: boolean;
}

const LongResponseIndicator: React.FC<LongResponseIndicatorProps> = ({
  isLongResponse,
}) => {
  if (!isLongResponse) return null;

  const { isDarkMode } = useTheme();

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        padding: '10px 16px',
        backgroundColor: isDarkMode
          ? 'rgba(255, 167, 38, 0.15)'
          : 'rgba(255, 167, 38, 0.08)',
        borderRadius: '8px',
        marginTop: '12px',
        marginBottom: '12px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        animation: 'fadeIn 0.5s ease-in-out',
      }}
    >
      <style>
        {`
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
          }
        `}
      </style>
      <div
        style={{
          marginRight: '16px',
          display: 'flex',
          alignItems: 'center',
          animation: 'pulse 1.5s infinite ease-in-out',
        }}
      >
        <style>
          {`
            @keyframes pulse {
              0% { opacity: 0.6; }
              50% { opacity: 1; }
              100% { opacity: 0.6; }
            }
          `}
        </style>
        <CircularLoader size="small" />
      </div>
      <span
        style={{
          color: isDarkMode ? '#ffb74d' : '#e65100',
          fontWeight: 400,
          fontSize: '14px',
          lineHeight: '1.5',
        }}
      >
        This request is taking longer than usual. I'm preparing a detailed
        response for you...
      </span>
    </div>
  );
};

export default LongResponseIndicator;
