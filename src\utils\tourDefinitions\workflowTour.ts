// Workflow page tour definition
export const workflowTour = {
  id: 'workflow-tour',
  name: 'Workflow Feature Tour',
  // Add video tour information
  videoTour: {
    videoId: 'CV_FHGyCT58?si=1cRqD5-afSksUoYM', // Replace with your actual YouTube video ID
    description:
      'This video tutorial will guide you through all the features of the Workflow tool, showing you how to create and manage approval workflows for your documents effectively.',
  },
  // Keep the steps as a fallback in case video can't be loaded
  // steps: [
  //   {
  //     element: '.workflow-container',
  //     popover: {
  //       title: 'Workflow Interface',
  //       description: 'Create and manage approval workflows for your documents.',
  //       position: 'bottom',
  //     },
  //   },
  //   {
  //     element: '.workflow-form',
  //     popover: {
  //       title: 'Workflow Form',
  //       description: 'Fill out this form to create a new workflow.',
  //       position: 'right',
  //     },
  //   },
  //   {
  //     element: '.team-section',
  //     popover: {
  //       title: 'Team Management',
  //       description: 'Add team members and assign roles for the workflow.',
  //       position: 'left',
  //     },
  //   },
  //   {
  //     element: '.notes-section',
  //     popover: {
  //       title: 'Notes',
  //       description: 'Add notes and instructions for your team members.',
  //       position: 'top',
  //     },
  //   },
  //   {
  //     element: '.networkStatus',
  //     popover: {
  //       title: 'Network Status',
  //       description:
  //         'Monitor your internet connection status. The indicator will show if you are online, offline, or have a slow connection.',
  //       position: 'bottom',
  //     },
  //   },
  // ],
};
