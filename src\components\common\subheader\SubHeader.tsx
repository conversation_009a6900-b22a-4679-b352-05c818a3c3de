import React from 'react';
import Search from '../../specific/search/Search';
import style from './SubHeader.module.css';

type HeadingProps = {
  type: 'recent' | 'other';
  title?: string;
  onSearch: (searchTerm: string) => void;
  hanldeSeeAll?: any;
};

const SubHeader: React.FC<HeadingProps> = ({
  title,
  type,
  onSearch,
  hanldeSeeAll,
}) => {
  return (
    <div className={style.searchContainer}>
      {/* <h4
        className={style.recentProject}
      >
        {title}
      </h4> */}
      <Search onSearch={onSearch} />
      {/* {type === 'recent' && (
        <button onClick={hanldeSeeAll} className={style.buttonStyle}>
          See All
        </button>
      )} */}
    </div>
  );
};

export default SubHeader;
