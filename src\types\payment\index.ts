export interface PaymentPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  tokens: number;
  description: string;
  maxGraphs: number;
  supportLevel?: string | null;
  isPopular?: boolean;
  isDeleted?: boolean;
  createdOn?: string;
  updatedOn?: string;
  // Legacy fields for backward compatibility
  interval?: 'month' | 'year';
  features?: string[];
  stripePriceId?: string;
}

export interface PaymentIntent {
  id: string;
  clientSecret: string;
  amount: number;
  currency: string;
  status: string;
}

export interface PaymentMethod {
  id: string;
  type: string;
  card?: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
}

export interface PaymentFormData {
  planId: string;
  customerEmail?: string;
  customerName?: string;
  billingAddress?: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
}

export interface PaymentState {
  isLoading: boolean;
  isProcessing: boolean;
  currentPlan: PaymentPlan | null;
  paymentIntent: PaymentIntent | null;
  paymentMethods: PaymentMethod[];
  error: string | null;
  success: boolean;
  lastPayment: PaymentRecord | null;
}

export interface PaymentRecord {
  id: string;
  paymentIntentId: string;
  userId: string;
  planId: string;
  amount: number;
  currency: string;
  status: 'created' | 'succeeded' | 'failed' | 'pending' | 'canceled';
  dated?: string | null;
  createdOn?: string;
  updatedOn?: string;
  // Legacy fields for backward compatibility
  planName?: string;
  createdAt?: string;
  paymentMethodId?: string;
}

export interface CreatePaymentIntentRequest {
  planId: string;
  userId: string;
  customerEmail?: string;
  metadata?: Record<string, string>;
}

export interface CreatePaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  subscriptionId: string;
}

export interface ConfirmPaymentRequest {
  paymentIntentId: string;
  paymentMethodId: string;
}

export interface ConfirmPaymentResponse {
  success: boolean;
  paymentIntent: PaymentIntent;
  error?: string;
}

export interface PaymentError {
  type: 'card_error' | 'validation_error' | 'api_error' | 'network_error';
  code?: string;
  message: string;
  param?: string;
}

export interface StripeElementsOptions {
  appearance?: {
    theme?: 'stripe' | 'night' | 'flat';
    variables?: Record<string, string>;
  };
  loader?: 'auto' | 'always' | 'never';
}

export interface PaymentFormProps {
  plan: PaymentPlan;
  onSuccess: (paymentRecord: PaymentRecord) => void;
  onError: (error: PaymentError) => void;
  onCancel?: () => void;
}

export interface PaymentSuccessProps {
  paymentRecord: PaymentRecord;
  onContinue: () => void;
}

export interface PaymentErrorProps {
  error: PaymentError;
  onRetry: () => void;
  onCancel: () => void;
}
