import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './baseQuery';
import { PAYMENT_ENDPOINTS } from './constants/paymentServiceConstants';
import {
  PaymentPlan,
  PaymentRecord,
  CreatePaymentIntentRequest,
  CreatePaymentIntentResponse,
} from '../types/payment';

// API Response wrapper interface
interface ApiResponse<T> {
  timestamp: string;
  path: string;
  status: number;
  success: boolean;
  message: string;
  error: string;
  data: T;
}

// Paginated response interface (keeping for future use)
// interface PaginatedResponse<T> {
//   content: T[];
//   pageSize: number;
//   currentPage: number;
//   totalItems: number;
//   totalPages: number;
// }

export const paymentApi = createApi({
  reducerPath: 'paymentApi',
  baseQuery,
  tagTypes: ['PaymentIntent', 'PaymentMethod', 'PaymentHistory', 'Plans'],
  endpoints: (builder) => ({
    // Get available payment plans
    getPaymentPlans: builder.query<PaymentPlan[], void>({
      query: () => ({
        url: PAYMENT_ENDPOINTS.GET_PLANS,
        method: 'GET',
      }),
      transformResponse: (response: ApiResponse<PaymentPlan[]>) =>
        response.data,
      providesTags: ['Plans'],
    }),

    // Get specific plan details
    getPlanDetails: builder.query<PaymentPlan, string>({
      query: (planId) => ({
        url: `${PAYMENT_ENDPOINTS.GET_PLAN_DETAILS}/${planId}`,
        method: 'GET',
      }),
      transformResponse: (response: ApiResponse<PaymentPlan>) => response.data,
      providesTags: ['Plans'],
    }),

    // Initiate payment - Production Stripe Integration
    initiatePayment: builder.mutation<
      CreatePaymentIntentResponse,
      CreatePaymentIntentRequest
    >({
      query: (data) => ({
        url: PAYMENT_ENDPOINTS.INITIATE_PAYMENT,
        method: 'POST',
        body: {
          planId: data.planId,
          userId: data.userId,
        },
      }),
      transformResponse: (response: any) => {
        console.log('✅ Payment initiation response:', response);

        // Handle direct backend response
        if (response && response.clientSecret && response.paymentIntentId) {
          return {
            clientSecret: response.clientSecret,
            paymentIntentId: response.paymentIntentId,
            subscriptionId: response.subscriptionId,
          };
        }

        // Handle wrapped API response
        if (response && response.data) {
          const data = response.data;
          if (data && data.clientSecret && data.paymentIntentId) {
            return {
              clientSecret: data.clientSecret,
              paymentIntentId: data.paymentIntentId,
              subscriptionId: data.subscriptionId,
            };
          }
        }

        // Handle error cases
        console.error('❌ Invalid payment initiation response:', response);
        throw new Error(
          response?.message ||
            'Invalid response from payment service. Please try again or contact support.'
        );
      },
      invalidatesTags: ['PaymentIntent'],
    }),

    // Legacy endpoints - not available in current backend
    // Commenting out to prevent 404 errors

    // createPaymentIntent: builder.mutation<
    //   CreatePaymentIntentResponse,
    //   CreatePaymentIntentRequest
    // >({
    //   query: (data) => ({
    //     url: PAYMENT_ENDPOINTS.CREATE_PAYMENT_INTENT,
    //     method: 'POST',
    //     body: data,
    //   }),
    //   invalidatesTags: ['PaymentIntent'],
    // }),

    // getPaymentIntent: builder.query<PaymentIntent, string>({
    //   query: (paymentIntentId) => ({
    //     url: `${PAYMENT_ENDPOINTS.GET_PAYMENT_INTENT}/${paymentIntentId}`,
    //     method: 'GET',
    //   }),
    //   providesTags: ['PaymentIntent'],
    // }),

    // confirmPayment: builder.mutation<
    //   ConfirmPaymentResponse,
    //   ConfirmPaymentRequest
    // >({
    //   query: (data) => ({
    //     url: PAYMENT_ENDPOINTS.CONFIRM_PAYMENT,
    //     method: 'POST',
    //     body: data,
    //   }),
    //   invalidatesTags: ['PaymentIntent', 'PaymentHistory'],
    // }),

    // Payment methods endpoints are not available in the current backend
    // Commenting out to prevent 404 errors

    // getPaymentMethods: builder.query<PaymentMethod[], void>({
    //   query: () => ({
    //     url: PAYMENT_ENDPOINTS.GET_PAYMENT_METHODS,
    //     method: 'GET',
    //   }),
    //   providesTags: ['PaymentMethod'],
    // }),

    // attachPaymentMethod: builder.mutation<
    //   PaymentMethod,
    //   { paymentMethodId: string }
    // >({
    //   query: (data) => ({
    //     url: PAYMENT_ENDPOINTS.ATTACH_PAYMENT_METHOD,
    //     method: 'POST',
    //     body: data,
    //   }),
    //   invalidatesTags: ['PaymentMethod'],
    // }),

    // detachPaymentMethod: builder.mutation<void, { paymentMethodId: string }>({
    //   query: (data) => ({
    //     url: PAYMENT_ENDPOINTS.DETACH_PAYMENT_METHOD,
    //     method: 'POST',
    //     body: data,
    //   }),
    //   invalidatesTags: ['PaymentMethod'],
    // }),

    // Get payment history (new API)
    getPaymentHistory: builder.query<
      PaymentRecord[],
      { page?: number; size?: number; planId?: string; userId?: string }
    >({
      query: ({ page = 1, size = 10, planId, userId } = {}) => {
        const params = new URLSearchParams({
          page: page.toString(),
          size: size.toString(),
        });
        if (planId) params.append('planId', planId);
        if (userId) params.append('userId', userId);

        return {
          url: `${PAYMENT_ENDPOINTS.GET_PAYMENT_HISTORY}?${params.toString()}`,
          method: 'GET',
        };
      },
      transformResponse: (response: any) => {
        // Handle different response formats
        if (response?.data?.content) {
          return response.data.content;
        }
        if (response?.data && Array.isArray(response.data)) {
          return response.data;
        }
        if (Array.isArray(response)) {
          return response;
        }
        return [];
      },
      transformErrorResponse: (response: any) => {
        // Log error but don't throw - payment history is optional
        console.warn(
          'Payment history API not available:',
          response?.status || 'Unknown error'
        );
        if (response?.status === 404) {
          console.info(
            "Payment history endpoint not found - this is expected if the backend doesn't support this feature yet"
          );
        }
        return {
          status: response?.status || 500,
          message: 'Payment history temporarily unavailable',
          isOptional: true,
        };
      },
      providesTags: ['PaymentHistory'],
    }),

    // Customer and payment details endpoints are not available in current backend
    // Commenting out to prevent 404 errors

    // getPaymentDetails: builder.query<PaymentRecord, string>({
    //   query: (paymentId) => ({
    //     url: `${PAYMENT_ENDPOINTS.GET_PAYMENT_DETAILS}/${paymentId}`,
    //     method: 'GET',
    //   }),
    //   providesTags: ['PaymentHistory'],
    // }),

    // createCustomer: builder.mutation<
    //   { customerId: string },
    //   { email: string; name?: string }
    // >({
    //   query: (data) => ({
    //     url: PAYMENT_ENDPOINTS.CREATE_CUSTOMER,
    //     method: 'POST',
    //     body: data,
    //   }),
    // }),

    // updateCustomer: builder.mutation<
    //   void,
    //   { customerId: string; email?: string; name?: string }
    // >({
    //   query: (data) => ({
    //     url: PAYMENT_ENDPOINTS.UPDATE_CUSTOMER,
    //     method: 'PUT',
    //     body: data,
    //   }),
    // }),

    // getCustomer: builder.query<
    //   { id: string; email: string; name?: string },
    //   void
    // >({
    //   query: () => ({
    //     url: PAYMENT_ENDPOINTS.GET_CUSTOMER,
    //     method: 'GET',
    //   }),
    // }),
  }),
});

export const {
  useGetPaymentPlansQuery,
  useGetPlanDetailsQuery,
  useInitiatePaymentMutation,
  useGetPaymentHistoryQuery,
} = paymentApi;
