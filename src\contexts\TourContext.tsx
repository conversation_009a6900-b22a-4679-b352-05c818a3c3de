import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { driver } from 'driver.js';
import 'driver.js/dist/driver.css';

// Types
type TourStep = {
  element: string;
  popover: {
    title: string;
    description: string;
    position?: 'top' | 'right' | 'bottom' | 'left';
  };
};

type TourDefinition = {
  id: string;
  name: string;
  steps: TourStep[];
  videoTour?: {
    videoId: string;
    description?: string;
  };
};

type TourContextType = {
  startTour: (tourId: string) => void;
  endTour: () => void;
  resetTours: () => void;
  hasSeenTour: (tourId: string) => boolean;
  isActiveTour: boolean;
  availableTours: TourDefinition[];
  currentTourId: string | null;
  isVideoTour: (tourId: string) => boolean;
  getVideoTourData: (
    tourId: string
  ) => { videoId: string; title: string; description?: string } | null;
};

// Create context
const TourContext = createContext<TourContextType | undefined>(undefined);

// Tour provider props
interface TourProviderProps {
  children: ReactNode;
  tours: TourDefinition[];
}

// Local storage key for storing tour history
const TOUR_HISTORY_KEY = 'neuquip_tour_history';

export const TourProvider: React.FC<TourProviderProps> = ({
  children,
  tours,
}) => {
  const [driverObj, setDriverObj] = useState<any>(null);
  const [isActiveTour, setIsActiveTour] = useState(false);
  const [currentTourId, setCurrentTourId] = useState<string | null>(null);
  const [tourHistory, setTourHistory] = useState<Record<string, boolean>>({});

  // Initialize driver.js and load tour history from localStorage
  useEffect(() => {
    // Initialize driver with enhanced styling
    const driverInstance = driver({
      showProgress: true,
      animate: true,
      showButtons: ['next', 'previous', 'close'],
      steps: [],
      stagePadding: 10,
      smoothScroll: true,
      allowClose: true,
      // Set a proper overlay opacity that doesn't make everything too dark
      overlayOpacity: 0.5,
      // Use a custom class for styling
      popoverClass: 'neuquip-tour-popover',
      // Set a proper stage radius
      stageRadius: 5,
      // Set overlay color to a dark color but not completely black
      overlayColor: '#000',
      // Confirm before exiting the tour
      onDestroyStarted: () => {
        if (true) {
          driverInstance.destroy();
          setIsActiveTour(false);
          setCurrentTourId(null);
        }
      },
      onDestroyed: () => {
        setIsActiveTour(false);
        setCurrentTourId(null);
      },
      onHighlightStarted: () => {
        setIsActiveTour(true);
      },
      onDeselected: () => {
        setIsActiveTour(false);
        setCurrentTourId(null);
      },
    });

    setDriverObj(driverInstance);

    // Load tour history from localStorage
    const savedHistory = localStorage.getItem(TOUR_HISTORY_KEY);
    if (savedHistory) {
      try {
        setTourHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Error parsing tour history:', error);
        localStorage.removeItem(TOUR_HISTORY_KEY);
      }
    }

    return () => {
      driverInstance.destroy();
    };
  }, []);

  // Save tour history to localStorage when it changes
  useEffect(() => {
    if (Object.keys(tourHistory).length > 0) {
      localStorage.setItem(TOUR_HISTORY_KEY, JSON.stringify(tourHistory));
    }
  }, [tourHistory]);

  // Start a tour by ID
  const startTour = (tourId: string) => {
    const tourToStart = tours.find((tour) => tour.id === tourId);

    if (!tourToStart || !driverObj) {
      console.error(
        `Tour with ID ${tourId} not found or driver not initialized`
      );
      return;
    }

    // Update tour history
    setTourHistory((prev) => ({
      ...prev,
      [tourId]: true,
    }));

    setCurrentTourId(tourId);

    // Ensure elements are properly highlighted by adding a small delay
    setTimeout(() => {
      // Expand the sidebar if it's collapsed to ensure all elements are visible
      const sidebarElement = document.getElementById('sidebar');
      if (sidebarElement && sidebarElement.classList.contains('collapsed')) {
        // Find and click the expand button
        const expandButton = sidebarElement.querySelector('button');
        if (expandButton) {
          expandButton.click();
        }
      }

      // Configure driver with the tour steps
      driverObj.setSteps(tourToStart.steps);

      // Force a refresh of the driver instance to ensure proper highlighting
      driverObj.refresh();

      // Start the tour
      driverObj.drive();
    }, 500);
  };

  // End the current tour
  const endTour = () => {
    if (driverObj && isActiveTour) {
      driverObj.destroy();
      setIsActiveTour(false);
      setCurrentTourId(null);
    }
  };

  // Reset all tour history
  const resetTours = () => {
    setTourHistory({});
    localStorage.removeItem(TOUR_HISTORY_KEY);
  };

  // Check if a user has seen a specific tour
  const hasSeenTour = (tourId: string) => {
    return !!tourHistory[tourId];
  };

  // Check if a tour is a video tour
  const isVideoTour = (tourId: string) => {
    const tour = tours.find((t) => t.id === tourId);
    return !!tour?.videoTour;
  };

  // Get video tour data
  const getVideoTourData = (tourId: string) => {
    const tour = tours.find((t) => t.id === tourId);
    if (tour?.videoTour) {
      return {
        videoId: tour.videoTour.videoId,
        title: tour.name,
        description: tour.videoTour.description,
      };
    }
    return null;
  };

  const value = {
    startTour,
    endTour,
    resetTours,
    hasSeenTour,
    isActiveTour,
    availableTours: tours,
    currentTourId,
    isVideoTour,
    getVideoTourData,
  };

  return <TourContext.Provider value={value}>{children}</TourContext.Provider>;
};

// Custom hook to use the tour context
export const useTour = (): TourContextType => {
  const context = useContext(TourContext);

  if (context === undefined) {
    throw new Error('useTour must be used within a TourProvider');
  }

  return context;
};
