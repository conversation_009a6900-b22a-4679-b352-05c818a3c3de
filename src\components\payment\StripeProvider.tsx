import React, { ReactNode } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { getStripe, getStripeElementsAppearance } from '../../utils/payment/stripeHelpers';
import { useTheme } from '../../hooks/useTheme';

interface StripeProviderProps {
  children: ReactNode;
}

const StripeProvider: React.FC<StripeProviderProps> = ({ children }) => {
  const { isDarkMode } = useTheme();
  
  const stripePromise = getStripe();
  
  const options = {
    appearance: getStripeElementsAppearance(isDarkMode),
    loader: 'auto' as const,
  };

  return (
    <Elements stripe={stripePromise} options={options}>
      {children}
    </Elements>
  );
};

export default StripeProvider;
