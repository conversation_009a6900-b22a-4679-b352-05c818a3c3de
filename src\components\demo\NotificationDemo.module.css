.demoContainer {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: 'Lato', sans-serif;
}

.demoContainer h2 {
  color: #0e2f51;
  margin-bottom: 20px;
}

.demoContainer p {
  color: #6b7280;
  margin-bottom: 10px;
}

.buttonGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin: 20px 0;
}

.notificationList {
  margin-top: 30px;
  border-top: 1px solid #e5e7eb;
  padding-top: 20px;
}

.notificationList h3 {
  color: #0e2f51;
  margin-bottom: 15px;
}

.notificationItem {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background-color: #ffffff;
  transition: all 0.2s ease;
}

.notificationItem:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notificationItem.unread {
  border-left: 4px solid #3b82f6;
  background-color: #f8fafc;
}

.notificationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notificationType {
  font-size: 12px;
  font-weight: 600;
  color: #6366f1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.notificationDate {
  font-size: 12px;
  color: #9ca3af;
}

.notificationMessage {
  color: #374151;
  margin: 8px 0;
  line-height: 1.5;
}

.notificationMeta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.priority {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
  text-transform: uppercase;
  color: white;
}

.priority.high {
  background-color: #dc2626;
}

.priority.medium {
  background-color: #d97706;
}

.priority.low {
  background-color: #059669;
}

.actionRequired {
  font-size: 10px;
  background-color: #ef4444;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.unreadBadge {
  font-size: 10px;
  background-color: #3b82f6;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}
