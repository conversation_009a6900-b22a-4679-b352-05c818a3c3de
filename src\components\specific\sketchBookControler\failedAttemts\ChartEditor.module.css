.graphCanvas {
  /* border: 2px dashed #d1dfec; */
  /* border-radius: 8px; */
  padding: 20px;
  /* width: 57%; */
  width: 794px;
  height: 1123px;
  margin: 1px;
  /* max-height: 100vh; */
  position: relative;
  display: flex;
  background: #ffffff;
  flex-direction: column;
  float: right;
  font-family: 'Lato', sans-serif;
  color: #1d192b;
  overflow-x: hidden;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.graphCanvas h2 {
  font-family: 'Lato', sans-serif;
  font-size: 30px;
  font-weight: 600;
  color: #292e33;
}

.graphCanvas p {
  font-family: 'Lato', sans-serif;
  font-size: 12.36px;
  font-weight: 400;
  line-height: 18.54px;
  color: #383e45;
  padding: 10px;
}

.droppedGraphs {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  width: 100%;
}

.droppedGraph {
  position: relative;
  overflow: hidden;
  resize: both;
  width: calc(50% - 10px);
  height: 250px;
  border: 1px solid #d1dfec;
  border-radius: 8px;
  padding: 10px;
  background: #ffffff;
  box-shadow: 1px 6px 8px 0px rgba(0, 0, 0, 0.05);
}

.droppedGraph .resize-handle {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 10px;
  height: 10px;
  background: rgba(0, 0, 0, 0.3);
  cursor: se-resize;
}

.dropTarget {
  color: #677480;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.checkbox {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 4;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
}

.checkbox svg {
  font-size: 16px;
  color: #d1dfec;
}

.checkbox.checked svg {
  color: #174e86;
}

.selected {
  outline: 1px solid #174e86;
  box-shadow: 0 0 10px #174e86;
}

.droppedGraph {
  position: relative;
}

.resizeHandle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  cursor: se-resize;
}

@media (max-width: 768px) {
  .droppedGraph {
    width: 70%;
  }
}
