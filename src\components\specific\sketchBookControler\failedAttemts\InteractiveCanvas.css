/* .container {
  position: relative;
  width: 60vw;
  height: 78vh;
  overflow: hidden;
  background-color: #d8d7e8;
  cursor: move;
  margin: 5px;
}

.canvas {
  position: absolute;
  background-color: white;
  width: 700px;
  height: 800px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  will-change: transform;
}

.item {
  position: absolute;
  background-color: #f8f9ff;
  border: 1px solid #ccd0ff;
  border-radius: 4px;
  cursor: move;
  user-select: none;
}

.item-content {
  padding: 8px;
}

.resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  background-color: #ccd0ff;
  cursor: se-resize;
  border-radius: 0 0 4px 0;
}
.resize-handle::after {
  content: '';
  position: absolute;
  right: 4px;
  bottom: 4px;
  width: 4px;
  height: 4px;
  background-color: white;
  border-radius: 50%;
} */
