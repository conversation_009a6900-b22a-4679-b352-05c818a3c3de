import useLocalStorage from '../../hooks/useLocalStorage';
import { useNavigate } from 'react-router-dom';
import { useGetSketchbookByUserPaginatedQuery } from '../../services/sketchbookServices';
import SketchbookCard from '../../components/common/card/SketchbookCard';
import { useGridData } from '../../hooks/useGridData';
import { DataGridTemplate } from '../../components/templates/DataGridTemplate';
import { analyticsImagesForSketchbookHome } from '../../utils/sktechbookBoohHomeThumbnails';
import { useState, useEffect } from 'react';
import { formatTimestamp, getTimeAgo } from '../../utils/timesAgo';
import { Button } from '@mui/material';
import { IoAdd } from 'react-icons/io5';
import SketchbookTemplatesModal from '../../components/specific/sketchBookControler/SketchbookTemplatesModal';
import { useTabletDetection } from '../../hooks/useTabletDetection';
import { triggerHapticFeedback } from '../../utils/touchUtils';
import styles from './SketchbooksHome.module.css';

const SketchbooksHome = () => {
  const [userDetails] = useLocalStorage('user', null);
  const userId = userDetails?.id;
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const { isTablet, isTouchDevice, screenSize } = useTabletDetection();

  const resetPage = () => setPage(1);

  const handleOpenTemplateModal = () => {
    if (isTouchDevice) {
      triggerHapticFeedback('light');
    }
    setIsTemplateModalOpen(true);
  };

  const handleCloseTemplateModal = () => setIsTemplateModalOpen(false);

  const {
    data,
    isLoading,
    error: isError,
    isFetching,
  } = useGetSketchbookByUserPaginatedQuery(
    {
      userId,
      page,
      size: 20,
    },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const hasNextPage = data?.data
    ? data.data.totalPages > data.data.currentPage
    : false;

  const {
    viewMode,
    setViewMode,
    handleSearch,
    filteredData: filteredProjects,
    handleLoadMore,
    isPaginated,
    isFetchingNextPage,
    hasMore,
  } = useGridData({
    data: data?.data?.content,
    isLoading,
    isError,
    searchField: 'sketchbook_name',
    isPaginated: true,
    fetchNextPage: () => {
      if (!isFetching && hasNextPage) {
        setPage((prev) => prev + 1);
      }
    },
    isFetchingNextPage: isFetching && page > 1,
    hasNextPage,
    resetPage,
    sortField: 'createdDate',
    sortDirection: 'desc',
    currentPage: page,
  });

  const handleCardClick = (chartData: any) => {
    if (isTouchDevice) {
      triggerHapticFeedback('light');
    }
    navigate('/sketchbook', { state: { sketchbookId: chartData.id } });
  };

  const getRandomImage = () => {
    const randomIndex = Math.floor(
      Math.random() * analyticsImagesForSketchbookHome.length
    );
    return analyticsImagesForSketchbookHome[randomIndex];
  };

  // Create New button component
  const CreateNewButton = (
    <Button
      variant="contained"
      color="primary"
      startIcon={<IoAdd />}
      onClick={handleOpenTemplateModal}
      className={styles.createNewButton}
      sx={{
        borderRadius: '8px',
        padding: '8px 16px',
        fontWeight: 500,
        textTransform: 'none',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      }}
    >
      Create New Sketchbook
    </Button>
  );

  return (
    <>
      <DataGridTemplate
        isLoading={isLoading}
        isError={!!isError}
        isEmpty={!filteredProjects?.length}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        onSearch={handleSearch}
        loadingText="Loading sketchbooks..."
        errorTitle="Error Loading Sketchbooks"
        errorMessage="There was a problem loading your sketchbooks. Please try again."
        emptyTitle="No Sketchbooks Found"
        emptyMessage={
          data?.data?.content?.length
            ? 'No sketchbooks found matching your search.'
            : "You don't have any sketchbooks yet."
        }
        hasMore={hasMore}
        onLoadMore={handleLoadMore}
        isLoadingMore={isFetchingNextPage}
        actionButton={CreateNewButton}
        headerExtras={null}
      >
        {filteredProjects?.map((item: any, index: number) => (
          <SketchbookCard
            key={item.id}
            title={item.sketchbook_name}
            timeAgo={formatTimestamp(item?.createdOnTimestamp)}
            onClick={() => handleCardClick(item)}
            viewMode={viewMode}
            isPrivate={true}
            index={index}
            creator="you"
            thumbnails={getRandomImage()}
          />
        ))}
      </DataGridTemplate>

      {/* Template Selection Modal */}
      <SketchbookTemplatesModal
        open={isTemplateModalOpen}
        onClose={handleCloseTemplateModal}
      />
    </>
  );
};

export default SketchbooksHome;
