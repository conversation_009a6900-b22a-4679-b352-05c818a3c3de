.inputContainer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  width: 100%;
}

.fullWidth {
  width: 100%;
}

.inputLabel {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.required {
  color: var(--error-color);
  margin-left: 0.25rem;
}

.passwordContainer {
  position: relative;
  width: 100%;
}

.input {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  color: var(--text-color);
  background-color: var(--background-color);
  transition:
    border-color 0.2s ease,
    background-color 0.2s ease;
  box-sizing: border-box;
}

.input:focus {
  outline: none;
  border: 1px solid var(--secondary-color);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb, 27, 94, 161), 0.1);
}

.input:disabled {
  background-color: var(--color-grey-100);
  cursor: not-allowed;
  opacity: 0.6;
}

.input::placeholder {
  color: var(--placeholder-color);
}

.inputError {
  border-color: var(--error-color) !important;
}

.inputError:focus {
  box-shadow: 0 0 0 2px rgba(211, 47, 47, 0.1);
}

.eyeIcon {
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--secondary-color);
}

.helperText {
  font-family: var(--font-family-primary);
  font-size: 0.75rem;
  font-weight: var(--font-weight-normal);
  color: var(--trinary-color);
  margin-top: 0.25rem;
}

.error {
  color: var(--error-color);
}

/* Dark theme support */
[data-theme='dark'] .input {
  background-color: var(--color-background-secondary);
  border-color: var(--color-border);
  color: var(--color-text-primary);
}

[data-theme='dark'] .input::placeholder {
  color: var(--color-text-secondary);
}

[data-theme='dark'] .inputLabel {
  color: var(--color-text-primary);
}

[data-theme='dark'] .helperText {
  color: var(--color-text-secondary);
}

[data-theme='dark'] .eyeIcon {
  color: var(--color-primary);
}
