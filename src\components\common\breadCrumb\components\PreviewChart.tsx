import React, { useEffect, useRef } from 'react';
import {
  Bar,
  Line,
  Pie,
  Doughnut,
  Radar,
  PolarArea,
  Bubble,
  Scatter,
} from 'react-chartjs-2';
import Gau<PERSON><PERSON><PERSON> from 'react-gauge-chart';
import Gantt<PERSON>hart from '../../../specific/sketchBookControler/components/GanttChart';
import TextArea from '../../../specific/sketchBookControler/components/TextArea';
import styles from '../BreadCrumbsComponent.module.css';

interface PreviewChartProps {
  chart: {
    type: string;
    data: any;
    options?: any;
    id?: string;
  };
  width: number;
  height: number;
}

const getChartComponent = (type: string) => {
  const componentMap: Record<string, any> = {
    bar: Bar,
    line: Line,
    pie: Pie,
    doughnut: Doughnut,
    radar: Radar,
    polararea: PolarArea,
    bubble: Bubble,
    scatter: Scatter,
    area: Line,
    horizontal: Bar,
    timeline: Line,
    burndown: Line,
    gantt: Gantt<PERSON>hart,
    textarea: TextArea,
    gauge: null,
    image: null,
  };
  return componentMap[type.toLowerCase()];
};

// Helper function to create a static canvas rendering of a chart
// This can be used to generate chart images for export
export const renderChartToCanvas = async (
  chart: any,
  width: number,
  height: number
): Promise<HTMLCanvasElement | null> => {
  if (!chart) return null;

  // Handle different chart types
  if (chart.type === 'image' && chart.data?.url) {
    return new Promise((resolve) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.src = chart.data.url;

      img.onload = () => {
        const canvas = document.createElement('canvas');
        // Use a higher resolution for better quality
        canvas.width = width * 2;
        canvas.height = height * 2;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          // Enable high quality image rendering
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';

          // Fill with white background
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // Draw with proper sizing
          const imgAspect = img.width / img.height;
          const canvasAspect = width / height;

          let drawWidth,
            drawHeight,
            offsetX = 0,
            offsetY = 0;

          if (imgAspect > canvasAspect) {
            // Image is wider than canvas (relative to height)
            drawWidth = canvas.width;
            drawHeight = canvas.width / imgAspect;
            offsetY = (canvas.height - drawHeight) / 2;
          } else {
            // Image is taller than canvas (relative to width)
            drawHeight = canvas.height;
            drawWidth = canvas.height * imgAspect;
            offsetX = (canvas.width - drawWidth) / 2;
          }

          ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
        }
        resolve(canvas);
      };

      img.onerror = () => {
        const canvas = document.createElement('canvas');
        canvas.width = width * 2;
        canvas.height = height * 2;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.fillStyle = '#d32f2f';
          ctx.font = '20px Arial';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(
            'Image failed to load',
            canvas.width / 2,
            canvas.height / 2
          );
        }
        resolve(canvas);
      };

      // Timeout for image loading
      setTimeout(() => {
        if (!img.complete) {
          // Create a fallback canvas instead of triggering the error event
          const canvas = document.createElement('canvas');
          canvas.width = width * 2;
          canvas.height = height * 2;
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#d32f2f';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(
              'Image loading timed out',
              canvas.width / 2,
              canvas.height / 2
            );
          }
          resolve(canvas);
        }
      }, 5000);
    });
  }

  if (chart.type === 'textarea') {
    const canvas = document.createElement('canvas');
    canvas.width = width * 2;
    canvas.height = height * 2;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      // Set high quality text rendering
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Render text content with better quality
      ctx.fillStyle = '#000000';
      ctx.font = '28px Arial'; // Larger font size for better quality rendering

      const text = chart.data?.text || '';
      const maxLines = Math.floor(canvas.height / 40); // Adjusted for higher resolution
      const words = text.split(' ');
      let line = '';
      let y = 60; // Start higher to account for larger canvas

      for (let i = 0; i < words.length && y < canvas.height - 20; i++) {
        const testLine = line + words[i] + ' ';
        const metrics = ctx.measureText(testLine);
        const testWidth = metrics.width;

        if (testWidth > canvas.width - 40 && i > 0) {
          ctx.fillText(line, 20, y);
          line = words[i] + ' ';
          y += 40;
        } else {
          line = testLine;
        }
      }

      ctx.fillText(line, 20, y);
    }
    return Promise.resolve(canvas);
  }

  try {
    // For chart types, we'll try to find any existing canvas in the DOM
    // that corresponds to this chart and clone it for best quality
    const chartId = chart.id || '';
    const chartSelector = `#chart-${chartId} canvas`;
    const existingCanvas = document.querySelector(
      chartSelector
    ) as HTMLCanvasElement;

    if (existingCanvas) {
      // If we found an existing canvas, clone it at high resolution
      const canvas = document.createElement('canvas');
      canvas.width = width * 2;
      canvas.height = height * 2;
      const ctx = canvas.getContext('2d');

      if (ctx) {
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Scale up the existing canvas
        ctx.drawImage(
          existingCanvas,
          0,
          0,
          existingCanvas.width,
          existingCanvas.height,
          0,
          0,
          canvas.width,
          canvas.height
        );

        return canvas;
      }
    }

    // Fallback to placeholder
    const canvas = document.createElement('canvas');
    canvas.width = width * 2;
    canvas.height = height * 2;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      ctx.fillStyle = '#f5f5f5';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#333333';
      ctx.font = '28px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(`Chart: ${chart.type}`, canvas.width / 2, canvas.height / 2);
    }

    return Promise.resolve(canvas);
  } catch (error) {
    console.error('Error rendering chart to canvas:', error);

    // Return simple fallback canvas
    const canvas = document.createElement('canvas');
    canvas.width = width * 2;
    canvas.height = height * 2;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      ctx.fillStyle = '#f8f8f8';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#d32f2f';
      ctx.font = '28px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(
        'Error rendering chart',
        canvas.width / 2,
        canvas.height / 2
      );
    }

    return Promise.resolve(canvas);
  }
};

const PreviewChart: React.FC<PreviewChartProps> = ({
  chart,
  width,
  height,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<any>(null);

  console.log('chart', chart);

  // Apply print optimization when component mounts
  useEffect(() => {
    if (chartRef.current) {
      // Force chart to render at full size
      const container = chartRef.current;
      container.style.width = `${width}px`;
      container.style.height = `${height}px`;
      container.style.overflow = 'visible';

      // Add data attribute for html2canvas to recognize for high-quality rendering
      container.setAttribute('data-html2canvas-render', 'true');

      // Ensure chart container has a unique ID for canvas capture
      if (chart.id) {
        container.id = `preview-chart-${chart.id}`;
      }
    }

    // Clean up on unmount
    return () => {
      // Destroy chart instances if needed
      if (
        chartInstanceRef.current &&
        typeof chartInstanceRef.current.destroy === 'function'
      ) {
        chartInstanceRef.current.destroy();
      }
    };
  }, [width, height, chart.id]);

  if (chart.type === 'gauge') {
    const { data, options } = chart || {};

    return (
      <div
        ref={chartRef}
        className={styles.chartInnerWrapper}
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'visible',
        }}
      >
        <GaugeChart
          id={`gauge-chart-${chart.id || Math.random()}`}
          nrOfLevels={chart.options?.nrOfLevels || 3}
          colors={chart.options?.colors || ['#FF5F6D', '#FFC371', '#5CB85C']}
          arcWidth={chart?.options?.arcWidth || 0.3}
          arcPadding={chart?.options?.arcPadding || 0.05}
          cornerRadius={chart?.options?.cornerRadius || 6}
          needleColor={chart?.options?.needleColor || '#464A4F'}
          needleBaseColor={chart?.options?.needleBaseColor || '#464A4F'}
          percent={data?.percent || 0.9}
          textColor={options?.textColor || '#000000'}
          style={{ width: '100%', maxWidth: width, height: 'auto' }}
          animDelay={chart?.options?.animDelay || 0}
        />
        <div
          style={{
            position: 'absolute',
            bottom: '2px',
            color: options?.textColor || '#000000',
            fontSize: '0.5rem',
            fontWeight: '500',
            textAlign: 'center',
          }}
        >
          {data?.text || 'Your Text Here!'}
        </div>
      </div>
    );
  }

  if (chart.type === 'image') {
    return (
      <div
        ref={chartRef}
        className={styles.chartInnerWrapper}
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        <img
          src={chart.data?.url || ''}
          alt={chart.data?.alt || chart.options?.title || 'Chart image'}
          crossOrigin="anonymous"
          style={{
            maxWidth: '100%',
            maxHeight: '100%',
            objectFit: 'contain',
            display: 'block',
            // Add high-quality image rendering
            imageRendering: 'auto',
          }}
          // Prevent CORS issues and add fallback error handling
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            // Add error message
            const parent = target.parentElement;
            if (parent) {
              const errorMsg = document.createElement('div');
              errorMsg.textContent = 'Image failed to load';
              errorMsg.style.color = '#d32f2f';
              errorMsg.style.padding = '10px';
              errorMsg.style.textAlign = 'center';
              parent.appendChild(errorMsg);
            }
          }}
        />
      </div>
    );
  }

  const ChartComponent = getChartComponent(chart.type);
  if (!ChartComponent) return null;

  // Create standardized options with print optimizations and dark mode support
  const isDarkMode =
    document.documentElement.getAttribute('data-theme') === 'dark';

  const printOptimizedOptions = {
    ...chart.options,
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 0, // Disable animations for print
    },
    plugins: {
      ...chart.options?.plugins,
      legend: {
        ...chart.options?.plugins?.legend,
        labels: {
          ...chart.options?.plugins?.legend?.labels,
          color: isDarkMode ? '#ffffff' : '#333333', // Text color based on theme
          font: {
            ...chart.options?.plugins?.legend?.labels?.font,
            size: 12, // Consistent font size for printing
          },
        },
      },
      tooltip: {
        enabled: false, // Disable tooltips for print
      },
      title: {
        ...chart.options?.plugins?.title,
        color: isDarkMode ? '#ffffff' : '#333333', // Title color based on theme
      },
    },
    scales: chart.options?.scales
      ? {
          ...chart.options.scales,
          x: {
            ...chart.options.scales?.x,
            ticks: {
              ...chart.options.scales?.x?.ticks,
              color: isDarkMode ? '#b0bec5' : '#666666', // X-axis tick color based on theme
            },
            grid: {
              ...chart.options.scales?.x?.grid,
              color: isDarkMode
                ? 'rgba(255, 255, 255, 0.1)'
                : 'rgba(0, 0, 0, 0.1)', // Grid color based on theme
            },
          },
          y: {
            ...chart.options.scales?.y,
            ticks: {
              ...chart.options.scales?.y?.ticks,
              color: isDarkMode ? '#b0bec5' : '#666666', // Y-axis tick color based on theme
            },
            grid: {
              ...chart.options.scales?.y?.grid,
              color: isDarkMode
                ? 'rgba(255, 255, 255, 0.1)'
                : 'rgba(0, 0, 0, 0.1)', // Grid color based on theme
            },
          },
        }
      : undefined,
  };

  return (
    <div
      ref={chartRef}
      className={styles.chartInnerWrapper}
      style={{
        width: '100%',
        height: '100%',
        overflow: 'visible',
        position: 'relative',
      }}
    >
      <ChartComponent
        data={chart.data}
        options={printOptimizedOptions}
        // Add key to force re-render when dimensions change
        key={`chart-${chart.id}-${width}-${height}`}
      />
    </div>
  );
};

export default PreviewChart;
