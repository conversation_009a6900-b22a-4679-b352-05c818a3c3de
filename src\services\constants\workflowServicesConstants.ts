// Workflow-related API endpoints
export const SAVE_WORKFLOW = '/api/v1/workflow/save';
export const GET_WORKFLOW_BY_CREATOR_AND_CURRENT_USER =
  '/api/v1/workflow/get-by-creator-and-current-user/';
export const GET_WORKFLOW_BY_CREATOR_AND_CURRENT_USER_PAGINATED =
  '/api/v1/workflow/get-by-creator-and-current-user/';
export const GET_WORKFLOW_BY_CREATOR = '/api/v1/workflow/get-by-creator/';
export const UPDATE_WORKFLOW_STATUS = '/api/v1/workflow/update/status/';
export const GET_WORKFLOW_BY_PROJECT_ID = '/api/v1/workflow/get-by-projectid/';
export const GET_WORKFLOW_BY_ID = '/api/v1/workflow/get-by-id/';
export const GET_ALL_USERS = '/api/v1/user/get/all';
export const GET_ALL_USERS_PAGINATED = '/api/v1/user/get/all/paginated/';
