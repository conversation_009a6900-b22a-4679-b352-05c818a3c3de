import React, { createContext, useContext, ReactNode } from 'react';
import useNetworkStatus from '../hooks/useNetworkStatus';

// Define the context type
type NetworkContextType = ReturnType<typeof useNetworkStatus> & {
  // Additional methods can be added here
  setOfflineMode: (enabled: boolean) => void;
  isOfflineModeEnabled: boolean;
};

// Create the context with a default value
const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

// Props for the provider
interface NetworkProviderProps {
  children: ReactNode;
  pingInterval?: number;
  notifyOnChange?: boolean;
  pingEndpoint?: string;
  retryOnReconnect?: boolean;
}

/**
 * Network status provider component
 */
export const NetworkProvider: React.FC<NetworkProviderProps> = ({
  children,
  pingInterval = 30000,
  notifyOnChange = true,
  pingEndpoint,
  retryOnReconnect = true,
}) => {
  const networkStatus = useNetworkStatus({
    pingInterval,
    notifyOnChange,
    pingEndpoint,
    retryOnReconnect,
  });

  // State for manual offline mode toggle
  const [isOfflineModeEnabled, setIsOfflineModeEnabled] = React.useState<boolean>(
    localStorage.getItem('offlineMode') === 'true'
  );

  // Function to manually set offline mode
  const setOfflineMode = (enabled: boolean) => {
    setIsOfflineModeEnabled(enabled);
    localStorage.setItem('offlineMode', enabled.toString());
  };

  // Combine the network status with additional methods
  const value: NetworkContextType = {
    ...networkStatus,
    setOfflineMode,
    isOfflineModeEnabled,
  };

  return (
    <NetworkContext.Provider value={value}>
      {children}
    </NetworkContext.Provider>
  );
};

/**
 * Hook to use the network context
 */
export const useNetwork = (): NetworkContextType => {
  const context = useContext(NetworkContext);
  if (context === undefined) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  return context;
};

export default NetworkContext;
