import React, { useRef, useEffect, useState } from 'react';
import Select from 'react-select';
import { Fa<PERSON><PERSON>, FaPortrait, FaCog } from 'react-icons/fa';
import { BiLandscape } from 'react-icons/bi';
import styles from './BottomControlBar.module.css';
import CustomSizeModal from './CustomSizeModal';
import ConfirmDialog from '../../common/confirmDialog/ConfirmDialog';

interface BottomControlBarProps {
  pages: any[];
  activePage: string;
  setActivePage: (id: string) => void;
  addPage: () => void;
  removePage: (id: string, index: number) => void;
  pageSize: any;
  pageSizes: any[];
  onPageSizeChange: (newSize: any) => void;
  toggleOrientation: () => void;
  customSelectStyles: any;
}

const BottomControlBar: React.FC<BottomControlBarProps> = ({
  pages = [], // Add default empty array
  activePage,
  setActivePage,
  addPage,
  removePage,
  pageSize,
  pageSizes,
  onPageSizeChange,
  toggleOrientation,
}) => {
  const pagesContainerRef = useRef<HTMLDivElement>(null);
  const lastPageRef = useRef<HTMLDivElement>(null);
  const [isCustomSizeModalOpen, setIsCustomSizeModalOpen] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [pageToDelete, setPageToDelete] = useState<{
    id: string;
    index: number;
  } | null>(null);

  // Scroll to the last page when pages array changes
  useEffect(() => {
    if (lastPageRef.current && pagesContainerRef.current && pages?.length > 0) {
      const container = pagesContainerRef.current;
      const lastPage = lastPageRef.current;

      container.scrollTo({
        left:
          lastPage.offsetLeft -
          container.offsetWidth +
          lastPage.offsetWidth +
          100,
        behavior: 'smooth',
      });
    }
  }, [pages?.length]);

  // Update this section to properly handle the current page size
  const currentPageSize = (pageSize
    ? pageSizes.find(
        (size) =>
          size.value === pageSize.value &&
          size.width === pageSize.width &&
          size.height === pageSize.height
      )
    : pageSizes[0]) || {
    value: 'custom',
    label: 'Custom Size',
    width: pageSize?.width || 600,
    height: pageSize?.height || 800,
    orientation: pageSize?.orientation || 'portrait',
  };

  // Modify the pageSizesWithCustom array only if we're using a custom size
  const pageSizesWithCustom = [
    ...pageSizes,
    {
      value: 'custom',
      label: 'Custom Size',
      icon: <FaCog size={12} />,
      width: pageSize?.width || 600,
      height: pageSize?.height || 800,
      orientation: pageSize?.orientation || 'portrait',
    },
  ];

  const selectStyles = {
    control: (provided: any) => ({
      ...provided,
      minHeight: '22px',
      height: '22px',
      borderColor: '#e0e0e0',
      boxShadow: 'none',
      '&:hover': {
        borderColor: '#b3c6d9',
      },
      backgroundColor: '#f8f9fa',
      borderRadius: '3px',
    }),
    menu: (provided: any) => ({
      ...provided,
      position: 'absolute',
      width: '100%',
      zIndex: 99999,
      backgroundColor: 'white',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    }),
    menuPortal: (base: any) => ({
      ...base,
      zIndex: 99999,
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      fontSize: '11px',
      padding: '4px 8px',
      backgroundColor: state.isSelected ? '#e8e8e8' : 'white',
      color: '#333',
      '&:hover': {
        backgroundColor: '#f0f0f0',
      },
    }),
    valueContainer: (provided: any) => ({
      ...provided,
      height: '22px',
      padding: '0 6px',
      fontSize: '11px',
    }),
    indicatorsContainer: (provided: any) => ({
      ...provided,
      height: '22px',
    }),
    dropdownIndicator: (provided: any) => ({
      ...provided,
      padding: '4px',
    }),
    input: (provided: any) => ({
      ...provided,
      margin: '0',
      padding: '0',
    }),
  };
  const handlePageSizeChange = (newSize: any) => {
    if (newSize.value === 'custom') {
      setIsCustomSizeModalOpen(true);
    } else {
      onPageSizeChange(newSize);
    }
  };

  const handleDeleteClick = (pageId: string, index: number) => {
    setPageToDelete({ id: pageId, index });
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = () => {
    if (pageToDelete) {
      removePage(pageToDelete.id, pageToDelete.index);
    }
    setShowDeleteConfirm(false);
    setPageToDelete(null);
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
    setPageToDelete(null);
  };

  return (
    <div className={styles.bottomControlBar}>
      <div className={styles.pageSizeControls}>
        <Select
          value={currentPageSize}
          onChange={handlePageSizeChange}
          options={pageSizesWithCustom}
          styles={selectStyles}
          className={styles.pageSizeSelect}
          isSearchable={false}
          menuPosition="fixed"
          menuPlacement="top"
          menuPortalTarget={document.body}
          classNamePrefix="select"
        />
        <button
          onClick={toggleOrientation}
          className={styles.orientationButton}
          title={`Switch to ${pageSize?.orientation === 'portrait' ? 'landscape' : 'portrait'} orientation`}
        >
          {pageSize?.orientation === 'portrait' ? (
            <FaPortrait size={16} />
          ) : (
            <BiLandscape size={16} />
          )}
        </button>
      </div>
      <div className={styles.addPageButton}>
        <button
          className={styles.addPageButton}
          onClick={addPage}
          title="Add new page"
        >
          <FaPlus size={14} />
          <span>Add Page</span>
        </button>
      </div>
      <div className={styles.pageControls}>
        <div className={styles.pagesContainer} ref={pagesContainerRef}>
          {Array.isArray(pages) &&
            pages.map((page: any, index: number) => (
              <div
                key={page?.id || index}
                ref={index === pages.length - 1 ? lastPageRef : null}
                className={`${styles.pageItem} ${
                  activePage === page?.id ? styles.active : ''
                }`}
                onClick={() => page?.id && setActivePage(page.id)}
                title={`Page ${index + 1}`}
              >
                <span>Page {index + 1}</span>
                {pages.length > 1 && (
                  <button
                    className={styles.removeButton}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (page?.id) {
                        handleDeleteClick(page.id, index);
                      }
                    }}
                    title="Remove page"
                  >
                    ×
                  </button>
                )}
              </div>
            ))}
        </div>
      </div>
      {isCustomSizeModalOpen && (
        <CustomSizeModal
          isOpen={isCustomSizeModalOpen}
          onClose={() => setIsCustomSizeModalOpen(false)}
          onSave={(width, height) => {
            const customSize = {
              value: 'custom',
              label: 'Custom Size',
              width,
              height,
              orientation: pageSize?.orientation || 'portrait',
            };
            onPageSizeChange(customSize);
            setIsCustomSizeModalOpen(false);
          }}
          currentWidth={pageSize?.width || 600}
          currentHeight={pageSize?.height || 800}
        />
      )}

      <ConfirmDialog
        isOpen={showDeleteConfirm}
        title="Delete Page"
        message={`Are you sure you want to delete Page ${pageToDelete ? pageToDelete.index + 1 : ''}? This action cannot be undone.`}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </div>
  );
};

export default BottomControlBar;
