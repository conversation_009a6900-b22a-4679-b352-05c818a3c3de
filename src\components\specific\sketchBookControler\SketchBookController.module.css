.sketchbook-controller {
  padding: 12px;
  height: 100%;
  overflow-y: auto;
  font-size: 11px;
}

.section {
  margin-bottom: 12px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  margin: 0;
  cursor: pointer;
  background-color: var(--color-background-tertiary);
  border-radius: 3px;
  font-weight: 500;
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.caretIcon {
  width: 14px;
  height: 14px;
}

.graph-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
  gap: 6px;
  padding: 6px;
}

.graph-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 6px;
  border: 1px solid var(--color-border);
  border-radius: 3px;
  cursor: grab;
  background: var(--color-background-secondary);
  font-size: 11px;
  text-align: center;
  color: var(--color-text-primary);
  touch-action: manipulation;
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease,
    transform 0.2s ease;
}

.graph-item svg {
  width: 14px;
  height: 14px;
}

.graph-item:hover {
  background: var(--color-background-tertiary);
  border-color: var(--color-primary-light);
}

.pageToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 6px;
  font-size: 11px;
}

/* Scrollbar styles */
.sketchbook-controller::-webkit-scrollbar {
  width: 6px;
}

.sketchbook-controller::-webkit-scrollbar-track {
  background: var(--color-background-tertiary);
  border-radius: 3px;
}

.sketchbook-controller::-webkit-scrollbar-thumb {
  background: var(--color-text-secondary);
  border-radius: 3px;
}

.sketchbook-controller::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-primary);
}

.item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.item:hover {
  background-color: var(--color-background-tertiary);
  color: var(--color-text-primary);
}

.item.active {
  background-color: var(--color-primary-light);
  color: var(--color-primary-contrast);
}

.icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.removeButton {
  margin-left: auto;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #888;
}

.removeButton:hover {
  color: #333;
}

.graph-item span {
  font-size: 12px;
  color: #677480;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.graph-item:active {
  cursor: grabbing;
}

.pageSelect {
  flex: 1;
}

.pageSelect .react-select__control {
  border-color: #d1dfec;
  box-shadow: none;
}

.pageSelect .react-select__control:hover {
  border-color: #b3c6d9;
}

.pageSelect .react-select__option {
  font-size: 14px;
}

.pageSelect .react-select__option--is-selected {
  background-color: #e0e0e0;
  color: #1d192b;
}

.pageSelect .react-select__option:hover {
  background-color: #f0f0f0;
}

.pagesContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 8px;
}

.pagesContainer::-webkit-scrollbar {
  width: 4px;
}

.pagesContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.pagesContainer::-webkit-scrollbar-thumb {
  background: #d1dfec;
  border-radius: 4px;
}

.pagesContainer::-webkit-scrollbar-thumb:hover {
  background: #b3c6d9;
}

.addPageItem {
  border-top: 1px solid #e0e0e0;
  margin-top: 5px;
  padding-top: 10px;
  position: sticky;
  bottom: 0;
  background: white;
}

.pageToggle span {
  font-size: 14px;
  color: #666;
}

.graph-item[data-type='flow'] {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.graph-item[data-type='flow']:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.graph-item[data-type='flow'] svg {
  font-size: 1.2em;
  margin-right: 8px;
}

.pageControls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.orientationButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.orientationButton:hover {
  background-color: #f5f5f5;
}

/* Hide page size and pages sections when in flow mode */
[data-mode='flow'] .section:has(.pageSelect),
[data-mode='flow'] .section:has(.pagesContainer) {
  display: none;
}

/* Keep the page mode section visible */
[data-mode='flow'] .section:has(.pageToggle) {
  display: block;
}

/* Optional: adjust spacing when in flow mode */
[data-mode='flow'] .section {
  margin-bottom: 16px;
}

/* Hide flow elements section when in chart mode (pageEnabled is true) */
[data-mode='default'] .section:has(.sectionHeader:contains('Flow Elements')) {
  display: none;
}

/* Hide graphs and text tools sections when in flow mode (pageEnabled is false) */
[data-mode='flow'] .section:has(.graph-grid) {
  display: none;
}

/* Keep other sections visible */
[data-mode='flow'] .section:has(.pageToggle),
[data-mode='default'] .section:has(.pageToggle) {
  display: block;
}

.subSectionHeader {
  font-size: 0.9em;
  color: #677480;
  margin: 16px 0 8px 0;
  padding-left: 8px;
  border-left: 2px solid #d1dfec;
}

/* Mode-specific visibility */
[data-mode='flow'] .pageSizeSection,
[data-mode='flow'] .pagesSection,
[data-mode='flow'] .graphSection,
[data-mode='flow'] .textSection {
  display: none;
}

/* Keep the mode toggle section always visible */
[data-mode='flow'] .section:first-child,
[data-mode='default'] .section:first-child {
  display: block;
}

/* Show flow elements only in flow mode */
[data-mode='flow'] .flowSection {
  display: block !important;
}

/* Hide flow elements in chart mode */
[data-mode='default'] .flowSection {
  display: none;
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .sketchbook-controller {
    padding: 16px;
    font-size: 14px;
  }

  .sectionHeader {
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 8px;
    min-height: 48px;
  }

  .caretIcon {
    width: 18px;
    height: 18px;
  }

  .graph-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 12px;
    padding: 12px;
  }

  .graph-item {
    padding: 12px;
    border-radius: 8px;
    font-size: 12px;
    min-height: 80px;
    gap: 8px;
  }

  .graph-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .graph-item:active {
    transform: scale(0.95);
  }

  .graph-item svg {
    width: 20px;
    height: 20px;
  }

  .pageToggle {
    padding: 12px;
    font-size: 14px;
    gap: 12px;
  }

  .pageToggle span {
    font-size: 16px;
  }

  .orientationButton {
    width: 48px;
    height: 48px;
    border-radius: 8px;
  }

  .section {
    margin-bottom: 16px;
  }
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
  .sketchbook-controller {
    padding: 12px;
    font-size: 12px;
  }

  .sectionHeader {
    padding: 10px 12px;
    font-size: 14px;
    min-height: 44px;
  }

  .graph-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 8px;
    padding: 8px;
  }

  .graph-item {
    padding: 8px;
    font-size: 10px;
    min-height: 60px;
    gap: 4px;
  }

  .graph-item:active {
    transform: scale(0.95);
  }

  .graph-item svg {
    width: 16px;
    height: 16px;
  }

  .pageToggle {
    padding: 8px;
    font-size: 12px;
    gap: 8px;
  }

  .pageToggle span {
    font-size: 14px;
  }

  .orientationButton {
    width: 40px;
    height: 40px;
  }
}
