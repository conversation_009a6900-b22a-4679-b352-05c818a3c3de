import { useLocation, useNavigate } from 'react-router-dom';
import styles from './AwaitedActionsViewPage.module.css';
import { format } from 'date-fns';
import CustomButton from '../../components/common/button/CustomButton';
import IconButton from '../../components/common/button/IconButton';
import {
  IoCheckmark,
  IoClose,
  IoArrowBack,
  IoTime,
  IoCalendar,
  IoWarning,
} from 'react-icons/io5';
import useLocalStorage from '../../hooks/useLocalStorage';
import { RiSketching } from 'react-icons/ri';
import WorkflowParticipantFlow from '../../components/workflow/WorkflowParticipantFlow';
import WorkflowMessages from '../../components/workflow/WorkflowMessages';
import { useWorkflowActions } from '../../hooks/workflow';
import { Workflow } from '../../types/workflow/index';

const AwaitedActionsViewPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [currentUser] = useLocalStorage('user', null);

  const workflow = location.state?.workflowData as Workflow;

  const {
    message,
    setMessage,
    showMessageInput,
    actionType,
    handleMessageSubmit,
    handleAction,
    handleCancelAction,
  } = useWorkflowActions(workflow);

  if (!workflow) {
    return <div>No workflow data available</div>;
  }

  // Sequential workflow logic
  const currentParticipant = workflow.participants.find(
    (p) => p.userId === currentUser.id
  );

  // Check if current user is the active participant in the sequence
  const isCurrentActiveParticipant =
    workflow.currentUserId === currentUser.id &&
    workflow.creatorId !== currentUser.id &&
    !currentParticipant?.status; // Haven't acted yet

  // Check if current user is a participant (but not necessarily active)
  const isParticipant =
    !!currentParticipant && workflow.creatorId !== currentUser.id;

  // Get current participant's position in the sequence
  const currentParticipantIndex = workflow.participants.findIndex(
    (p) => p.userId === workflow.currentUserId
  );

  // Get current user's position in the sequence
  const userParticipantIndex = workflow.participants.findIndex(
    (p) => p.userId === currentUser.id
  );

  // Determine user's workflow status
  const getUserWorkflowStatus = () => {
    if (!isParticipant) return 'not-participant';
    if (currentParticipant?.status) return 'completed';
    if (isCurrentActiveParticipant) return 'active';
    if (userParticipantIndex > currentParticipantIndex) return 'waiting';
    return 'pending';
  };

  const userWorkflowStatus = getUserWorkflowStatus();

  const getStatusColor = (status: string | undefined) => {
    if (!status) return styles.default;

    switch (status.toLowerCase()) {
      case 'in-progress':
        return styles.inProgress;
      case 'pending':
        return styles.pending;
      case 'approved':
        return styles.approved;
      case 'rejected':
        return styles.rejected;
      default:
        return styles.default;
    }
  };

  const formatDateTime = (date: string | undefined) => {
    if (!date) return '-';
    return format(new Date(date), 'MMM dd, yyyy HH:mm');
  };

  // Get status message for current user
  const getStatusMessage = () => {
    switch (userWorkflowStatus) {
      case 'active':
        return {
          message: 'Your review is required',
          type: 'action-required',
          icon: '⏰',
        };
      case 'waiting':
        const currentActiveParticipant = workflow.participants.find(
          (p) => p.userId === workflow.currentUserId
        );
        return {
          message: `Waiting for ${currentActiveParticipant?.name || 'previous participant'} to review`,
          type: 'waiting',
          icon: '⏳',
        };
      case 'completed':
        return {
          message: `You have ${currentParticipant?.status} this workflow`,
          type: 'completed',
          icon:
            currentParticipant?.status === 'approved'
              ? '✅'
              : currentParticipant?.status === 'rejected'
                ? '❌'
                : '⚠️',
        };
      case 'not-participant':
        return {
          message: 'You are not a participant in this workflow',
          type: 'info',
          icon: 'ℹ️',
        };
      default:
        return {
          message: 'Workflow status unknown',
          type: 'info',
          icon: '❓',
        };
    }
  };

  const statusInfo = getStatusMessage();

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <IconButton
            type="secondary"
            icon={<IoArrowBack size={20} />}
            onClick={() => navigate(-1)}
            size="medium"
            title="Go back"
          />

          <div>
            <h1 className={styles.title}>{workflow?.title || 'Untitled'}</h1>
            <div className={styles.subtitle}>
              <IoTime className={styles.icon} />
              Created {formatDateTime(workflow?.createdOn)}
            </div>
          </div>
        </div>

        <div className={styles.headerRight}>
          {workflow.sketchbookId && (
            <CustomButton
              type="secondary"
              label="View Sketchbook"
              onClick={() =>
                navigate('/sketchbook', {
                  state: { sketchbookId: workflow.sketchbookId },
                })
              }
              leftIcon={<RiSketching />}
            />
          )}
          {workflow?.approvalStatus && (
            <div
              className={`${styles.participantStatus} ${getStatusColor(workflow.approvalStatus)}`}
            >
              {workflow.approvalStatus}
            </div>
          )}
        </div>
      </div>

      <div className={styles.section}>
        <h2 className={styles.sectionTitle}>Workflow Details</h2>
        <div className={styles.grid}>
          <div className={styles.field}>
            <div className={styles.label}>Subject</div>
            <div className={styles.value}>{workflow.subject || '-'}</div>
          </div>
          <div className={styles.field}>
            <div className={styles.label}>Priority</div>
            <div className={styles.value}>{workflow.priority}</div>
          </div>
          <div className={styles.field}>
            <div className={styles.label}>Project Name</div>
            <div className={styles.value}>{workflow.title || '-'}</div>
          </div>
          <div className={styles.field}>
            <div className={styles.label}>Due Date</div>
            <div className={styles.value}>
              <IoCalendar className={styles.icon} />
              {formatDateTime(workflow.dueDate)}
            </div>
          </div>
        </div>
      </div>

      <div className={styles.section}>
        <h2 className={styles.sectionTitle}>Approval Flow</h2>

        {/* User Status Message */}
        <div className={`${styles.statusMessage} ${styles[statusInfo.type]}`}>
          <span className={styles.statusIcon}>{statusInfo.icon}</span>
          <span className={styles.statusText}>{statusInfo.message}</span>
        </div>

        <WorkflowParticipantFlow
          participants={workflow.participants}
          currentUserId={workflow.currentUserId}
          creatorId={workflow.creatorId}
        />
        <WorkflowMessages participants={workflow.participants} />
      </div>

      {workflow.notes && workflow.notes.length > 0 && (
        <div className={styles.section}>
          <h2 className={styles.sectionTitle}>Notes</h2>
          <div className={styles.notes}>
            {workflow.notes.map((note: string, index: number) => (
              <div key={index} className={styles.note}>
                {note}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action buttons - only show for active participants */}
      {userWorkflowStatus === 'active' && (
        <>
          {showMessageInput ? (
            <div className={styles.messageInput}>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder={`Enter your ${actionType} message...`}
                className={styles.textarea}
              />
              <div className={styles.messageActions}>
                <CustomButton
                  style={{ width: '20%' }}
                  type="primary"
                  label="Submit"
                  onClick={handleMessageSubmit}
                />
                <CustomButton
                  style={{ width: '20%' }}
                  type="secondary"
                  label="Cancel"
                  onClick={handleCancelAction}
                />
              </div>
            </div>
          ) : (
            <div className={styles.actions}>
              <CustomButton
                type="primary"
                label="Approve"
                leftIcon={<IoCheckmark />}
                onClick={() => handleAction('approve')}
              />
              <CustomButton
                type="secondary"
                label="Conditional Approve"
                leftIcon={<IoWarning />}
                onClick={() => handleAction('conditional')}
              />
              <CustomButton
                type="secondary"
                label="Reject"
                leftIcon={<IoClose />}
                onClick={() => handleAction('reject')}
              />
            </div>
          )}
        </>
      )}

      {/* Information message for non-active participants */}
      {userWorkflowStatus === 'waiting' && (
        <div className={styles.waitingMessage}>
          <p>
            You will be notified when it's your turn to review this workflow.
          </p>
        </div>
      )}

      {userWorkflowStatus === 'completed' && (
        <div className={styles.completedMessage}>
          <p>You have already completed your review of this workflow.</p>
        </div>
      )}
    </div>
  );
};

export default AwaitedActionsViewPage;
