import React, { useState } from 'react';
import { <PERSON><PERSON>, Toolt<PERSON> } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import YouTubeIcon from '@mui/icons-material/YouTube';
import { useTour } from '../../../contexts/TourContext';
import VideoTourDialog from '../VideoTourDialog/VideoTourDialog';
import styles from './TourButton.module.css';

interface TourButtonProps {
  tourId: string;
  tooltip?: string;
  className?: string;
  variant?: 'text' | 'outlined' | 'contained';
  size?: 'small' | 'medium' | 'large';
  iconOnly?: boolean;
}

const TourButton: React.FC<TourButtonProps> = ({
  tourId,
  tooltip = 'Start Tour',
  className = '',
  variant = 'outlined',
  size = 'small',
  iconOnly = false,
}) => {
  const [videoTourOpen, setVideoTourOpen] = useState(false);
  const [videoData, setVideoData] = useState<{
    videoId: string;
    title: string;
    description?: string;
  } | null>(null);

  const {
    startTour,
    isActiveTour,
    availableTours,
    isVideoTour,
    getVideoTourData,
  } = useTour();

  const tourName =
    availableTours.find((tour) => tour.id === tourId)?.name || 'Tour';
  const isVideo = isVideoTour(tourId);

  const handleStartTour = () => {
    if (isVideo) {
      const data = getVideoTourData(tourId);
      if (data) {
        setVideoData(data);
        setVideoTourOpen(true);
        startTour(tourId); // Mark as seen
      }
    } else {
      startTour(tourId);
    }
  };

  const handleCloseVideoTour = () => {
    setVideoTourOpen(false);
  };

  return (
    <>
      {/* Video Tour Dialog */}
      {videoData && (
        <VideoTourDialog
          open={videoTourOpen}
          onClose={handleCloseVideoTour}
          videoId={videoData.videoId}
          title={videoData.title}
          description={videoData.description}
        />
      )}

      <Tooltip title={tooltip} arrow placement="top">
        <span className={styles.buttonWrapper}>
          {iconOnly ? (
            <Button
              className={`${styles.iconButton} ${className}`}
              onClick={handleStartTour}
              disabled={isActiveTour}
              size={size}
              color="primary"
            >
              {isVideo ? <YouTubeIcon color="error" /> : <HelpOutlineIcon />}
            </Button>
          ) : (
            <Button
              className={`${styles.button} ${className}`}
              onClick={handleStartTour}
              disabled={isActiveTour}
              variant={variant}
              size={size}
              color={isVideo ? 'error' : 'primary'}
              startIcon={isVideo ? <YouTubeIcon /> : <HelpOutlineIcon />}
            >
              {tourName}
            </Button>
          )}
        </span>
      </Tooltip>
    </>
  );
};

export default TourButton;
