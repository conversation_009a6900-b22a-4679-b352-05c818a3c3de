import { create<PERSON><PERSON> } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './baseQuery';
import {
  ADD_FLOW_EDGE_TO_SKETCHBOOK,
  ADD_FLOW_NODE_TO_SKETCHBOOK,
  ADD_PAGE_TO_SKETCHBOOK,
  DELETE_<PERSON>ETCHBOOK_TEMPLATE,
  GET_<PERSON><PERSON><PERSON>BOOK_BY_ID,
  GET_SKETCHBOOK_BY_USER_ID,
  GET_SKET<PERSON>BOOK_BY_USER_ID_PAGINATED,
  GET_SKETCHBOOK_TEMPLATE_BY_ID,
  GET_SKETCHBOOK_TEMPLATE_BY_USER_ID,
  GET_SKETCHBOOK_TEMPLATE_BY_USER_ID_PAGINATED,
  MOVE_CHART_TO_ANOTHER_PAGE,
  REMOVE_CHART_FROM_SKETCHBOOK,
  REMOVE_FLOW_EDGE_FROM_SKETCHBOOK,
  REMOVE_FLOW_NODE_FROM_SKETCHBOOK,
  REMOVE_PAGE_FROM_SKETCHBOOK,
  SAVE_CUSTOM_CHARTS,
  SAVE_FILE_FOR_EMAIL,
  SAVE_SKETCHBOOK_AS_TEMPLATE,
  SAVE_SKETCHBOOK_TEMPLATE,
  SAVE_TO_SKETCHBOOK,
  SEND_EMAIL_WITH_ATTACHMENT,
  TOGGLE_PAGE_ENABLED,
  UPDATE_CUSTOM_CHARTS,
  UPDATE_FLOW_NODE_DATA,
  UPDATE_FLOW_NODE_LABEL,
  UPDATE_FLOW_NODE_POSITION,
  UPDATE_SKETCHBOOK,
  UPDATE_SKETCHBOOK_AI_DATA,
  UPDATE_SKETCHBOOK_LAYOUT,
  UPDATE_SKETCHBOOK_NAME,
  UPDATE_SKETCHBOOK_PAGE_SIZE,
  USE_SKETCHBOOK_TEMPLATE,
} from './constants/sketchbookServicesConstants';

interface PaginationParams {
  page: number;
  size: number;
}

export const sketchboookServices = createApi({
  reducerPath: 'sketchboookServices',
  tagTypes: ['DisableCache'],
  baseQuery: baseQuery,
  endpoints: (builder) => ({
    getSketchbookByUser: builder.query({
      query: ({ userId }) => GET_SKETCHBOOK_BY_USER_ID + userId,
      providesTags: ['DisableCache'],
    }),
    getSketchbookByUserPaginated: builder.query({
      query: ({ userId, page, size }) =>
        `${GET_SKETCHBOOK_BY_USER_ID_PAGINATED}${userId}?page=${page}&size=${size}`,
      providesTags: ['DisableCache'],
    }),
    getSketchbookById: builder.query({
      query: ({ sketchbookId }) => GET_SKETCHBOOK_BY_ID + sketchbookId,
      providesTags: ['DisableCache'],
    }),
    saveToSketchbook: builder.mutation({
      query: (payload) => ({
        url: SAVE_TO_SKETCHBOOK,
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    updateSketchbook: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: UPDATE_SKETCHBOOK + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    updateSketchbookLayout: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: UPDATE_SKETCHBOOK_LAYOUT + id,
        method: 'PUT',
        body: payload,
        keepUnusedDataFor: 0,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    updateSketchbookAiData: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: UPDATE_SKETCHBOOK_AI_DATA + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    saveCustomCharts: builder.mutation({
      query: ({ payload, chartType }) => ({
        url: SAVE_CUSTOM_CHARTS + chartType,
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    updateCustomCharts: builder.mutation({
      query: ({ payload, chartType, id }) => ({
        url: UPDATE_CUSTOM_CHARTS + chartType + '/' + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    removeChartFromSketchbook: builder.mutation({
      query: ({ payload, id }) => ({
        url: REMOVE_CHART_FROM_SKETCHBOOK + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    addPageToSketchbook: builder.mutation({
      query: ({ payload, id }) => ({
        url: ADD_PAGE_TO_SKETCHBOOK + id,
        method: 'PUT',
        body: payload,
      }),
      // Disable automatic cache invalidation to prevent double page additions
      // We're handling the UI updates manually in the component
      // invalidatesTags: ['DisableCache'],
      // Add an onQueryStarted handler to log when the query starts
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        console.log('Starting addPageToSketchbook mutation:', arg);
        try {
          const result = await queryFulfilled;
          console.log(
            'addPageToSketchbook mutation completed successfully:',
            result
          );
        } catch (error) {
          console.error('addPageToSketchbook mutation failed:', error);
        }
      },
    }),
    removePageFromSketchbook: builder.mutation({
      query: ({ id, pageIndex }) => ({
        url: REMOVE_PAGE_FROM_SKETCHBOOK + id + '/' + pageIndex,
        method: 'PUT',
      }),
      invalidatesTags: ['DisableCache'],
    }),
    addFlowNodeToSketchbook: builder.mutation({
      query: ({ payload, id }) => ({
        url: ADD_FLOW_NODE_TO_SKETCHBOOK + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    addFlowEdgeToSketchbook: builder.mutation({
      query: ({ payload, id }) => ({
        url: ADD_FLOW_EDGE_TO_SKETCHBOOK + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    updateFlowNodeLabel: builder.mutation({
      query: ({ payload, id }) => ({
        url: UPDATE_FLOW_NODE_LABEL + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    updateFlowNodePosition: builder.mutation({
      query: ({ payload, id }) => ({
        url: UPDATE_FLOW_NODE_POSITION + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    updateFlowNodeData: builder.mutation({
      query: ({ payload, id }) => ({
        url: UPDATE_FLOW_NODE_DATA + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    removeFlowNodeFromSketchbook: builder.mutation({
      query: ({ id, index }) => ({
        url: REMOVE_FLOW_NODE_FROM_SKETCHBOOK + id + '/' + index,
        method: 'PUT',
      }),
      invalidatesTags: ['DisableCache'],
    }),
    removeFlowEdgeFromSketchbook: builder.mutation({
      query: ({ id, index }) => ({
        url: REMOVE_FLOW_EDGE_FROM_SKETCHBOOK + id + '/' + index,
        method: 'PUT',
      }),
      invalidatesTags: ['DisableCache'],
    }),
    togglePageEnabled: builder.mutation({
      query: ({ payload, id }) => ({
        url: TOGGLE_PAGE_ENABLED + id + '/' + payload,
        method: 'PUT',
      }),
      invalidatesTags: ['DisableCache'],
    }),
    moveToAnotherPage: builder.mutation({
      query: ({ payload, id }) => ({
        url: MOVE_CHART_TO_ANOTHER_PAGE + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    updateSketchbookPageSize: builder.mutation({
      query: ({ payload, id }) => ({
        url: UPDATE_SKETCHBOOK_PAGE_SIZE + id,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    saveFileForEmail: builder.mutation({
      query: ({ payload }) => ({
        url: SAVE_FILE_FOR_EMAIL,
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    sendEmailWithAttachment: builder.mutation({
      query: ({ payload }) => ({
        url: SEND_EMAIL_WITH_ATTACHMENT,
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    // Sketchbook Template endpoints (legacy)
    saveSketchbookTemplate: builder.mutation({
      query: (payload) => ({
        url: SAVE_SKETCHBOOK_TEMPLATE,
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    getSketchbookTemplateById: builder.query({
      query: ({ templateId }) => GET_SKETCHBOOK_TEMPLATE_BY_ID + templateId,
      providesTags: ['DisableCache'],
    }),
    getSketchbookTemplateByUser: builder.query({
      query: ({ userId }) => GET_SKETCHBOOK_TEMPLATE_BY_USER_ID + userId,
      providesTags: ['DisableCache'],
    }),
    getSketchbookTemplateByUserPaginated: builder.query({
      query: ({ userId, page, size }) =>
        `${GET_SKETCHBOOK_TEMPLATE_BY_USER_ID_PAGINATED}${userId}?page=${page}&size=${size}`,
      providesTags: ['DisableCache'],
    }),

    // New Sketchbook Template endpoints
    saveSketchbookAsTemplate: builder.mutation({
      query: (payload) => ({
        url: SAVE_SKETCHBOOK_AS_TEMPLATE,
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    useSketchbookTemplate: builder.mutation({
      query: (payload) => ({
        url: USE_SKETCHBOOK_TEMPLATE,
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['DisableCache'],
    }),
    getSketchbooksByUserWithType: builder.query({
      query: ({ userId, page, size, type }) =>
        `${GET_SKETCHBOOK_BY_USER_ID_PAGINATED}${userId}?page=${page}&size=${size}&type=${type}`,
      providesTags: ['DisableCache'],
    }),
    updateSketchbookName: builder.mutation({
      query: ({ sketchbookId, sketchbook_name }) => ({
        url: UPDATE_SKETCHBOOK_NAME + sketchbookId,
        method: 'PUT',
        body: { sketchbook_name },
      }),
      invalidatesTags: ['DisableCache'],
    }),
    deleteSketchbookTemplate: builder.mutation({
      query: (templateId) => ({
        url: DELETE_SKETCHBOOK_TEMPLATE + templateId,
        method: 'DELETE',
      }),
      invalidatesTags: ['DisableCache'],
    }),
  }),
});

export const {
  useGetSketchbookByUserQuery,
  useSaveToSketchbookMutation,
  useUpdateSketchbookMutation,
  useGetSketchbookByIdQuery,
  useSaveCustomChartsMutation,
  useUpdateCustomChartsMutation,
  useUpdateSketchbookLayoutMutation,
  useUpdateSketchbookAiDataMutation,
  useRemoveChartFromSketchbookMutation,
  useAddPageToSketchbookMutation,
  useAddFlowNodeToSketchbookMutation,
  useAddFlowEdgeToSketchbookMutation,
  useTogglePageEnabledMutation,
  useRemovePageFromSketchbookMutation,
  useMoveToAnotherPageMutation,
  useUpdateSketchbookPageSizeMutation,
  useUpdateFlowNodeLabelMutation,
  useUpdateFlowNodePositionMutation,
  useUpdateFlowNodeDataMutation,
  useRemoveFlowNodeFromSketchbookMutation,
  useRemoveFlowEdgeFromSketchbookMutation,
  useSaveFileForEmailMutation,
  useSendEmailWithAttachmentMutation,
  useGetSketchbookByUserPaginatedQuery,
  // Legacy Sketchbook Template hooks
  useSaveSketchbookTemplateMutation,
  useGetSketchbookTemplateByIdQuery,
  useGetSketchbookTemplateByUserQuery,
  useGetSketchbookTemplateByUserPaginatedQuery,
  // New Sketchbook Template hooks
  useSaveSketchbookAsTemplateMutation,
  useUseSketchbookTemplateMutation,
  useGetSketchbooksByUserWithTypeQuery,
  // New operations hooks
  useUpdateSketchbookNameMutation,
  useDeleteSketchbookTemplateMutation,
} = sketchboookServices;
