import React, { useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styles from './SketchBookController.module.css';
import { Switch } from '@mui/material';

import caretDown from '../../../assets/images/sketchController/CaretDown.png';
import caretUp from '../../../assets/images/sketchController/CaretUp.png';
import {
  FaUserTie,
  FaRegSquare,
  FaStop,
  FaPlay,
  FaCloud,
  FaFileAlt,
  FaCog,
  FaDatabase,
} from 'react-icons/fa';
import { togglePageEnabled } from '../../../store/sketchbookSlice';
import {
  FaChartBar,
  FaChartPie,
  FaChartLine,
  FaChartArea,
  FaCircleNotch,
  FaRegDotCircle,
  FaRegCircle,
  FaProjectDiagram,
  FaRegClock,
  FaBullseye,
  FaWaveSquare,
  FaBar<PERSON>,
  FaFont,
} from 'react-icons/fa';

import { FaChartGantt, FaDiamond } from 'react-icons/fa6';
import { RootState } from '../../../store/store';
import { useTogglePageEnabledMutation } from '../../../services/sketchbookServices';
import toast from 'react-hot-toast';

interface SketchBookControllerProps {
  sketchbookId: any;
}

const SketchBookController: React.FC<SketchBookControllerProps> = ({
  sketchbookId,
}) => {
  const [pageEnableApi] = useTogglePageEnabledMutation();
  const dispatch = useDispatch();
  const [expandedSection, setExpandedSection] = useState<string | null>(
    'graphs'
  );
  const pageEnabled = useSelector(
    (state: RootState) => state.sketchbook.pageEnabled ?? true
  );
  const isFlowMode = !useSelector(
    (state: RootState) => state.sketchbook.pageEnabled ?? true
  );

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, item: any) => {
    if (!item.type) {
      const dragIcon = e.currentTarget.cloneNode(true) as HTMLElement;
      dragIcon.style.border = '2px solid blue';
      dragIcon.style.backgroundColor = 'white';
      dragIcon.style.padding = '8px';
      dragIcon.style.borderRadius = '4px';

      dragIcon.style.position = 'absolute';
      dragIcon.style.top = '-1000px';
      document.body.appendChild(dragIcon);

      e.dataTransfer.setDragImage(dragIcon, 0, 0);
      setTimeout(() => document.body.removeChild(dragIcon), 0);
    }

    const data = JSON.stringify({
      type: item.type || item.id,
      defaultData: item.defaultData,
      isFlowElement: !!item.type,
      borderColor: item.type ? undefined : '#2196f3',
    });
    e.dataTransfer.setData('application/json', data);
  };

  const handleTogglePage = async () => {
    try {
      const response = await pageEnableApi({
        payload: !pageEnabled,
        id: sketchbookId,
      });

      dispatch(togglePageEnabled(!pageEnabled));
      if (pageEnabled) {
        toast.success('switching to flowchart mode');
      } else {
        toast.success('switching to page mode');
      }
    } catch (error) {
      console.error('Error toggling page enabled:', error);
    }
  };

  const graphs = [
    { id: 'bar', name: 'Bar Chart', icon: <FaChartBar /> },
    { id: 'line', name: 'Line Chart', icon: <FaChartLine /> },
    { id: 'area', name: 'Area Chart', icon: <FaChartArea /> },
    { id: 'pie', name: 'Pie Chart', icon: <FaChartPie /> },
    { id: 'doughnut', name: 'Doughnut Chart', icon: <FaRegCircle /> },
    { id: 'radar', name: 'Radar Chart', icon: <FaProjectDiagram /> },
    { id: 'polarArea', name: 'Polar Area Chart', icon: <FaBullseye /> },
    { id: 'scatter', name: 'Scatter Chart', icon: <FaRegDotCircle /> },
    { id: 'bubble', name: 'Bubble Chart', icon: <FaCircleNotch /> },
    { id: 'gauge', name: 'Gauge Chart', icon: <FaWaveSquare /> },
    { id: 'timeline', name: 'Timeline Chart', icon: <FaRegClock /> },
    { id: 'horizontal', name: 'Horizontal Bar Chart', icon: <FaBarcode /> },
    { id: 'gantt', name: 'Gantt Chart', icon: <FaChartGantt /> },
    { id: 'burndown', name: 'Burn Down Chart', icon: <FaChartLine /> },
  ];

  const textTools = [{ id: 'textarea', name: 'Text Box', icon: <FaFont /> }];

  const flowElements = [
    {
      id: 'start',
      name: 'Start',
      icon: <FaPlay />,
      type: 'start',
      defaultData: { label: 'Start' },
    },
    {
      id: 'end',
      name: 'End',
      icon: <FaStop />,
      type: 'end',
      defaultData: { label: 'End' },
    },
    {
      id: 'process',
      name: 'Process',
      icon: <FaRegSquare />,
      type: 'process',
      defaultData: { label: 'New Process' },
    },
    {
      id: 'decision',
      name: 'Decision',
      icon: <FaDiamond />,
      type: 'decision',
      defaultData: { label: 'New Decision' },
    },
    {
      id: 'organization',
      name: 'Organization',
      icon: <FaUserTie />,
      type: 'organization',
      defaultData: {
        name: 'New Employee',
        role: 'Position',
        department: 'Department',
      },
    },
    {
      id: 'database',
      name: 'Database',
      icon: <FaDatabase />,
      type: 'database',
      defaultData: { label: 'Database' },
    },
    {
      id: 'document',
      name: 'Document',
      icon: <FaFileAlt />,
      type: 'document',
      defaultData: { label: 'Document' },
    },
    {
      id: 'cloud',
      name: 'Cloud',
      icon: <FaCloud />,
      type: 'cloud',
      defaultData: { label: 'Cloud Service' },
    },
    {
      id: 'operation',
      name: 'Operation',
      icon: <FaCog />,
      type: 'operation',
      defaultData: { label: 'Operation' },
    },
  ];

  return (
    <div
      id="sketchbook-controller"
      className={styles['sketchbook-controller']}
      data-mode={isFlowMode ? 'flow' : 'default'}
    >
      <div className={styles.section}>
        <div className={styles.pageToggle}>
          <span>Flow Mode</span>
          <Switch
            checked={pageEnabled}
            onChange={handleTogglePage}
            color="primary"
            sx={{
              '& .MuiSwitch-thumb': {
                backgroundColor: 'blue',
              },
              '& .MuiSwitch-track': {
                backgroundColor: 'blue',
              },
              '& .MuiSwitch-switchBase.Mui-checked+.MuiSwitch-track': {
                backgroundColor: 'blue',
              },
            }}
          />
          <span>Chart Mode</span>
        </div>
      </div>

      {pageEnabled && (
        <>
          <div className={styles.section}>
            <p
              onClick={() => toggleSection('graphs')}
              className={styles.sectionHeader}
            >
              <span>Graphs</span>
              <img
                src={expandedSection === 'graphs' ? caretDown : caretUp}
                alt={expandedSection === 'graphs' ? 'Collapse' : 'Expand'}
                className={styles.caretIcon}
              />
            </p>
            {expandedSection === 'graphs' && (
              <div className={styles['graph-grid']}>
                {graphs.map((graph) => (
                  <div
                    key={graph.id}
                    className={styles['graph-item']}
                    draggable
                    onDragStart={(e) => handleDragStart(e, graph)}
                  >
                    {graph.icon}
                    <span>{graph.name}</span>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className={styles.section}>
            <p
              onClick={() => toggleSection('text')}
              className={styles.sectionHeader}
            >
              <span>Text Tools</span>
              <img
                src={expandedSection === 'text' ? caretDown : caretUp}
                alt={expandedSection === 'text' ? 'Collapse' : 'Expand'}
                className={styles.caretIcon}
              />
            </p>
            {expandedSection === 'text' && (
              <div className={styles['graph-grid']}>
                {textTools.map((tool: any) => (
                  <div
                    key={tool.id}
                    className={styles['graph-item']}
                    draggable
                    onDragStart={(e) => handleDragStart(e, tool)}
                  >
                    {tool.icon}
                    <span>{tool.name}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </>
      )}

      {!pageEnabled && (
        <div className={`${styles.section} ${styles.flowSection}`}>
          <p
            onClick={() => toggleSection('flow')}
            className={styles.sectionHeader}
          >
            <span>Flow Elements</span>
            <img
              src={expandedSection === 'flow' ? caretDown : caretUp}
              alt={expandedSection === 'flow' ? 'Collapse' : 'Expand'}
              className={styles.caretIcon}
            />
          </p>
          {expandedSection === 'flow' && (
            <div className={styles['graph-grid']}>
              {flowElements.map((element) => (
                <div
                  key={element.id}
                  className={styles['graph-item']}
                  draggable
                  onDragStart={(e) => handleDragStart(e, element)}
                >
                  {element.icon}
                  <span>{element.name}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SketchBookController;
