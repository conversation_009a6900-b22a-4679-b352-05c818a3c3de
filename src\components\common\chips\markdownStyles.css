.markdown {
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  width: 100%;
  max-width: 800px;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

.markdown h1,
.markdown h2,
.markdown h3 {
  color: var(--color-text-primary);
  max-width: 100%;
  transition: color 0.3s ease;
}

.markdown p {
  /* margin: 0 0 1em; */
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.markdown ul,
.markdown ol {
  padding-left: 20px;
  max-width: 100%;
}

.markdown li {
  margin-bottom: 0.5em;
  max-width: 100%;
}

.markdown blockquote {
  border-left: 4px solid var(--color-border);
  padding-left: 1em;
  color: var(--color-text-secondary);
  font-style: italic;
  max-width: 100%;
  transition:
    color 0.3s ease,
    border-color 0.3s ease;
}

.markdown .math {
  overflow-x: auto;
  padding: 1em 0;
  max-width: 100%;
  white-space: normal !important;
}

.markdown .math-display {
  overflow-x: auto;
  padding: 1rem 0;
  margin: 1rem 0;
  background-color: var(--color-background-tertiary);
  border-radius: 4px;
  max-width: 100%;
  transition: background-color 0.3s ease;
}

.markdown .math-inline {
  padding: 0.2rem 0.4rem;
  background-color: var(--color-background-secondary);
  border-radius: 3px;
  max-width: 100%;
  white-space: normal !important;
  transition: background-color 0.3s ease;
}

.katex-display {
  padding: 1rem;
  margin: 1rem 0;
  /* background-color: var(--color-background-tertiary); */
  background-color: transparent;
  border-left: 4px solid var(--color-primary);
  box-shadow: 0 1px 3px var(--color-shadow);
  max-width: 100%;
  overflow-x: auto;
  white-space: normal !important;
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

.katex-display > .katex {
  overflow-x: auto;
  overflow-y: hidden;
  max-width: 100%;
  white-space: normal !important;
}

.katex-display > .katex > .katex-html {
  color: 'var(--color-text-primary)' !important;
  padding: 0.5rem 0;
  max-width: 100%;
  white-space: normal !important;
}

.katex-display:hover {
  border-left-color: var(--color-primary-dark);
  background-color: var(--color-background-hover);
  transition: all 0.3s ease;
}

.katex,
.katex-html {
  max-width: 100%;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  white-space: normal !important;
  color: var(--color-text-primary) !important;
}

/* Ensure KaTeX works properly in table cells */
.markdown-table td .katex,
.markdown-table th .katex {
  display: inline !important;
  font-size: inherit !important;
}

.katex .mord {
  word-spacing: 0.1em !important;
  margin-right: 0.1em !important;
}

.katex .base {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

.katex .mathnormal {
  word-spacing: 0.1em !important;
  letter-spacing: 0.01em !important;
}

/* Enhanced table styling */
.table-wrapper {
  overflow-x: auto;
  margin: 1rem 0;
  border-radius: 8px;
  box-shadow: 0 1px 3px var(--color-shadow);
  transition: box-shadow 0.3s ease;
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--color-background-primary);
  transition: background-color 0.3s ease;
}

.markdown-table th,
.markdown-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  transition:
    border-color 0.3s ease,
    background-color 0.3s ease;
}

.markdown-table th {
  background-color: var(--color-background-secondary);
  font-weight: 600;
  color: var(--color-text-primary);
  border-bottom: 2px solid var(--color-border);
}

.markdown-table tr:hover {
  background-color: var(--color-background-hover);
}

.markdown-table tr:last-child td {
  border-bottom: none;
}

/* Checkbox styling */
.markdown-checkbox {
  margin-right: 8px;
  transform: scale(1.1);
  accent-color: var(--color-primary);
}

/* Enhanced link styling */
.markdown-link {
  color: var(--color-primary);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition:
    color 0.3s ease,
    border-color 0.3s ease;
}

.markdown-link:hover {
  color: var(--color-primary-dark);
  border-bottom-color: var(--color-primary);
}

.markdown-link[target='_blank']::after {
  content: ' ↗';
  font-size: 0.8em;
  opacity: 0.7;
}

/* Strikethrough text */
.markdown del {
  text-decoration: line-through;
  opacity: 0.7;
}

/* Enhanced code block styling */
.markdown pre {
  background-color: var(--color-background-tertiary);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.markdown code {
  background-color: var(--color-background-secondary);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
  transition: background-color 0.3s ease;
}

.markdown pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

/* Task list styling */
.markdown .task-list-item {
  list-style: none;
  margin-left: -1.5rem;
}

.markdown .task-list-item input {
  margin-right: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .markdown {
    font-size: 14px;
    max-width: 100%;
  }

  .table-wrapper {
    font-size: 14px;
  }

  .markdown-table th,
  .markdown-table td {
    padding: 8px 12px;
  }

  .katex-display {
    padding: 0.5rem;
    margin: 0.5rem 0;
  }
}

/* Dark theme specific adjustments */
@media (prefers-color-scheme: dark) {
  .markdown-table {
    border: 1px solid var(--color-border);
  }

  .markdown pre {
    background-color: #1e1e1e;
    border-color: #333;
  }

  .markdown code {
    background-color: #2d2d2d;
    color: #f8f8f2;
  }
}
