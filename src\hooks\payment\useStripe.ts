import { useState, useEffect, useCallback } from 'react';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { useDispatch, useSelector } from 'react-redux';
import {
  setProcessing,
  setError,
  setSuccess,
  setPaymentIntent,
  completePaymentFlow,
  failPaymentFlow,
  selectCurrentPlan,
  selectPaymentIntent,
  selectIsPaymentProcessing,
} from '../../store/slices/paymentSlice';
import { useInitiatePaymentMutation } from '../../services/paymentService';
import {
  PaymentFormData,
  PaymentError,
  PaymentRecord,
} from '../../types/payment';
import {
  mapStripeError,
  generatePaymentMetadata,
} from '../../utils/payment/stripeHelpers';
import { validatePaymentForm } from '../../utils/payment/paymentValidation';
import { getCurrentUserId } from '../../utils/auth/userHelpers';

export const useStripePayment = () => {
  const stripe = useStripe();
  const elements = useElements();
  const dispatch = useDispatch();

  const currentPlan = useSelector(selectCurrentPlan);
  const paymentIntent = useSelector(selectPaymentIntent);
  const isProcessing = useSelector(selectIsPaymentProcessing);

  const [initiatePayment] = useInitiatePaymentMutation();

  const [isReady, setIsReady] = useState(false);
  const [cardComplete, setCardComplete] = useState(false);
  const [cardError, setCardError] = useState<string | null>(null);

  // Check if Stripe is ready
  useEffect(() => {
    setIsReady(!!stripe && !!elements);
  }, [stripe, elements]);

  // Handle card element changes
  const handleCardChange = useCallback((event: any) => {
    setCardError(event.error ? event.error.message : null);
    setCardComplete(event.complete);
  }, []);

  // Create payment intent using new API
  const createIntent = useCallback(
    async (formData: PaymentFormData) => {
      if (!currentPlan) {
        throw new Error('No payment plan selected');
      }

      const currentUserId = getCurrentUserId();
      if (!currentUserId) {
        throw new Error('User not authenticated');
      }

      try {
        console.log('Initiating payment with backend...');
        const response = await initiatePayment({
          planId: currentPlan.id,
          userId: currentUserId,
          customerEmail: formData.customerEmail,
          metadata: generatePaymentMetadata(
            currentPlan,
            formData.customerEmail
          ),
        }).unwrap();

        console.log('✅ Payment initiation successful:', response);

        // Validate Stripe client secret format
        if (
          !response.clientSecret ||
          !response.clientSecret.startsWith('pi_')
        ) {
          console.error('❌ Invalid Stripe client secret format:', {
            hasClientSecret: !!response.clientSecret,
            clientSecretFormat: response.clientSecret?.substring(0, 10) + '...',
          });

          throw new Error(
            'Invalid payment response from server. Please try again or contact support.'
          );
        }

        console.log('✅ Valid Stripe payment intent created');

        dispatch(
          setPaymentIntent({
            id: response.paymentIntentId,
            clientSecret: response.clientSecret,
            amount: currentPlan.price,
            currency: currentPlan.currency,
            status: 'requires_payment_method',
          })
        );

        return response;
      } catch (error: any) {
        console.error('Payment initiation error:', error);
        const paymentError = mapStripeError(error);
        dispatch(setError(paymentError.message));
        throw paymentError;
      }
    },
    [currentPlan, initiatePayment, dispatch]
  );

  // Process payment
  const processPayment = useCallback(
    async (formData: PaymentFormData): Promise<PaymentRecord> => {
      if (!stripe || !elements || !currentPlan) {
        throw new Error('Stripe not initialized or no plan selected');
      }

      // Validate form data
      const validation = validatePaymentForm(formData);
      if (!validation.isValid) {
        const errorMessage = Object.values(validation.errors)[0];
        dispatch(setError(errorMessage));
        throw new Error(errorMessage);
      }

      dispatch(setProcessing(true));
      dispatch(setError(null));

      try {
        const cardElement = elements.getElement(CardElement);
        if (!cardElement) {
          throw new Error('Card element not found');
        }

        // Create payment intent if not exists
        let clientSecret = paymentIntent?.clientSecret;
        if (!clientSecret) {
          const intentResponse = await createIntent(formData);
          clientSecret = intentResponse.clientSecret;
        }

        // Confirm payment with Stripe
        const { error, paymentIntent: confirmedIntent } =
          await stripe.confirmCardPayment(clientSecret, {
            payment_method: {
              card: cardElement,
              billing_details: {
                name: formData.customerName,
                email: formData.customerEmail,
                address: formData.billingAddress
                  ? {
                      line1: formData.billingAddress.line1,
                      line2: formData.billingAddress.line2,
                      city: formData.billingAddress.city,
                      state: formData.billingAddress.state,
                      postal_code: formData.billingAddress.postalCode,
                      country: formData.billingAddress.country,
                    }
                  : undefined,
              },
            },
          });

        if (error) {
          const paymentError = mapStripeError(error);
          dispatch(failPaymentFlow(paymentError.message));
          throw paymentError;
        }

        if (confirmedIntent?.status === 'succeeded') {
          // Since confirmPayment API is not available, we'll create the payment record
          // based on the Stripe response. The backend webhook should handle the actual
          // payment confirmation and user credit updates.

          const currentUserId = getCurrentUserId();
          const paymentRecord: PaymentRecord = {
            id: confirmedIntent.id,
            paymentIntentId: confirmedIntent.id,
            userId: currentUserId || '',
            planId: currentPlan.id,
            amount: confirmedIntent.amount / 100, // Convert from cents
            currency: confirmedIntent.currency,
            status: 'succeeded',
            dated: new Date().toISOString(),
            createdOn: new Date().toISOString(),
            updatedOn: new Date().toISOString(),
            // Legacy fields for backward compatibility
            planName: currentPlan.name,
            createdAt: new Date().toISOString(),
            paymentMethodId: confirmedIntent.payment_method as string,
          };

          dispatch(completePaymentFlow(paymentRecord));
          return paymentRecord;
        } else {
          throw new Error('Payment was not successful');
        }
      } catch (error: any) {
        const errorMessage = error.message || 'Payment failed';
        dispatch(failPaymentFlow(errorMessage));
        throw error;
      } finally {
        dispatch(setProcessing(false));
      }
    },
    [stripe, elements, currentPlan, paymentIntent, createIntent, dispatch]
  );

  // Reset payment state
  const resetPayment = useCallback(() => {
    setCardError(null);
    setCardComplete(false);
    dispatch(setError(null));
    dispatch(setSuccess(false));
    dispatch(setProcessing(false));
  }, [dispatch]);

  return {
    // State
    isReady,
    isProcessing,
    cardComplete,
    cardError,

    // Actions
    handleCardChange,
    processPayment,
    createIntent,
    resetPayment,

    // Stripe objects
    stripe,
    elements,
  };
};

export default useStripePayment;
