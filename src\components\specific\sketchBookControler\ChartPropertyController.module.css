/* ColorController.module.css */
.colorController {
  height: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background-secondary);
  overflow-y: auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif;
  gap: 16px;
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.colorController::-webkit-scrollbar {
  width: 6px;
}

.colorController::-webkit-scrollbar-track {
  background: transparent;
}

.colorController::-webkit-scrollbar-thumb {
  background-color: var(--color-text-secondary);
  border-radius: 3px;
}

.autoLayout {
  width: 100%;
  padding: 10px 16px;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  background: linear-gradient(107.11deg, #9f7aea 12.42%, #4299e1 96.58%);
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
  text-align: center;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.autoLayout:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.icon {
  height: 24px;
  margin-right: 8px;
}

.section {
  background: var(--color-background-secondary);
  border-radius: 8px;
  border: 1px solid var(--color-border);
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    border-color 0.3s ease;
  margin-bottom: 16px;
}

.section:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.heading {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  transition: color 0.3s ease;
  border-bottom: 2px solid var(--color-primary);
  transition:
    color 0.3s ease,
    border-color 0.3s ease;
}

.subheading {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
  transition: color 0.3s ease;
}

.property {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-2);
  margin-left: var(--spacing-2);
}

.property span {
  /* width: 75px; */
  height: 16px;
}

.property input {
  margin: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  color: var(--color-text-primary);
  background-color: var(--color-background-tertiary);
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.property input:focus {
  border-color: var(--color-primary-main);
  box-shadow: 0 0 0 2px var(--color-primary-light);
  outline: none;
}

.linkSimple {
  /* width: 20px; */
  height: 20px;
  cursor: pointer;
  margin-left: 0;
  align-self: flex-end;
}

.colorItem {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  width: 100%;
  height: 40px;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-1);
  gap: var(--spacing-2);
  background: var(--color-grey-100);
  transition: background-color 0.2s ease;
}

.colorItem:hover {
  background: var(--color-grey-200);
}

.colorPreview {
  width: 24px;
  height: 24px;
  padding: 0;
  border: 1px solid var(--color-grey-300);
  border-radius: var(--radius-sm);
  cursor: pointer;
  background: none;
}

.colorPreview::-webkit-color-swatch-wrapper {
  padding: 0;
}

.colorPreview::-webkit-color-swatch {
  border: none;
  border-radius: var(--radius-sm);
}

.colorPreview::-moz-color-swatch {
  border: none;
  border-radius: var(--radius-sm);
}

.colorItem input[type='text'] {
  width: 100%; /* Increased width for better usability */
  height: 20px;
  background: transparent;
  text-transform: uppercase;
  border: none;
  border-radius: 5px;
  font-family: Lato;
  font-size: 13px;
  font-weight: 400;
  line-height: 19.5px;
  color: #131414;
  align-self: center;
  background: rgba(255, 255, 255, 0.5);
  padding: 4px 8px;
  transition: background-color 0.3s;
}

.colorItem input[type='text']:focus {
  background: rgba(255, 255, 255, 0.8);
}

.colorItem input[type='number'] {
  width: 100%; /* Increased width for better usability */
  height: 20px;
  border: none;
  background: transparent;
  border-radius: 5px;
  font-family: Lato;
  font-size: 13px;
  font-weight: 400;
  line-height: 19.5px;
  color: var(--color-text-primary);
  text-align: right;
  align-self: center;
  background: var(--color-background-tertiary);
  padding: 4px 8px;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.colorItem input[type='number']:focus {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-primary-light);
}

.textOption {
  width: 100%;
  height: 40px;
  padding: 4px 8px 4px 8px;
  border-radius: 5px;
  font-family: 'Lato', sans-serif;
  font-size: 13px;
  line-height: 19.5px;
  color: var(--color-text-secondary);
  margin-bottom: 5px;
  margin-left: 0;
  background: var(--color-background-tertiary);
  align-content: center;
  display: flex;
  align-items: center;
}

.propertyGroup {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 16px;
}

.propertyGroup h4 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
  padding-bottom: var(--spacing-1);
  border-bottom: 1px solid var(--color-grey-200);
}

.propertyRow {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background-color: var(--color-background-default);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-grey-200);
  width: 100%;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.propertyRow:hover {
  border-color: var(--color-grey-300);
  box-shadow: var(--shadow-sm);
}

.propertyItem {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  min-width: 0;
}

.propertyItem span {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  white-space: nowrap;
}

.propertyItem input {
  width: 100%;
  padding: var(--spacing-2);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  background-color: var(--color-background-tertiary);
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.propertyItem input:focus {
  border-color: var(--color-primary-main);
  box-shadow: 0 0 0 2px var(--color-primary-light);
  outline: none;
}

.propertyItem input:hover {
  border-color: var(--color-primary-light);
}

/* Specific styles for bubble chart inputs */
.propertyRow.bubble {
  background-color: var(--color-background-tertiary);
  flex-wrap: wrap;
  transition: background-color 0.3s ease;
}

.propertyRow.bubble .propertyItem {
  flex-basis: calc(33.33% - 8px);
  min-width: 80px;
}

.propertyRow.bubble .propertyItem input {
  text-align: center;
  padding: 6px 4px;
}

/* Specific styles for timeline inputs */
.propertyRow.timeline {
  background-color: #f7fafc;
  flex-wrap: wrap;
}

.propertyRow.timeline .propertyItem {
  flex-basis: calc(50% - 6px);
}

.propertyRow.timeline .propertyItem input[type='datetime-local'] {
  font-family: 'Lato', sans-serif;
  font-size: 12px;
  padding: 6px 4px;
}

/* Regular chart styles */
.propertyRow:not(.bubble):not(.timeline) .propertyItem {
  flex: 1;
}

/* Common input styles */
.propertyItem input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
  outline: none;
}

.propertyItem input:hover {
  border-color: var(--color-primary-light);
}

/* Add hover effect for all property rows */
.propertyRow:hover {
  border-color: var(--color-primary-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Add Data Point section styles */
.section button {
  /* width: 100%;
  padding: 8px;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease; */
  margin-top: 8px;
}

/* .section button:hover {
  background-color: #3182ce;
} */

/* Scatter chart specific styles */
.propertyRow.scatter {
  background-color: var(--color-background-tertiary);
  flex-wrap: wrap;
  transition: background-color 0.3s ease;
}

.propertyRow.scatter .propertyItem {
  flex-basis: calc(50% - 6px);
  min-width: 80px;
}

.propertyRow.scatter .propertyItem input {
  text-align: center;
  padding: 6px 4px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  background-color: var(--color-background-tertiary);
  border-radius: 4px;
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.sectionHeader:hover {
  background-color: var(--color-background-hover);
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
  align-items: center;
}

.pagination button {
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid var(--color-border);
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* AG Grid theme styles */
.propertyGroup .ag-theme-alpine,
.ag-theme-alpine {
  --ag-header-height: 30px;
  --ag-header-foreground-color: var(--color-text-primary);
  --ag-header-background-color: var(--color-background-tertiary);
  --ag-row-hover-color: var(--color-background-hover);
  --ag-selected-row-background-color: var(--color-primary-light);
  --ag-font-size: 12px;
  --ag-font-family: 'Lato', sans-serif;
  --ag-background-color: var(--color-background-secondary);
  --ag-foreground-color: var(--color-text-primary);
  --ag-border-color: var(--color-border);
  --ag-row-border-color: var(--color-border);
  --ag-cell-horizontal-border: var(--color-border);
  width: 100%;
  height: 400px;
}

/* Dark theme for AG Grid */
.ag-theme-alpine-dark {
  --ag-background-color: var(--color-background-secondary);
  --ag-foreground-color: var(--color-text-primary);
  --ag-border-color: var(--color-border);
  --ag-row-border-color: var(--color-border);
  --ag-header-background-color: var(--color-background-tertiary);
  --ag-odd-row-background-color: rgba(255, 255, 255, 0.05);
  --ag-header-foreground-color: var(--color-text-primary);
  --ag-disabled-foreground-color: var(--color-text-disabled);
  --ag-input-disabled-background-color: var(--color-background-tertiary);
  --ag-input-disabled-border-color: var(--color-border);
}

.propertyGroup .ag-theme-alpine .ag-header-cell {
  font-weight: 600;
}

.propertyGroup .ag-theme-alpine .ag-cell-focus {
  border-color: var(--color-primary);
}

.propertyGroup .ag-theme-alpine .ag-cell-inline-editing {
  padding: 0;
}

.propertyGroup .ag-theme-alpine .ag-cell-inline-editing input {
  height: 100%;
  width: 100%;
  padding: 0 8px;
}

.addDataButton {
  width: 100%;
  padding: 10px 16px;
  margin-top: 16px;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.addDataButton:hover {
  background-color: #3182ce;
}

.addDataButton:active {
  transform: translateY(1px);
}

/* Adjust specific column widths */
.section .ag-theme-alpine .ag-header-cell[col-id='index'] {
  min-width: 70px !important;
  max-width: 70px !important;
}

.section .ag-theme-alpine .ag-header-cell[col-id='label'] {
  min-width: 120px !important;
}

.section .ag-theme-alpine .ag-header-cell[col-id='value'],
.section .ag-theme-alpine .ag-header-cell[col-id='x'],
.section .ag-theme-alpine .ag-header-cell[col-id='y'],
.section .ag-theme-alpine .ag-header-cell[col-id='r'] {
  min-width: 100px !important;
}

.section .ag-theme-alpine .ag-header-cell[col-id='backgroundColor'],
.section .ag-theme-alpine .ag-header-cell[col-id='borderColor'] {
  min-width: 140px !important;
}

/* Add these new styles */
.colorPickerPopup {
  position: absolute;
  z-index: 1000;
  background: var(--color-background-modal);
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: background-color 0.3s ease;
}

.colorPreview {
  width: 24px;
  height: 24px;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.colorPreview:hover {
  transform: scale(1.1);
}

/* Update the existing styles */
.section .ag-theme-alpine .ag-header-cell[col-id='backgroundColor'],
.section .ag-theme-alpine .ag-header-cell[col-id='borderColor'] {
  min-width: 140px !important;
}

.section .ag-theme-alpine .ag-cell {
  display: flex;
  align-items: center;
  padding: 4px 8px;
}

/* Ensure color picker popup stays above other elements */
:global(.react-colorful) {
  width: 200px !important;
  height: 200px !important;
}

:global(.react-colorful__saturation) {
  border-radius: 4px 4px 0 0;
}

:global(.react-colorful__hue) {
  height: 20px !important;
  margin-top: 8px;
  border-radius: 0 0 4px 4px;
}

:global(.react-colorful__pointer) {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.datePicker {
  width: 100%;
  height: 100%;
  border: none;
  padding: 4px 8px;
  font-size: 14px;
  background-color: transparent;
  color: var(--color-text-primary);
}

.datePickerPopper {
  z-index: 9999 !important;
}

/* Ensure the date picker dropdown appears above the grid */
:global(.react-datepicker-popper) {
  z-index: 9999 !important;
}

:global(.react-datepicker) {
  font-family: 'Lato', sans-serif;
  border-color: var(--color-border);
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
}

:global(.react-datepicker__header) {
  background-color: var(--color-background-tertiary);
  border-color: var(--color-border);
}

:global(.react-datepicker__current-month),
:global(.react-datepicker__day-name),
:global(.react-datepicker__day) {
  color: var(--color-text-primary);
}

:global(.react-datepicker__day:hover) {
  background-color: var(--color-primary-light);
}

:global(.react-datepicker__day--selected) {
  background-color: var(--color-primary);
  color: white;
}

.datePickerWrapper {
  position: absolute;
  z-index: 9999 !important;
  background-color: var(--color-background-secondary);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Style the date picker input */
.react-datepicker__input-container input {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--color-background-tertiary);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

/* Add these new styles */
.dataSourceControls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  background-color: var(--color-background-tertiary);
  border-radius: 8px;
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
  border: 1px solid var(--color-border);
  padding: 16px;
  margin-bottom: 16px;
}

.fileInput {
  display: none;
}

.fileInputLabel {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: 8px 16px;
  background-color: #1976d2;
  color: #ffffff;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: fit-content;
}

.fileInputLabel:hover {
  background-color: #1565c0;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.dataSourceSelect {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  color: #333333;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dataSourceSelect:hover {
  border-color: #1976d2;
}

.dataSourceSelect:focus {
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
  outline: none;
}

.applyButton,
.addDataButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 120px;
}

.applyButton:hover:not(:disabled),
.addDataButton:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.applyButton:disabled {
  background-color: var(--color-background-tertiary);
  color: var(--color-text-disabled);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.buttonGroup {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
}

.addButton {
  padding: 8px 16px;
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.addButton:hover {
  background-color: var(--color-primary-dark);
}

.datePickerWrapper {
  position: absolute;
  z-index: 1000;
  background: var(--color-background-modal);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

/* Ensure the date picker appears above other elements */
.datePickerWrapper :global(.react-datepicker) {
  font-family: inherit;
  border-radius: 4px;
}

/* Grid Styles */
:global(.ag-theme-alpine) {
  --ag-font-size: var(--font-size-sm);
  --ag-font-family: var(--font-family-primary);
  --ag-grid-size: 6px;
  --ag-row-height: 48px;
  --ag-header-height: 48px;
  --ag-cell-horizontal-padding: 8px;
  --ag-borders: solid 1px;
  --ag-border-color: var(--color-border);
  --ag-header-background-color: var(--color-background-tertiary);
  --ag-odd-row-background-color: var(--color-background-secondary);
  --ag-row-hover-color: var(--color-background-hover);
  --ag-selected-row-background-color: var(--color-primary-light);
  border-radius: var(--radius-md);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

:global(.ag-theme-alpine .ag-cell) {
  display: flex;
  align-items: center;
  line-height: 1.4;
  padding: var(--spacing-2) var(--spacing-3);
}

:global(.ag-theme-alpine .ag-header-cell) {
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-2) var(--spacing-3);
}

:global(.ag-theme-alpine .ag-row) {
  border-bottom: 1px solid var(--color-grey-100);
}

/* Button Styles */
.buttonGroup {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.addButton {
  padding: 8px 16px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.addButton:hover {
  background-color: #e0e0e0;
}

/* Section Styles */
.section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.heading {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

/* Date Picker Styles */
:global(.ag-theme-alpine .ag-date-cell) {
  display: flex;
  align-items: center;
  gap: 8px;
}

.textControls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.textInput {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  transition: border-color 0.2s;
}

.textInput:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.styleControls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.controlRow {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.styleGroup {
  flex: 1;
  min-width: 200px;
}

.styleGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.select,
.numberInput {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  font-size: 14px;
}

.colorPickerWrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.colorInput {
  width: 40px;
  height: 40px;
  padding: 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.colorValue {
  font-size: 0.9rem;
  color: #666;
}

.formatButtons {
  display: flex;
  gap: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #eee;
}

.formatButton {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s;
}

.formatButton:hover {
  background: #f8f9fa;
}

.formatButton.active {
  background: #e9ecef;
  border-color: #ced4da;
}

.fontPreview {
  margin-top: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #eee;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background: #f8f9fa;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.2s;
}

.fontPreview:hover {
  white-space: normal;
  overflow: visible;
  height: auto;
  z-index: 1;
  position: relative;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Update select styles for better font preview */
.select option {
  padding: 8px;
  font-size: 14px;
}

.settingsGrid {
  display: grid;
  gap: 12px;
}

.settingItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.settingItem label {
  font-size: 12px;
  color: var(--text-secondary);
}

.settingItem input {
  padding: 6px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 13px;
  background: var(--input-background);
  color: var(--text-primary);
}

.settingItem input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.editorContainer {
  margin: 16px 0;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.styleControls {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.controlGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.controlGroup label {
  font-size: 14px;
  color: #666;
}

.controlGroup select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.colorPreview {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #ddd;
  cursor: pointer;
}

.colorPickerPopover {
  position: absolute;
  z-index: 2;
  background: white;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.addButton {
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition:
    background-color 0.3s,
    transform 0.2s;
}

.addButton:hover {
  background-color: #357abd;
  transform: scale(1.02);
}

.addButton:active {
  transform: scale(0.98);
}
.ag-theme-alpine {
  --ag-font-size: var(--font-size-sm);
  --ag-cell-horizontal-padding: 12px;
  --ag-row-hover-color: var(--color-grey-100);
  --ag-header-background-color: var(--color-grey-100);
  --ag-selected-row-background-color: var(--color-primary-light);
}

.ag-theme-alpine .ag-header-cell {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  background: var(--color-grey-100);
  border-bottom: 1px solid var(--color-grey-200);
}

.ag-theme-alpine .ag-cell {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

.ag-theme-alpine .ag-row {
  transition: background-color 0.2s;
}

.ag-theme-alpine .ag-row:hover {
  background-color: var(--color-grey-50);
}

.progressBarContainer {
  position: relative;
  width: 100%;
  height: 20px;
  background-color: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progressBarFill {
  height: 100%;
  background-color: #4caf50;
  transition: width 0.3s ease;
}

.progressBarLabel {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
  color: #ffffff;
}
input,
select {
  width: 100%;
  padding: 8px;
  border: 1px solid #d1dfec;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

input:focus,
select:focus {
  border-color: #4299e1;
  outline: none;
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2);
}

.datePickerWrapper {
  position: relative;
  z-index: 1000;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.react-datepicker {
  border-radius: 4px;
  font-family: inherit;
}

.react-datepicker__input-container input {
  width: 100%;
  padding: 8px;
  font-size: 14px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.buttonGroup {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.buttonGroup button {
  flex: 1;
}

.tasksManagementHeading {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.modernControlsContainer {
  display: flex;
  flex-direction: column;
  align-self: center;
  gap: 1.5rem;
  max-width: 100%;
  margin: auto;
}

.controlCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  height: fit-content;
  transition:
    box-shadow 0.3s ease,
    transform 0.2s ease;
}

.controlCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.cardTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #ecf0f1;
}

.modernLabel {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: #606060;
  margin-bottom: 0.5rem;
}

.modernInput {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.modernInput:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.modernInput:hover {
  border-color: #bbb;
}

.modernSlider {
  width: 100%;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  appearance: none;
  margin: 0.5rem 0;
}

.modernSlider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  background: #3498db;
  border-radius: 50%;
  cursor: pointer;
  transition:
    transform 0.1s,
    background-color 0.2s;
}

.modernSlider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  background: #2980b9;
}

.modernSlider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #3498db;
  border-radius: 50%;
  cursor: pointer;
  transition:
    transform 0.1s,
    background-color 0.2s;
  border: none;
}

.modernSlider::-moz-range-thumb:hover {
  transform: scale(1.1);
  background: #2980b9;
}

.colorGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  width: 100%;
}

@media (min-width: 768px) {
  .colorGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.colorControl {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
  width: 100%;
}

.modernColorPicker {
  width: 100%;
  height: 40px;
  padding: 0;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.modernColorPicker:hover {
  transform: scale(1.05);
}

.modernCheckboxLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  user-select: none;
}

.modernCheckbox {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 2px solid #3498db;
  appearance: none;
  cursor: pointer;
  position: relative;
  transition:
    background-color 0.2s,
    border-color 0.2s;
}

.modernCheckbox:checked {
  background-color: #3498db;
}

.modernCheckbox:checked::after {
  content: '✓';
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
}

.modernCheckbox:hover {
  border-color: #2980b9;
}

.controlGroup {
  margin-bottom: 1.5rem;
}

.controlGroup:last-child {
  margin-bottom: 0;
}

/* New styles for the optimized gauge controller */
.labelWithTooltip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.infoIcon {
  color: #3498db;
  font-size: 0.9rem;
  cursor: help;
  transition: color 0.2s ease;
}

.infoIcon:hover {
  color: #2980b9;
}

.sliderWithValue {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.sliderValue {
  min-width: 50px;
  text-align: right;
  font-size: 0.9rem;
  color: #606060;
  font-weight: 500;
}

/* Tab styles */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border);
  margin-bottom: 16px;
  background-color: var(--color-background-secondary);
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.tab {
  padding: 12px 24px;
  background-color: var(--color-background-secondary);
  color: var(--color-text-secondary);
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  flex: 1;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.tab:hover {
  background-color: var(--color-background-hover);
  color: var(--color-text-primary);
}

.activeTab {
  background-color: var(--color-primary-light);
  color: var(--color-primary-contrast);
  font-weight: 600;
}

.activeTab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--color-primary);
}

/* Add responsive breakpoints */
@media (max-width: 768px) {
  .modernControlsContainer {
    grid-template-columns: 1fr;
  }

  .colorGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .controlCard:nth-child(3) {
    grid-column: auto;
  }
}

.savingIndicator {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 0.8em;
  color: #666;
  margin-left: 10px;
}

.errorMessage {
  color: #ff4444;
  background-color: #ffe5e5;
  padding: 10px;
  margin: 10px 0;
  border-radius: 4px;
  font-size: 0.9em;
}

.buttonContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 20px;
  padding: 16px;
  background: linear-gradient(
    to right,
    rgba(66, 153, 225, 0.05),
    rgba(99, 179, 237, 0.1)
  );
  border-radius: 12px;
  border: 1px solid rgba(66, 153, 225, 0.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

/* Dependency Management Modal Styles */
.modalContent {
  padding: 16px;
  max-height: 70vh;
  overflow-y: auto;
}

.dependencyList {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 16px 0;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-background-tertiary);
}

.dependencyItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: var(--color-background-secondary);
  transition: background-color 0.2s;
}

.dependencyItem:hover {
  background-color: var(--color-background-hover);
}

.dependencyItem input[type='checkbox'] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.dependencyItem label {
  cursor: pointer;
  flex: 1;
  font-size: 14px;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border);
}

.primaryButton {
  padding: 8px 16px;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primaryButton:hover {
  background-color: var(--color-primary-dark);
}

.secondaryButton {
  padding: 8px 16px;
  background-color: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.secondaryButton:hover {
  background-color: var(--color-background-hover);
  border-color: var(--color-text-secondary);
}

.exportButtons {
  display: flex;
  gap: 12px;
  margin-left: auto;
  flex-wrap: wrap;
}

.buttonContainer :global(.custom-button) {
  position: relative;
  min-width: 40px;
  height: 40px;
  padding: 0 12px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.buttonContainer :global(.custom-button svg) {
  width: 18px;
  height: 18px;
}

.buttonContainer :global(.custom-button-primary) {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.25);
}

.buttonContainer :global(.custom-button-secondary) {
  background: white;
  color: #4299e1;
  border: 2px solid #4299e1;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.1);
}

.buttonContainer :global(.custom-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(66, 153, 225, 0.35);
}

.buttonContainer :global(.custom-button:active) {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(66, 153, 225, 0.2);
}

.buttonContainer {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  margin-bottom: 16px;
}

.saveButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(66, 153, 225, 0.25);
}

.saveButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(66, 153, 225, 0.35);
}

.saveButton:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(66, 153, 225, 0.2);
}

.formatContainer {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1100; /* Higher z-index to ensure dropdown appears above other elements */
}

.formatSelector {
  min-width: 150px;
  margin-right: 16px;
  background-color: var(--color-background-secondary);
  border-radius: 4px;
  color: var(--color-text-primary);
}

/* Dark theme support for the dropdown */
.formatSelector :global(.MuiOutlinedInput-root) {
  color: var(--color-text-primary);
  background-color: var(--color-background-secondary);
  border-color: var(--color-border);
}

.formatSelector :global(.MuiOutlinedInput-notchedOutline) {
  border-color: var(--color-border);
}

.formatSelector :global(.MuiInputLabel-root) {
  color: var(--color-text-secondary);
}

.formatSelector :global(.MuiSelect-icon) {
  color: var(--color-text-secondary);
}

/* Style for the dropdown menu */
:global(.MuiMenu-paper) {
  background-color: var(--color-background-secondary) !important;
  color: var(--color-text-primary) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  z-index: 9999 !important; /* Extremely high z-index to ensure it's above everything */
}

:global(.MuiMenuItem-root) {
  color: var(--color-text-primary) !important;
}

:global(.MuiMenuItem-root:hover) {
  background-color: var(--color-background-hover) !important;
}

/* High z-index popover class */
:global(.high-z-index-popover) {
  z-index: 9999 !important;
}

/* Ensure the dropdown is above everything else */
:global(.MuiPopover-root) {
  z-index: 9999 !important;
}

/* Timeline Controls specific styles */
.timelineControls {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0;
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

/* Gantt Chart specific styles */
.optionsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.optionGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
}

.optionGroup label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.selectInput,
.numberInput {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-background-tertiary);
  color: var(--color-text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
}

.selectInput:focus,
.numberInput:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(75, 85, 99, 0.1);
}

.selectInput:hover,
.numberInput:hover {
  border-color: var(--color-primary-light);
}

@media (max-width: 768px) {
  .buttonContainer {
    flex-direction: column;
    gap: 12px;
  }

  .exportButtons {
    margin-left: 0;
    width: 100%;
  }

  .buttonContainer :global(.custom-button) {
    width: 100%;
  }
}
