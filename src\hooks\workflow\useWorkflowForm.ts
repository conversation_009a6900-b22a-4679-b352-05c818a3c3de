import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSaveWorkflowMutation } from '../../services/workflowServices';
import {
  WorkflowFormData,
  WorkflowPerson,
  WorkflowPayload,
} from '../../types/workflow/index';
import toast from 'react-hot-toast';
import { useNotifications } from '../useNotifications';

interface UseWorkflowFormProps {
  currentUser: any;
  projectTitle?: string;
  sketchbookId?: string;
  projectId?: string;
}

export const useWorkflowForm = ({
  currentUser,
  projectTitle,
  sketchbookId,
  projectId,
}: UseWorkflowFormProps) => {
  const navigate = useNavigate();
  const [saveWorkflow] = useSaveWorkflowMutation();
  const { createWorkflowCreatedNotification, createAssignmentNotification } =
    useNotifications(true); // Enable API notifications

  const [formData, setFormData] = useState<WorkflowFormData>({
    creator: currentUser.name,
    date: '',
    projectName: projectTitle || 'untitled',
    priority: '',
    dueDate: '',
    subject: '',
    requiredActions: '',
  });

  const [notes, setNotes] = useState<string[]>([]);
  const [people, setPeople] = useState<WorkflowPerson[]>([]);
  const [validationErrors, setValidationErrors] = useState<Set<string>>(
    new Set()
  );

  useEffect(() => {
    // Set default dates
    const today = new Date().toISOString().split('T')[0];
    const dueDateDefault = new Date();
    dueDateDefault.setDate(dueDateDefault.getDate() + 2);

    setFormData((prev) => ({
      ...prev,
      date: today,
      dueDate: dueDateDefault.toISOString().split('T')[0],
      subject: 'New Workflow Request',
    }));
  }, []);

  const handleInputChange =
    (field: keyof WorkflowFormData) =>
    (
      valueOrEvent:
        | string
        | React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
      const value =
        typeof valueOrEvent === 'string'
          ? valueOrEvent
          : valueOrEvent.target.value;

      setFormData((prev) => ({ ...prev, [field]: value }));

      // Remove field from validation errors if it now has a value
      if (value.trim() && validationErrors.has(field)) {
        const newErrors = new Set(validationErrors);
        newErrors.delete(field);
        setValidationErrors(newErrors);
      }
    };

  const handleAddNote = (note: string) => {
    if (note.trim()) {
      setNotes((prev) => [...prev, note]);
    }
  };

  const handleDeleteNote = (index: number) => {
    setNotes((prev) => prev.filter((_, i) => i !== index));
  };

  const handleAddPerson = (person: WorkflowPerson) => {
    // Check if person already exists
    if (people.some((p) => p.id === person.id)) {
      toast.error('This person is already added to the workflow');
      return;
    }

    setPeople((prev) => [...prev, person]);

    // Remove validation error for people if we now have at least one
    if (validationErrors.has('people')) {
      const newErrors = new Set(validationErrors);
      newErrors.delete('people');
      setValidationErrors(newErrors);
    }
  };

  const handleUpdatePerson = (id: string, note: string) => {
    setPeople((prev) =>
      prev.map((person) => (person.id === id ? { ...person, note } : person))
    );
  };

  const handleDeletePerson = (id: string) => {
    setPeople((prev) => prev.filter((person) => person.id !== id));
  };

  const validateForm = (): boolean => {
    const newErrors = new Set<string>();

    // Check required fields
    if (!formData.priority) newErrors.add('priority');
    if (!formData.dueDate) newErrors.add('dueDate');
    if (!formData.subject) newErrors.add('subject');
    if (!formData.requiredActions) newErrors.add('requiredActions');

    // Check if at least one person is added
    if (people.length === 0) newErrors.add('people');

    setValidationErrors(newErrors);
    return newErrors.size === 0;
  };

  const createPayload = (): WorkflowPayload => {
    const payload = {
      title: projectTitle || '',
      sketchbookId: sketchbookId || '',
      projectId: projectId || '',
      creatorId: currentUser.id,
      createdDate: new Date().toISOString().split('T')[0],
      dueDate: formData.dueDate,
      notes: notes,
      participants: people.map((person) => ({
        userId: person.id,
        note: person.note || '',
        approval_message: '',
        rejection_message: '',
        conditional_approved_message: '',
        status: '',
      })),
      priority: formData.priority,
      projectName: formData.projectName,
      requiredActions: formData.requiredActions,
      subject: formData.subject,
      status: 'pending',
      fullyApproved: false,
      currentUserId: people[0]?.id || '',
      approvalStatus: 'in-progress',
    };

    return payload;
  };

  const handleCreateWorkflow = async () => {
    if (!validateForm()) {
      toast.error(
        'Please fill in all required fields and add at least one participant'
      );
      return;
    }

    try {
      const payload = createPayload();
      const response = await saveWorkflow({ payload }).unwrap();

      // Create notifications for workflow creation and assignments
      const workflowId = response?.data?.id || response?.id || 'temp-id';
      const workflowTitle =
        payload.title || payload.projectName || 'Untitled Workflow';
      const participantIds = people.map((person) => person.id.toString());

      // Notify all participants about workflow creation
      createWorkflowCreatedNotification(
        workflowId,
        workflowTitle,
        participantIds,
        currentUser.name,
        payload.priority as 'Low' | 'Medium' | 'High'
      );

      // Create assignment notification for the first participant
      if (people.length > 0) {
        createAssignmentNotification(
          workflowId,
          workflowTitle,
          people[0].id.toString(),
          currentUser.name,
          payload.priority as 'Low' | 'Medium' | 'High'
        );
      }

      toast.success('Workflow created successfully');
      navigate('/awaiting-actions');
    } catch (error: any) {
      console.error('Error creating workflow:', error);
      toast.error(
        error.data?.message ||
          'Error creating workflow. Please check all fields.'
      );
    }
  };

  const handleSaveAsDraft = async () => {
    if (!validateForm()) {
      toast.error(
        'Please fill in all required fields and add at least one participant'
      );
      return;
    }

    try {
      const payload = createPayload();
      payload.status = 'draft';
      await saveWorkflow({ payload }).unwrap();
      toast.success('Workflow saved as draft successfully');
      navigate('/drafts');
    } catch (error) {
      console.error('Error saving workflow as draft:', error);
      toast.error('Error saving workflow as draft');
    }
  };

  return {
    formData,
    notes,
    people,
    validationErrors,
    handleInputChange,
    handleAddNote,
    handleDeleteNote,
    handleAddPerson,
    handleUpdatePerson,
    handleDeletePerson,
    handleCreateWorkflow,
    handleSaveAsDraft,
  };
};

export default useWorkflowForm;
