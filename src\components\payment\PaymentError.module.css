.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  background: var(--color-background-light);
}

.errorCard {
  max-width: 600px;
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--color-error-border);
  background: var(--color-background);
}

.content {
  padding: 3rem 2rem;
  text-align: center;
}

.iconContainer {
  margin-bottom: 1.5rem;
}

.errorIcon {
  font-size: 4rem;
  color: var(--color-error);
  animation: errorShake 0.6s ease-out;
}

.title {
  color: var(--color-text-primary);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: var(--color-text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.errorAlert {
  margin-bottom: 1rem;
  text-align: left;
  border-radius: 8px;
}

.suggestionAlert {
  margin-bottom: 2rem;
  text-align: left;
  border-radius: 8px;
}

.divider {
  margin: 2rem 0;
  background-color: var(--color-border-light);
}

.planInfo {
  background: var(--color-background-light);
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  text-align: left;
}

.planTitle {
  color: var(--color-text-primary);
  font-weight: 600;
  margin-bottom: 1rem;
}

.planDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.planName {
  color: var(--color-text-primary);
  font-weight: 600;
}

.planPrice {
  color: var(--color-primary);
  font-weight: 600;
}

.troubleshooting {
  text-align: left;
  margin-bottom: 2rem;
}

.troubleshootingTitle {
  color: var(--color-text-primary);
  font-weight: 600;
  margin-bottom: 1rem;
}

.tipsList {
  color: var(--color-text-secondary);
  line-height: 1.8;
  padding-left: 1.5rem;
}

.tipsList li {
  margin-bottom: 0.5rem;
}

.actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.primaryButton {
  background: var(--color-primary);
  color: white;
  padding: 0.75rem 2rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.primaryButton:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
}

.secondaryButton {
  border-color: var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.75rem 2rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.secondaryButton:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: rgba(var(--color-primary-rgb), 0.05);
}

.support {
  border-top: 1px solid var(--color-border-light);
  padding-top: 1.5rem;
  margin-top: 1.5rem;
}

.supportText {
  color: var(--color-text-secondary);
  margin-bottom: 0.5rem;
}

.supportButton {
  color: var(--color-primary);
  font-weight: 600;
  text-transform: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.supportButton:hover {
  background: rgba(var(--color-primary-rgb), 0.1);
}

.additionalInfo {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  margin-top: 1rem;
  padding: 1rem;
  background: var(--color-info-light);
  border-radius: 8px;
  border: 1px solid var(--color-info-border);
}

/* Animations */
@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

.errorCard {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode support */
[data-theme="dark"] .container {
  background: var(--color-background-dark);
}

[data-theme="dark"] .errorCard {
  background: var(--color-background-dark);
  border-color: var(--color-border-dark);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .planInfo {
  background: rgba(var(--color-primary-rgb), 0.05);
}

[data-theme="dark"] .divider {
  background-color: var(--color-border-dark);
}

[data-theme="dark"] .support {
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .additionalInfo {
  background: rgba(var(--color-info-rgb), 0.1);
  border-color: rgba(var(--color-info-rgb), 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .content {
    padding: 2rem 1.5rem;
  }

  .actions {
    flex-direction: column;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
  }

  .title {
    font-size: 1.75rem;
  }

  .planInfo {
    padding: 1rem;
  }

  .planDetails {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }

  .content {
    padding: 1.5rem 1rem;
  }

  .errorIcon {
    font-size: 3rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .errorAlert,
  .suggestionAlert {
    font-size: 0.875rem;
  }
}

/* Focus states for accessibility */
.primaryButton:focus,
.secondaryButton:focus,
.supportButton:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Loading state for retry button */
.primaryButton:disabled {
  background: var(--color-disabled);
  color: var(--color-text-disabled);
  cursor: not-allowed;
}

.primaryButton:disabled:hover {
  transform: none;
  box-shadow: none;
}
