.card {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--color-border);
  border-radius: 16px;
  width: 280px;
  height: 260px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: var(--color-background-secondary);
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.imageContainer {
  width: 100%;
  height: 160px;
  background: var(--color-background-secondary);
  box-sizing: border-box;
  border-bottom: 1px solid var(--color-border);
  border-radius: 8px;
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.title {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
  padding: 15px;
  color: var(--color-text-primary);
  text-align: left;
  transition: color 0.3s ease;
}
