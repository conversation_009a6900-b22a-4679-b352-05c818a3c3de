import { ColDef } from 'ag-grid-community';
import styles from '../ChartPropertyController.module.css';
import { AgGridReact } from 'ag-grid-react';
import 'react-datepicker/dist/react-datepicker.css';
import { ViewMode } from 'gantt-task-react';
import { toast } from 'react-hot-toast';
import { useUpdateCustomChartsMutation } from '../../../../services/sketchbookServices';
import { chartPayloadHandler } from '../utils/chartPayloadHandler';
import IconButton from '../../../common/button/IconButton';
import { FaFolder, FaPlus, FaFlag, FaTrash, FaLink } from 'react-icons/fa';
import { useTheme } from '../../../../contexts/ThemeContext';
import { useState, useRef, useCallback, useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  But<PERSON>,
  IconButton as MuiIconButton,
} from '@mui/material';
import { IoClose } from 'react-icons/io5';

interface GanttControlsProps {
  sketchbookId: string;
  activePage: any;
  selectedChart: any;
  onChartUpdate: (chart: any) => void;
}

export const GanttControls = ({
  sketchbookId,
  activePage,
  selectedChart,
  onChartUpdate,
}: GanttControlsProps) => {
  if (selectedChart.type !== 'gantt') return null;
  const [updateCustomCharts] = useUpdateCustomChartsMutation();
  const { isDarkMode } = useTheme();
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const gridRef = useRef<any>(null);
  const [showDependencyModal, setShowDependencyModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any>(null);

  // Memoize column definitions to prevent unnecessary re-renders
  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        width: 40,
        headerName: '',
        field: 'selection',
        checkboxSelection: true,
        headerCheckboxSelection: true,
      },
      {
        field: 'name',
        headerName: 'Task Name',
        editable: true,
        flex: 2,
      },
      {
        field: 'type',
        headerName: 'Type',
        editable: true,
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: {
          values: ['project', 'task', 'milestone'],
        },
        cellRenderer: (params: any) => {
          const typeIcons = {
            project: '📁',
            task: '📝',
            milestone: '🎯',
          };
          return `${typeIcons[params.value as keyof typeof typeIcons]} ${params.value}`;
        },
        width: 120,
      },
      {
        field: 'project',
        headerName: 'Parent',
        editable: true,
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: (params: any) => ({
          values: [
            '',
            ...selectedChart.data.tasks
              .filter(
                (t: any) => t.type === 'project' && t.id !== params.data.id
              )
              .map((t: any) => t.id),
          ],
        }),
        cellRenderer: (params: any) => {
          if (!params.value) return 'No Parent';
          const parentTask = selectedChart.data.tasks.find(
            (t: any) => t.id === params.value
          );
          return parentTask ? parentTask.name : params.value;
        },
        width: 140,
      },
      {
        field: 'start',
        headerName: 'Start Date',
        editable: true,
        cellEditor: 'agDateCellEditor',
        cellEditorParams: {
          browserDatePicker: true,
          parseValue: (value: string) => new Date(value),
        },
        valueGetter: (params: any) => {
          return params.data.start ? new Date(params.data.start) : null;
        },
        valueFormatter: (params: any) => {
          if (!params.value) return '';
          return params.value.toLocaleDateString();
        },
        valueSetter: (params: any) => {
          const newDate = params.newValue;
          if (newDate) {
            params.data.start = newDate.toISOString();
            return true;
          }
          return false;
        },
        width: 130,
      },
      {
        field: 'end',
        headerName: 'End Date',
        editable: true,
        cellEditor: 'agDateCellEditor',
        cellEditorParams: {
          browserDatePicker: true,
          parseValue: (value: string) => new Date(value),
        },
        valueGetter: (params: any) => {
          return params.data.end ? new Date(params.data.end) : null;
        },
        valueFormatter: (params: any) => {
          if (!params.value) return '';
          return params.value.toLocaleDateString();
        },
        valueSetter: (params: any) => {
          const newDate = params.newValue;
          if (newDate) {
            params.data.end = newDate.toISOString();
            return true;
          }
          return false;
        },
        width: 130,
      },
      {
        field: 'progress',
        headerName: 'Progress',
        editable: true,
        cellRenderer: (params: any) => {
          const progress = params.value || 0;
          return (
            <div style={{ width: '100%', position: 'relative' }}>
              <div
                style={{
                  width: `${progress}%`,
                  height: '20px',
                  backgroundColor: '#4CAF50',
                  borderRadius: '4px',
                }}
              />
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  color: progress > 50 ? 'white' : 'black',
                }}
              >
                {progress}%
              </span>
            </div>
          );
        },
        cellEditor: 'agNumberCellEditor',
        cellEditorParams: {
          min: 0,
          max: 100,
          step: 5,
        },
        width: 120,
      },
      {
        field: 'dependencies',
        headerName: 'Dependencies',
        editable: false,
        cellRenderer: (params: any) => {
          const deps = params.value || [];
          const depsText = deps
            .map((depId: string) => {
              const depTask = selectedChart.data.tasks.find(
                (t: any) => t.id === depId
              );
              return depTask ? depTask.name : depId;
            })
            .join(', ');

          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <div style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
                {depsText || 'No dependencies'}
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  // Make a deep copy of the task data to avoid reference issues
                  const taskData = JSON.parse(JSON.stringify(params.data));
                  setSelectedTask(taskData);
                  setShowDependencyModal(true);
                }}
                style={{
                  background: 'transparent',
                  border: 'none',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '2px',
                }}
              >
                <FaLink size={14} color={isDarkMode ? '#aaa' : '#666'} />
              </button>
            </div>
          );
        },
        width: 180,
      },
      {
        field: 'displayOrder',
        headerName: 'Order',
        editable: true,
        type: 'numericColumn',
        width: 80,
      },
    ],
    [isDarkMode]
  );

  // Add chart options controls
  const updateChartOptions = async (key: string, value: any) => {
    const updatedChart = {
      ...selectedChart,
      options: {
        ...selectedChart.options,
        [key]: value,
      },
    };
    const updatedChartPayload = chartPayloadHandler(
      updatedChart,
      'gantt',
      sketchbookId,
      activePage
    );
    const response: any = await updateCustomCharts({
      id: selectedChart.id,
      payload: updatedChartPayload,
      chartType: 'gantt',
    });
    if (response?.data?.success) {
      toast.success('Gantt chart updated successfully');
    }
    onChartUpdate(updatedChart);
  };

  const validateDates = (
    startDate: string,
    endDate: string,
    taskType: string
  ) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return 'Invalid date format';
    }

    if (end < start) {
      return 'End date cannot be before start date';
    }

    if (taskType === 'milestone' && start.getTime() !== end.getTime()) {
      return 'Milestone start and end dates must be the same';
    }

    return null;
  };

  const onCellValueChanged = async (params: any) => {
    const { data: rowData, colDef, oldValue, newValue } = params;

    // Create a copy of the current task
    let updatedTask = { ...rowData };

    // Handle date fields specifically
    if (colDef.field === 'start' || colDef.field === 'end') {
      try {
        const dateValue = new Date(newValue);
        if (isNaN(dateValue.getTime())) {
          toast.error('Invalid date format');
          params.node.setDataValue(colDef.field, oldValue);
          return;
        }

        // Format as ISO string with UTC timezone
        const formattedDate = dateValue.toISOString();

        // Update the specific date field
        updatedTask[colDef.field] = formattedDate;

        // For milestones, automatically set both start and end dates to be the same
        if (updatedTask.type === 'milestone') {
          if (colDef.field === 'start') {
            updatedTask.end = formattedDate;
            // Update the end date in the grid UI
            setTimeout(() => {
              const rowNode = gridRef.current.api.getRowNode(params.node.id);
              if (rowNode) {
                rowNode.setDataValue('end', formattedDate);
              }
            }, 0);
          } else if (colDef.field === 'end') {
            updatedTask.start = formattedDate;
            // Update the start date in the grid UI
            setTimeout(() => {
              const rowNode = gridRef.current.api.getRowNode(params.node.id);
              if (rowNode) {
                rowNode.setDataValue('start', formattedDate);
              }
            }, 0);
          }
        } else {
          // For non-milestone tasks, validate that end date is after start date
          const error = validateDates(
            colDef.field === 'start' ? formattedDate : updatedTask.start,
            colDef.field === 'end' ? formattedDate : updatedTask.end,
            updatedTask.type
          );

          if (error) {
            toast.error(error);
            params.node.setDataValue(colDef.field, oldValue);
            return;
          }
        }
      } catch (error) {
        console.error('Date parsing error:', error);
        toast.error('Invalid date format');
        params.node.setDataValue(colDef.field, oldValue);
        return;
      }
    }

    // Update the tasks array with the new task data
    const updatedTasks = selectedChart.data.tasks.map((task: any) =>
      task.id === updatedTask.id ? updatedTask : task
    );

    // Create the updated chart object
    const updatedChart = {
      ...selectedChart,
      data: {
        ...selectedChart.data,
        tasks: updatedTasks,
      },
    };

    try {
      const updatedChartPayload = chartPayloadHandler(
        updatedChart,
        'gantt',
        sketchbookId,
        activePage
      );

      const response: any = await updateCustomCharts({
        id: selectedChart.id,
        payload: updatedChartPayload,
        chartType: 'gantt',
      });

      if (response?.data?.success) {
        toast.success('Gantt chart updated successfully');
        onChartUpdate(updatedChart);
      } else {
        toast.error('Failed to update Gantt chart');
        params.node.setDataValue(colDef.field, oldValue);
      }
    } catch (error) {
      console.error('Error updating Gantt chart:', error);
      toast.error('Error updating Gantt chart');
      params.node.setDataValue(colDef.field, oldValue);
    }
  };

  // Memoize row data to prevent unnecessary re-renders
  const rowData = useMemo(() => {
    return selectedChart.data.tasks.map((task: any) => ({ ...task }));
  }, [selectedChart.data.tasks]);

  const addNewTask = async (type: 'task' | 'project' | 'milestone') => {
    const taskCount = selectedChart.data.tasks.length;
    const newTaskId = `${type}-${taskCount + 1}`;
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(
      endDate.getDate() +
        (type === 'project' ? 7 : type === 'milestone' ? 0 : 1)
    );

    const newTask = {
      id: newTaskId,
      name: `New ${type.charAt(0).toUpperCase() + type.slice(1)} ${taskCount + 1}`,
      type,
      progress: 0,
      start: startDate.toISOString(),
      end: endDate.toISOString(),
      project: '',
      displayOrder: taskCount + 1,
      dependencies: [],
      ...(type === 'project' ? { hideChildren: false } : {}),
    };

    const updatedChart = {
      ...selectedChart,
      data: {
        ...selectedChart.data,
        tasks: [...selectedChart.data.tasks, newTask],
      },
    };

    const updatedChartPayload = chartPayloadHandler(
      updatedChart,
      'gantt',
      sketchbookId,
      activePage
    );
    const response: any = await updateCustomCharts({
      id: selectedChart.id,
      payload: updatedChartPayload,
      chartType: 'gantt',
    });
    if (response?.data?.success) {
      toast.success('Gantt chart updated successfully');
    }
    onChartUpdate(updatedChart);
  };

  const deleteSelectedTasks = async () => {
    if (selectedRows.length === 0) {
      toast.error('Please select tasks to delete');
      return;
    }

    // Create a deep copy of the chart
    const updatedChart = JSON.parse(JSON.stringify(selectedChart));

    // Get the IDs of the selected tasks
    const taskIds = selectedRows.map((row) => row.id);

    // Remove the selected tasks
    updatedChart.data.tasks = updatedChart.data.tasks.filter(
      (task: any) => !taskIds.includes(task.id)
    );

    try {
      const updatedChartPayload = chartPayloadHandler(
        updatedChart,
        'gantt',
        sketchbookId,
        activePage
      );

      const response: any = await updateCustomCharts({
        id: selectedChart.id,
        payload: updatedChartPayload,
        chartType: 'gantt',
      });

      if (response?.data?.success) {
        toast.success(`${selectedRows.length} task(s) deleted successfully`);
        onChartUpdate(updatedChart);
        setSelectedRows([]);
      } else {
        toast.error('Failed to delete tasks');
      }
    } catch (error) {
      console.error('Error deleting tasks:', error);
      toast.error('Failed to delete tasks');
    }
  };

  // Simple selection change handler
  const onSelectionChanged = () => {
    if (gridRef.current && gridRef.current.api) {
      const selectedNodes = gridRef.current.api.getSelectedNodes();
      const selectedData = selectedNodes.map((node: any) => node.data);
      setSelectedRows(selectedData);
    }
  };

  return (
    <div className={styles.section}>
      <div className={styles.heading}>Gantt Chart Configuration</div>

      {/* Chart Options */}
      <div className={styles.optionsContainer}>
        <div className={styles.optionGroup}>
          <label>View Mode:</label>
          <select
            value={selectedChart.options.viewMode}
            onChange={(e) => updateChartOptions('viewMode', e.target.value)}
            className={styles.selectInput}
          >
            {Object.values(ViewMode).map((mode) => (
              <option key={mode} value={mode}>
                {mode}
              </option>
            ))}
          </select>
        </div>

        <div className={styles.optionGroup}>
          <label>Bar Height:</label>
          <input
            type="number"
            value={selectedChart.options.barHeight}
            onChange={(e) =>
              updateChartOptions('barHeight', Number(e.target.value))
            }
            min={20}
            max={100}
            className={styles.numberInput}
          />
        </div>

        <div className={styles.optionGroup}>
          <label>Column Width:</label>
          <input
            type="number"
            value={selectedChart.options.columnWidth}
            onChange={(e) =>
              updateChartOptions('columnWidth', Number(e.target.value))
            }
            min={30}
            max={200}
            className={styles.numberInput}
          />
        </div>
      </div>

      {/* Task Management */}
      <div className={styles.heading}>Tasks Management</div>
      <div className={styles.buttonContainer}>
        <IconButton
          icon={<FaFolder />}
          onClick={() => addNewTask('project')}
          type="primary"
          title="Add Project"
          tooltipPosition="top"
        />
        <IconButton
          icon={<FaPlus />}
          onClick={() => addNewTask('task')}
          type="primary"
          title="Add Task"
          tooltipPosition="top"
        />
        <IconButton
          icon={<FaFlag />}
          onClick={() => addNewTask('milestone')}
          type="primary"
          title="Add Milestone"
          tooltipPosition="top"
        />
        <IconButton
          icon={<FaTrash />}
          onClick={deleteSelectedTasks}
          type="secondary"
          title="Delete Selected Tasks"
          tooltipPosition="top"
          disabled={selectedRows.length === 0}
        />
      </div>

      {/* Tasks Grid */}
      <div
        className={`ag-theme-alpine ${isDarkMode ? 'ag-theme-alpine-dark' : ''}`}
        style={
          {
            height: '500px',
            width: '100%',
            '--ag-cell-horizontal-padding': '8px',
          } as any
        }
      >
        <AgGridReact
          ref={gridRef}
          columnDefs={columnDefs}
          rowData={rowData}
          onCellValueChanged={onCellValueChanged}
          onSelectionChanged={onSelectionChanged}
          rowSelection="multiple"
          getRowId={(params) => params.data.id}
          defaultColDef={useMemo(
            () => ({
              sortable: true,
              filter: true,
              resizable: true,
              flex: 1,
              minWidth: 100,
              autoHeight: true,
              wrapText: true,
            }),
            []
          )}
          domLayout="normal"
          rowHeight={48}
          suppressPropertyNamesCheck={true}
          ensureDomOrder={false}
        />
      </div>

      {/* Dependency Management Modal */}
      <Dialog
        open={showDependencyModal && !!selectedTask}
        onClose={() => setShowDependencyModal(false)}
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: '8px',
            width: '600px',
            maxWidth: '90vw',
          },
        }}
      >
        {selectedTask && (
          <>
            <DialogTitle
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderBottom: '1px solid var(--color-border, #eee)',
                padding: '16px 24px',
              }}
            >
              <span>Manage Dependencies for {selectedTask.name}</span>
              <MuiIconButton
                onClick={() => setShowDependencyModal(false)}
                size="small"
                sx={{ padding: '4px' }}
              >
                <IoClose />
              </MuiIconButton>
            </DialogTitle>
            <DialogContent sx={{ padding: '20px 24px' }}>
              <p>Select tasks that this task depends on:</p>
              <div className={styles.dependencyList}>
                {selectedChart.data.tasks
                  .filter((task: any) => task.id !== selectedTask.id)
                  .map((task: any) => (
                    <div key={task.id} className={styles.dependencyItem}>
                      <input
                        type="checkbox"
                        id={`dep-${task.id}`}
                        checked={(selectedTask.dependencies || []).includes(
                          task.id
                        )}
                        onChange={(e) => {
                          const isChecked = e.target.checked;
                          const updatedDependencies = isChecked
                            ? [...(selectedTask.dependencies || []), task.id]
                            : (selectedTask.dependencies || []).filter(
                                (id: string) => id !== task.id
                              );

                          // Update the selectedTask state directly
                          setSelectedTask({
                            ...selectedTask,
                            dependencies: updatedDependencies,
                          });

                          // Also update the task in the grid if available
                          if (gridRef.current && gridRef.current.api) {
                            try {
                              const rowNode = gridRef.current.api.getRowNode(
                                selectedTask.id
                              );
                              if (rowNode) {
                                rowNode.setDataValue(
                                  'dependencies',
                                  updatedDependencies
                                );
                              }
                            } catch (err) {
                              console.log(
                                'Grid node not found, using state update only'
                              );
                            }
                          }
                        }}
                      />
                      <label htmlFor={`dep-${task.id}`}>
                        {task.name} ({task.type})
                      </label>
                    </div>
                  ))}
              </div>
            </DialogContent>
            <DialogActions
              sx={{
                padding: '16px 24px',
                borderTop: '1px solid var(--color-border, #eee)',
              }}
            >
              <Button
                variant="outlined"
                onClick={() => setShowDependencyModal(false)}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={async () => {
                  // Use the selectedTask state which has the updated dependencies
                  // This ensures we have the latest dependencies even if the grid node can't be found
                  const updatedTask = selectedTask;

                  // Update the tasks array with the new dependencies
                  const updatedTasks = selectedChart.data.tasks.map(
                    (task: any) =>
                      task.id === updatedTask.id ? updatedTask : task
                  );

                  // Create the updated chart object
                  const updatedChart = {
                    ...selectedChart,
                    data: {
                      ...selectedChart.data,
                      tasks: updatedTasks,
                    },
                  };

                  try {
                    const updatedChartPayload = chartPayloadHandler(
                      updatedChart,
                      'gantt',
                      sketchbookId,
                      activePage
                    );

                    const response: any = await updateCustomCharts({
                      id: selectedChart.id,
                      payload: updatedChartPayload,
                      chartType: 'gantt',
                    });

                    if (response?.data?.success) {
                      toast.success('Dependencies updated successfully');
                      onChartUpdate(updatedChart);
                      setShowDependencyModal(false);
                    } else {
                      toast.error('Failed to update dependencies');
                    }
                  } catch (error) {
                    console.error('Error updating dependencies:', error);
                    toast.error('Error updating dependencies');
                  }
                }}
              >
                Save Dependencies
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </div>
  );
};
