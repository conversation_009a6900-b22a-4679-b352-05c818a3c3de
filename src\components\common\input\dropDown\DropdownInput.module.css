.inputContainer {
  height: fit-content;
  display: flex;
  flex-direction: column;
  gap: 4px;
  border: 1px;
}
.inputLabel {
  font-family: lato;
  font-size: 16px;
  font-weight: 700;
  color:#0E2F51;
  ;
}
.input {
  color: #0E2F51;
  font-family: lato;
  font-size: 16px;
  font-weight: 400;
  border: 1px solid #6c757d;
  border-radius: 4px;
  padding: 10px;
}

.input:focus {
  border: 1px solid;
}

.error {
  color: #d8000c;
  font-family: lato;
  font-size: 14px;
  font-weight: 400;
}
