import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { BaseQueryFn } from '@reduxjs/toolkit/query';
import { toast } from 'react-hot-toast';
import { getNetworkStatus } from './networkUtils';
import { queueOfflineRequest } from './offlineRequestHandler';
import { addToStore, getFromStore, OFFLINE_STORES } from './offlineStorage';

// Function to get the token
const getToken = (): string | undefined => {
  const token = localStorage.getItem('token');
  return token ? token.replace(/"/g, '') : undefined;
};

/**
 * Create an enhanced base query that handles offline mode
 * 
 * @param baseUrl Base URL for the API
 * @param options Additional options
 * @returns Enhanced base query function
 */
export const createOfflineBaseQuery = (
  baseUrl: string,
  options: {
    offlineEnabled?: boolean;
    cacheTTL?: number;
    cacheByDefault?: boolean;
  } = {}
): BaseQueryFn => {
  const {
    offlineEnabled = true,
    cacheTTL = 1000 * 60 * 60, // 1 hour by default
    cacheByDefault = true,
  } = options;

  // Create the base query
  const rawBaseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders: (headers) => {
      if (getToken()) {
        headers.set('Authorization', `Bearer ${getToken()}`);
      }
      return headers;
    },
  });

  // Return the enhanced query function
  return async (args: any, api: any, extraOptions: any) => {
    const { online } = getNetworkStatus();
    const isOfflineModeEnabled = localStorage.getItem('offlineMode') === 'true';
    
    // Generate a cache key based on the request
    const cacheKey = `${baseUrl}${typeof args === 'string' ? args : args.url}${
      args.params ? JSON.stringify(args.params) : ''
    }`;
    
    // Check if we should use offline mode
    const useOfflineMode = !online || isOfflineModeEnabled;
    
    // If we're offline and this is a mutation, queue it for later
    if (useOfflineMode && offlineEnabled && args.method && args.method !== 'GET') {
      try {
        await queueOfflineRequest({
          url: `${baseUrl}${typeof args === 'string' ? args : args.url}`,
          method: args.method || 'GET',
          body: args.body,
          headers: {
            'Content-Type': 'application/json',
            Authorization: getToken() ? `Bearer ${getToken()}` : '',
          },
        });
        
        return {
          error: {
            status: 'OFFLINE',
            data: { message: 'You are offline. Your request has been queued for later.' },
          },
        };
      } catch (error) {
        return {
          error: {
            status: 'OFFLINE_ERROR',
            data: { message: 'Failed to queue request for offline use.' },
          },
        };
      }
    }
    
    // If we're offline and this is a GET request, try to get from cache
    if (useOfflineMode && offlineEnabled && (!args.method || args.method === 'GET')) {
      try {
        const cachedData = await getFromStore(OFFLINE_STORES.CACHED_DATA, cacheKey);
        
        if (cachedData && cachedData.expiry > Date.now()) {
          return { data: cachedData.data };
        } else {
          return {
            error: {
              status: 'OFFLINE_CACHE_MISS',
              data: { message: 'You are offline and this data is not available offline.' },
            },
          };
        }
      } catch (error) {
        return {
          error: {
            status: 'OFFLINE_ERROR',
            data: { message: 'Failed to retrieve cached data.' },
          },
        };
      }
    }
    
    // If we're online, make the request normally
    const result = await rawBaseQuery(args, api, extraOptions);
    
    // If this is a successful GET request and caching is enabled, cache the result
    if (
      offlineEnabled &&
      cacheByDefault &&
      (!args.method || args.method === 'GET') &&
      !result.error &&
      result.data
    ) {
      try {
        await addToStore(OFFLINE_STORES.CACHED_DATA, {
          key: cacheKey,
          data: result.data,
          timestamp: Date.now(),
          expiry: Date.now() + cacheTTL,
        });
      } catch (error) {
        console.error('Failed to cache response:', error);
      }
    }
    
    return result;
  };
};

/**
 * Create an offline-enabled base query with default settings
 */
export const createDefaultOfflineBaseQuery = (baseUrl: string) => 
  createOfflineBaseQuery(baseUrl, {
    offlineEnabled: true,
    cacheTTL: 1000 * 60 * 60 * 24, // 24 hours
    cacheByDefault: true,
  });
