/**
 * Utility for offline data storage using IndexedDB
 */

// Database configuration
const DB_NAME = 'neuquip_offline_db';
const DB_VERSION = 1;
const STORES = {
  PENDING_REQUESTS: 'pendingRequests',
  CACHED_DATA: 'cachedData',
  USER_DATA: 'userData',
  SKETCHBOOKS: 'sketchbooks',
};

// Initialize the database
export const initDatabase = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = (event) => {
      console.error('IndexedDB error:', event);
      reject('Error opening IndexedDB');
    };

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      
      // Create object stores if they don't exist
      if (!db.objectStoreNames.contains(STORES.PENDING_REQUESTS)) {
        db.createObjectStore(STORES.PENDING_REQUESTS, { keyPath: 'id', autoIncrement: true });
      }
      
      if (!db.objectStoreNames.contains(STORES.CACHED_DATA)) {
        const cachedDataStore = db.createObjectStore(STORES.CACHED_DATA, { keyPath: 'key' });
        cachedDataStore.createIndex('timestamp', 'timestamp', { unique: false });
        cachedDataStore.createIndex('expiry', 'expiry', { unique: false });
      }
      
      if (!db.objectStoreNames.contains(STORES.USER_DATA)) {
        db.createObjectStore(STORES.USER_DATA, { keyPath: 'id' });
      }
      
      if (!db.objectStoreNames.contains(STORES.SKETCHBOOKS)) {
        const sketchbooksStore = db.createObjectStore(STORES.SKETCHBOOKS, { keyPath: 'id' });
        sketchbooksStore.createIndex('userId', 'userId', { unique: false });
        sketchbooksStore.createIndex('lastModified', 'lastModified', { unique: false });
      }
    };
  });
};

// Generic function to add data to a store
export const addToStore = async <T>(
  storeName: string, 
  data: T
): Promise<number | string> => {
  const db = await initDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.add(data);
    
    request.onsuccess = () => {
      resolve(request.result);
    };
    
    request.onerror = () => {
      reject(request.error);
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Generic function to get data from a store
export const getFromStore = async <T>(
  storeName: string, 
  key: string | number
): Promise<T | null> => {
  const db = await initDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.get(key);
    
    request.onsuccess = () => {
      resolve(request.result || null);
    };
    
    request.onerror = () => {
      reject(request.error);
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Generic function to update data in a store
export const updateInStore = async <T>(
  storeName: string, 
  data: T
): Promise<void> => {
  const db = await initDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.put(data);
    
    request.onsuccess = () => {
      resolve();
    };
    
    request.onerror = () => {
      reject(request.error);
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Generic function to delete data from a store
export const deleteFromStore = async (
  storeName: string, 
  key: string | number
): Promise<void> => {
  const db = await initDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.delete(key);
    
    request.onsuccess = () => {
      resolve();
    };
    
    request.onerror = () => {
      reject(request.error);
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Get all data from a store
export const getAllFromStore = async <T>(storeName: string): Promise<T[]> => {
  const db = await initDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.getAll();
    
    request.onsuccess = () => {
      resolve(request.result);
    };
    
    request.onerror = () => {
      reject(request.error);
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Clear all data from a store
export const clearStore = async (storeName: string): Promise<void> => {
  const db = await initDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.clear();
    
    request.onsuccess = () => {
      resolve();
    };
    
    request.onerror = () => {
      reject(request.error);
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

// Export store names for easy access
export const OFFLINE_STORES = STORES;
