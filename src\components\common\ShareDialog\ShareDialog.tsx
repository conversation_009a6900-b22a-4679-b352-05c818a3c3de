import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  Tabs,
  Tab,
  Box,
  TextField,
  Chip,
  Paper,
  IconButton,
  Typography,
} from '@mui/material';
import { Close, Group, Share, FileDownload } from '@mui/icons-material';
import { styled } from '@mui/system';
import CustomButton from '../button/CustomButton';
import { FaFilePdf, FaShare } from 'react-icons/fa';
import { GrCompliance } from 'react-icons/gr';
import {
  useDownloadPreviousComplianceReportMutation,
  useGetPreviousComplianceReportsQuery,
} from '../../../services/complianceServices';
import { toast } from 'react-hot-toast';
import useLocalStorage from '../../../hooks/useLocalStorage';

interface ShareDialogProps {
  open: boolean;
  onClose: () => void;
  onSendEmail: (emailData: {
    sendToEmails: string[];
    ccToEmails: string[];
    subject: string;
    content: string;
    hasAttachment: boolean;
    fileNames: string[];
  }) => void;
  onExport: () => void;
  fileName: string;
  projectId?: string;
}

const TabPanel = ({ children, value, index }: any) => (
  <div hidden={value !== index} style={{ padding: '20px 0' }}>
    {value === index && children}
  </div>
);

const StyledDialog = styled(Dialog)({
  '& .MuiDialog-paper': {
    width: '600px',
    maxWidth: '90vw',
  },
});

const StyledTab = styled(Tab)({
  textTransform: 'none',
  fontWeight: 500,
  fontSize: '14px',
  fontFamily:
    "system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  minHeight: '56px',
  padding: '0 24px',
  color: '#94a3b8',
  '&.Mui-selected': {
    color: 'blue',
    fontWeight: 600,
  },
  '& .MuiSvgIcon-root': {
    color: 'inherit',
  },
});

export const ShareDialog = ({
  open,
  onClose,
  onSendEmail,
  onExport,
  fileName,
  projectId,
}: ShareDialogProps) => {
  const [currentUser] = useLocalStorage('user', null);

  const [downloadPreviousComplianceReport] =
    useDownloadPreviousComplianceReportMutation();

  const {
    data: previousReports = [],
    isLoading: isPreviousReportsLoading,
    error: previousReportsError,
  } = useGetPreviousComplianceReportsQuery(
    {
      userId: currentUser?.id,
      projectId,
    },
    {
      skip: !projectId,
    }
  );

  const [activeTab, setActiveTab] = useState(0);
  const [emailData, setEmailData] = useState({
    sendToEmails: [] as string[],
    ccToEmails: [] as string[],
    subject: '',
    content: '',
  });
  const [emailInput, setEmailInput] = useState('');
  const [ccInput, setCcInput] = useState('');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleKeyDown = (e: React.KeyboardEvent, type: 'to' | 'cc') => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const input = type === 'to' ? emailInput.trim() : ccInput.trim();

      if (input && isValidEmail(input)) {
        setEmailData((prev) => ({
          ...prev,
          [type === 'to' ? 'sendToEmails' : 'ccToEmails']: [
            ...prev[type === 'to' ? 'sendToEmails' : 'ccToEmails'],
            input,
          ],
        }));
        type === 'to' ? setEmailInput('') : setCcInput('');
      }
    }
  };

  const handleDelete = (email: string, type: 'to' | 'cc') => {
    setEmailData((prev) => ({
      ...prev,
      [type === 'to' ? 'sendToEmails' : 'ccToEmails']: prev[
        type === 'to' ? 'sendToEmails' : 'ccToEmails'
      ].filter((e) => e !== email),
    }));
  };

  const handleSendEmail = () => {
    // Add any remaining input if valid
    if (
      emailInput &&
      isValidEmail(emailInput) &&
      !emailData.sendToEmails.includes(emailInput)
    ) {
      emailData.sendToEmails.push(emailInput);
    }
    if (
      ccInput &&
      isValidEmail(ccInput) &&
      !emailData.ccToEmails.includes(ccInput)
    ) {
      emailData.ccToEmails.push(ccInput);
    }

    onSendEmail({
      ...emailData,
      hasAttachment: true,
      fileNames: [fileName],
    });
  };

  const handleAddEmail = (type: 'to' | 'cc') => {
    const input = type === 'to' ? emailInput.trim() : ccInput.trim();

    if (input && isValidEmail(input)) {
      setEmailData((prev) => ({
        ...prev,
        [type === 'to' ? 'sendToEmails' : 'ccToEmails']: [
          ...prev[type === 'to' ? 'sendToEmails' : 'ccToEmails'],
          input,
        ],
      }));
      type === 'to' ? setEmailInput('') : setCcInput('');
    }
  };

  const onComplincesRepoertExport = async () => {
    try {
      const blob = await downloadPreviousComplianceReport({
        userId: currentUser?.id,
        projectId,
      }).unwrap();

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `compliance-report-${new Date().toISOString().split('T')[0]}.pdf`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(url);
      toast.success('Previous report downloaded successfully');
    } catch (error) {
      console.error('Error downloading previous report:', error);
      toast.error('Failed to download previous report');
    }
  };

  return (
    <StyledDialog open={open} onClose={onClose}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          p: 2,
          borderBottom: '1px solid #e0e0e0',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            flexGrow: 1,
            fontFamily:
              "system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
          }}
        >
          Share {fileName}
        </Typography>
        <IconButton onClick={onClose}>
          <Close />
        </IconButton>
      </Box>

      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          '& .MuiTabs-indicator': {
            backgroundColor: '#1976d2',
            height: 3,
            borderRadius: '3px 3px 0 0',
          },
        }}
      >
        <StyledTab icon={<Share />} iconPosition="start" label="Share" />
        <StyledTab
          icon={<FileDownload />}
          iconPosition="start"
          label="Export"
        />
        <StyledTab icon={<Group />} iconPosition="start" label="Collaborate" />
      </Tabs>

      <DialogContent>
        <TabPanel value={activeTab} index={0}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box>
              <TextField
                label="To"
                value={emailInput}
                onChange={(e) => setEmailInput(e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, 'to')}
                onBlur={() => handleAddEmail('to')}
                fullWidth
                size="small"
                placeholder="Enter recipient email and press Enter"
                helperText="Press Enter or comma to add multiple emails"
              />
              {emailData.sendToEmails.length > 0 && (
                <Paper
                  sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 0.5,
                    mt: 1,
                    p: 0.5,
                  }}
                >
                  {emailData.sendToEmails.map((email) => (
                    <Chip
                      key={email}
                      label={email}
                      onDelete={() => handleDelete(email, 'to')}
                      size="small"
                    />
                  ))}
                </Paper>
              )}
            </Box>

            <Box>
              <TextField
                label="CC"
                value={ccInput}
                onChange={(e) => setCcInput(e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, 'cc')}
                onBlur={() => handleAddEmail('cc')}
                fullWidth
                size="small"
                placeholder="Enter CC email and press Enter"
                helperText="Press Enter or comma to add multiple emails"
              />
              {emailData.ccToEmails.length > 0 && (
                <Paper
                  sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 0.5,
                    mt: 1,
                    p: 0.5,
                  }}
                >
                  {emailData.ccToEmails.map((email) => (
                    <Chip
                      key={email}
                      label={email}
                      onDelete={() => handleDelete(email, 'cc')}
                      size="small"
                    />
                  ))}
                </Paper>
              )}
            </Box>

            <TextField
              label="Subject"
              value={emailData.subject}
              onChange={(e) =>
                setEmailData((prev) => ({ ...prev, subject: e.target.value }))
              }
              fullWidth
              size="small"
            />
            <TextField
              label="Message"
              value={emailData.content}
              onChange={(e) =>
                setEmailData((prev) => ({ ...prev, content: e.target.value }))
              }
              multiline
              rows={4}
              fullWidth
            />

            <Box sx={{ mt: 1 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>Attachment:</strong> {fileName}
              </Typography>
            </Box>
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                width: '100%',
              }}
            >
              <CustomButton
                style={{ width: '30%', alignSelf: 'flex-end' }}
                label="Send"
                onClick={handleSendEmail}
                disabled={
                  emailData.sendToEmails.length === 0 || !emailData.subject
                }
                type="secondary"
                leftIcon={<FaShare size={20} />}
              />
            </div>
          </Box>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              width: '100%',
              flexDirection: 'column',
              gap: '10px',
            }}
          >
            <CustomButton
              // style={{backgroundColor:"red"}}
              label="Download PDF"
              onClick={onExport}
              type="primary"
              leftIcon={<FaFilePdf size={20} />}
            />
            {previousReports && previousReports.length !== 0 && (
              <CustomButton
                label="Download Compliance Report"
                onClick={onComplincesRepoertExport}
                type="primary"
                leftIcon={<GrCompliance size={20} />}
              />
            )}
          </div>
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              placeholder="Add emails or people"
              size="small"
            />
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle2" sx={{ mr: 1 }}>
              Workspace members
            </Typography>
            <Typography variant="body2" color="text.secondary">
              No access
            </Typography>
          </Box>
        </TabPanel>
      </DialogContent>
    </StyledDialog>
  );
};
