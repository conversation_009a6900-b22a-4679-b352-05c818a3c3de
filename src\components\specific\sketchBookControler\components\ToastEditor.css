.toast-editor-wrapper {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

/* Ensure the editor fits within its container */
.toast-editor-wrapper .toastui-editor-defaultUI {
  width: 100%;
  border: none;
}

/* Adjust toolbar styling */
.toast-editor-wrapper .toastui-editor-toolbar {
  background-color: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border);
}

/* Improve table styling in the editor */
.toast-editor-wrapper .toastui-editor-contents table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
}

.toast-editor-wrapper .toastui-editor-contents th,
.toast-editor-wrapper .toastui-editor-contents td {
  border: 1px solid var(--color-border);
  padding: 0.5em;
  text-align: left;
}

.toast-editor-wrapper .toastui-editor-contents th {
  background-color: var(--color-grey-200);
  color: var(--color-text-primary);
  font-weight: bold;
}

/* Adjust editor content area */
.toast-editor-wrapper .toastui-editor-main {
  background-color: var(--color-background-secondary);
}

/* Adjust markdown editor */
.toast-editor-wrapper .toastui-editor-md-container {
  background-color: var(--color-background-secondary);
}

.toast-editor-wrapper .toastui-editor-md-code-block-line-background {
  background-color: var(--color-grey-100);
}

/* Improve code block visibility */
.toast-editor-wrapper .toastui-editor-contents pre {
  background-color: var(--color-grey-100);
  padding: 0.5em;
  border-radius: 4px;
  margin: 0.5em 0;
  color: var(--color-text-primary);
  font-family: var(--font-family-code);
}

.toast-editor-wrapper .toastui-editor-contents code {
  background-color: var(--color-grey-100);
  padding: 0.1em 0.3em;
  border-radius: 3px;
  font-family: var(--font-family-code);
  color: var(--color-text-primary);
}

/* Adjust preview area */
.toast-editor-wrapper .toastui-editor-ww-container {
  background-color: var(--color-background-secondary);
}

/* Dark theme adjustments */
.toast-editor-wrapper.dark-theme .toastui-editor-toolbar {
  background-color: var(--color-background-primary-dark, #252525);
  border-color: var(--color-border-dark, #444);
}

.toast-editor-wrapper.dark-theme .toastui-editor-main {
  background-color: var(--color-background-secondary-dark, #333);
}

.toast-editor-wrapper.dark-theme .toastui-editor-md-container {
  background-color: var(--color-background-secondary-dark, #333);
}

.toast-editor-wrapper.dark-theme .toastui-editor-ww-container {
  background-color: var(--color-background-secondary-dark, #333);
}

/* Dark theme table styling */
.toast-editor-wrapper.dark-theme .toastui-editor-contents th {
  background-color: #3a3a3a; /* Darker background for better contrast in dark mode */
  color: #ffffff; /* White text for better visibility in dark mode */
  font-weight: bold;
}

.toast-editor-wrapper.dark-theme .toastui-editor-contents td {
  border-color: var(--color-border-dark, #444);
}

.toast-editor-wrapper.dark-theme .toastui-editor-contents table {
  border-color: var(--color-border-dark, #444);
}

/* Dark theme code block styling */
.toast-editor-wrapper.dark-theme .toastui-editor-contents pre,
.toast-editor-wrapper.dark-theme .toastui-editor-contents code {
  background-color: #2d2d2d; /* Darker background for better contrast in dark mode */
  color: #e0e0e0; /* Light gray text for better visibility in dark mode */
  font-family: var(--font-family-code);
}

.toast-editor-wrapper.dark-theme .toastui-editor-md-code-block-line-background {
  background-color: #2d2d2d; /* Darker background for better contrast in dark mode */
}

/* Ensure the editor respects the theme */
.toast-editor-wrapper.dark-theme .toastui-editor-contents,
.toast-editor-wrapper.dark-theme .toastui-editor-md-preview {
  color: var(--color-text-primary-dark, #eee);
}

/* Adjust toolbar button hover states */
.toast-editor-wrapper .toastui-editor-toolbar-icons:hover {
  background-color: var(--color-background-hover);
}

.toast-editor-wrapper.dark-theme .toastui-editor-toolbar-icons:hover {
  background-color: var(--color-background-hover-dark, #444);
}

/* Adjust dropdown menus */
.toastui-editor-dropdown-menu {
  background-color: var(--color-background-secondary);
  border-color: var(--color-border);
}

.toast-editor-wrapper.dark-theme .toastui-editor-dropdown-menu {
  background-color: var(--color-background-secondary-dark, #333);
  border-color: var(--color-border-dark, #444);
}

/* Ensure the editor is responsive */
@media (max-width: 768px) {
  .toast-editor-wrapper .toastui-editor-defaultUI {
    flex-direction: column;
  }

  .toast-editor-wrapper .toastui-editor-md-container,
  .toast-editor-wrapper .toastui-editor-ww-container {
    width: 100% !important;
  }
}
