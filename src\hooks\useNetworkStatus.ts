import { useState, useEffect, useCallback } from 'react';
import { 
  NetworkStatus, 
  defaultNetworkStatus, 
  getNetworkStatus, 
  notifyNetworkStatusChange,
  checkConnectivity,
  supportsNetworkInfoAPI
} from '../utils/networkUtils';

/**
 * Hook for monitoring network status with advanced features
 * 
 * @param options Configuration options
 * @returns Network status and utility functions
 */
export const useNetworkStatus = (options: {
  pingInterval?: number;
  notifyOnChange?: boolean;
  pingEndpoint?: string;
  retryOnReconnect?: boolean;
} = {}) => {
  const {
    pingInterval = 30000,
    notifyOnChange = true,
    pingEndpoint,
    retryOnReconnect = true,
  } = options;

  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>(defaultNetworkStatus);
  const [serverReachable, setServerReachable] = useState<boolean>(true);
  const [pendingRequests, setPendingRequests] = useState<Array<() => Promise<any>>>([]);

  // Update network status
  const updateNetworkStatus = useCallback(() => {
    const newStatus = getNetworkStatus();
    setNetworkStatus(prevStatus => {
      // Only notify if online status changed
      if (notifyOnChange && prevStatus.online !== newStatus.online) {
        notifyNetworkStatusChange(newStatus.online);
      }
      return newStatus;
    });
  }, [notifyOnChange]);

  // Check actual server connectivity
  const checkServerConnectivity = useCallback(async () => {
    if (networkStatus.online) {
      const { serverReachable: isReachable } = await checkConnectivity(pingEndpoint);
      setServerReachable(isReachable);
      
      // If server is reachable again and we have pending requests, retry them
      if (isReachable && retryOnReconnect && pendingRequests.length > 0) {
        const requests = [...pendingRequests];
        setPendingRequests([]);
        
        // Execute pending requests
        requests.forEach(request => {
          request().catch(error => {
            console.error('Failed to execute pending request:', error);
          });
        });
      }
    } else {
      setServerReachable(false);
    }
  }, [networkStatus.online, pendingRequests, pingEndpoint, retryOnReconnect]);

  // Add a request to the pending queue
  const addPendingRequest = useCallback((request: () => Promise<any>) => {
    setPendingRequests(prev => [...prev, request]);
  }, []);

  // Listen for online/offline events
  useEffect(() => {
    // Initial check
    updateNetworkStatus();
    checkServerConnectivity();

    // Set up event listeners for online/offline events
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    // Set up connection change listener if supported
    if (supportsNetworkInfoAPI()) {
      (navigator as any).connection.addEventListener('change', updateNetworkStatus);
    }

    // Set up periodic ping to check actual connectivity
    const pingIntervalId = setInterval(checkServerConnectivity, pingInterval);

    // Clean up
    return () => {
      window.removeEventListener('online', updateNetworkStatus);
      window.removeEventListener('offline', updateNetworkStatus);
      
      if (supportsNetworkInfoAPI()) {
        (navigator as any).connection.removeEventListener('change', updateNetworkStatus);
      }
      
      clearInterval(pingIntervalId);
    };
  }, [checkServerConnectivity, pingInterval, updateNetworkStatus]);

  return {
    ...networkStatus,
    serverReachable,
    isFullyConnected: networkStatus.online && serverReachable,
    addPendingRequest,
    checkConnectivity: checkServerConnectivity,
  };
};

export default useNetworkStatus;
