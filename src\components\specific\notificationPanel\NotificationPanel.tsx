import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { toast } from 'react-hot-toast';
import styles from './NotificationPanel.module.css';
import closeIcon from '../../../assets/images/Cross.png';
import { RootState } from '../../../store/store';
import {
  selectNotifications,
  selectUnreadCount,
  markAsRead,
  markAllAsRead,
} from '../../../store/slices/notificationSlice';
import {
  formatNotificationDate,
  getNotificationIcon,
  getNotificationPriorityColor,
  groupNotificationsByDate,
} from '../../../utils/notificationUtils';
import { WorkflowNotification } from '../../../types/workflow/index';
import { useNotifications } from '../../../hooks/useNotifications';

interface NotificationPanelProps {
  onClose: () => void;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({ onClose }) => {
  const dispatch = useDispatch();

  // Use API-enabled notifications for this component
  const {
    notifications,
    unreadCount,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    isMarkingAsRead,
    error,
  } = useNotifications(true); // Enable API

  const handleNotificationClick = async (
    notification: WorkflowNotification
  ) => {
    // Mark as read if not already read
    if (!notification.read) {
      try {
        console.log('Marking notification as read via API:', notification.id);
        await markNotificationAsRead(notification.id);
        console.log('Notification marked as read successfully');
        toast.success('Notification marked as read');
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
        toast.error('Failed to mark notification as read');
        // Fallback to local state update if API fails
        dispatch(markAsRead(notification.id));
      }
    }

    // Do nothing else - just mark as read
    console.log('Notification clicked:', notification.id);
  };

  const handleMarkAllAsRead = async () => {
    try {
      console.log('Marking all notifications as read via API');
      await markAllNotificationsAsRead();
      console.log('All notifications marked as read successfully');
      toast.success('All notifications marked as read');
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
      // Fallback to local state update if API fails
      dispatch(markAllAsRead());
    }
  };

  // Group notifications by date for better organization
  const groupedNotifications = groupNotificationsByDate(notifications);

  return (
    <div className={styles.notificationPanel}>
      <div className={styles.header}>
        <h3>Notifications</h3>
        <div className={styles.headerActions}>
          {unreadCount > 0 && (
            <button
              className={styles.markAllReadBtn}
              onClick={handleMarkAllAsRead}
              disabled={isMarkingAsRead}
              title="Mark all as read"
            >
              {isMarkingAsRead ? 'Marking...' : 'Mark all read'}
            </button>
          )}
          <button className={styles.closeButton} onClick={onClose}>
            <img src={closeIcon} alt="Close" />
          </button>
        </div>
      </div>

      {error && (
        <div className={styles.errorBanner}>
          <span className={styles.errorText}>⚠️ {error}</span>
        </div>
      )}

      <div className={styles.notificationListContainer}>
        {notifications.length > 0 ? (
          Object.entries(groupedNotifications).map(
            ([dateGroup, groupNotifications]) => (
              <div key={dateGroup} className={styles.dateGroup}>
                <div className={styles.dateGroupHeader}>{dateGroup}</div>
                {groupNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`${styles.notificationItem} ${
                      !notification.read ? styles.unread : ''
                    } ${notification.actionRequired ? styles.actionRequired : ''}`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className={styles.notificationIcon}>
                      {getNotificationIcon(
                        notification.type,
                        notification.status
                      )}
                    </div>
                    <div className={styles.notificationContent}>
                      <p className={styles.notificationMessage}>
                        {notification.message}
                      </p>
                      <div className={styles.notificationMeta}>
                        <span className={styles.notificationDate}>
                          {formatNotificationDate(notification.createdAt)}
                        </span>
                        {notification.priority && (
                          <span
                            className={styles.priorityBadge}
                            style={{
                              backgroundColor: getNotificationPriorityColor(
                                notification.priority
                              ),
                            }}
                          >
                            {notification.priority}
                          </span>
                        )}
                        {notification.actionRequired && (
                          <span className={styles.actionBadge}>
                            Action Required
                          </span>
                        )}
                      </div>
                    </div>
                    {!notification.read && (
                      <div className={styles.unreadDot}></div>
                    )}
                  </div>
                ))}
              </div>
            )
          )
        ) : (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>🔔</div>
            <p>No notifications yet</p>
            <span>You'll see workflow updates here</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationPanel;
