import React, { useState } from 'react';
import { useNetwork } from '../../contexts/NetworkContext';
import {
  Box,
  Typography,
  IconButton,
  Tooltip,
  Badge,
  Popover,
  Switch,
  FormControlLabel,
  Button,
  Divider,
} from '@mui/material';
import WifiIcon from '@mui/icons-material/Wifi';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import SignalCellular4BarIcon from '@mui/icons-material/SignalCellular4Bar';
import SignalCellular1BarIcon from '@mui/icons-material/SignalCellular1Bar';
import RefreshIcon from '@mui/icons-material/Refresh';
import SyncIcon from '@mui/icons-material/Sync';
import { styled } from '@mui/material/styles';
import useOfflineSync from '../../hooks/useOfflineSync';

// Styled components
const StatusBadge = styled(Badge)(({ theme }) => ({
  '& .MuiBadge-badge': {
    right: 3,
    top: 3,
    border: `2px solid ${theme.palette.background.paper}`,
    padding: '0 4px',
  },
}));

const NetworkStatusIndicator: React.FC = () => {
  const {
    online,
    serverReachable,
    effectiveType,
    isOfflineModeEnabled,
    setOfflineMode,
    checkConnectivity,
  } = useNetwork();

  const { isSyncing, pendingItemsCount, syncPendingRequests, lastSyncTime } =
    useOfflineSync({
      autoSync: true,
      syncInterval: 60000,
      syncOnReconnect: true,
    });

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  // Open popover
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleRefresh = () => {
    checkConnectivity();
  };

  const handleSync = () => {
    if (online && !isOfflineModeEnabled) {
      syncPendingRequests();
    }
  };

  const handleOfflineModeToggle = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setOfflineMode(event.target.checked);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'network-status-popover' : undefined;

  // Determine which icon to show based on connection status
  const getConnectionIcon = () => {
    if (isOfflineModeEnabled) {
      return <WifiOffIcon color="warning" />;
    }

    if (!online) {
      return <WifiOffIcon color="error" />;
    }

    if (!serverReachable) {
      return <WifiIcon color="warning" />;
    }

    if (effectiveType === '4g' || effectiveType === 'unknown') {
      return <SignalCellular4BarIcon color="success" />;
    }

    return <SignalCellular1BarIcon color="warning" />;
  };

  // Determine badge color based on connection status
  const getBadgeColor = () => {
    if (isOfflineModeEnabled) return 'warning';
    if (!online) return 'error';
    if (!serverReachable) return 'warning';
    return 'success';
  };

  return (
    <>
      <Tooltip
        title={
          isOfflineModeEnabled
            ? 'Offline Mode Enabled'
            : !online
              ? 'You are offline'
              : !serverReachable
                ? 'Limited connectivity'
                : `Connected (${effectiveType})`
        }
      >
        <IconButton aria-describedby={id} onClick={handleClick} size="small">
          <StatusBadge variant="dot" color={getBadgeColor()} overlap="circular">
            {getConnectionIcon()}
          </StatusBadge>
        </IconButton>
      </Tooltip>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >
        <Box sx={{ p: 2, width: 300 }}>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={1}
          >
            <Typography variant="h6">Network Status</Typography>
            <Box display="flex" gap={1}>
              {pendingItemsCount > 0 && online && !isOfflineModeEnabled && (
                <Tooltip title="Sync pending changes">
                  <IconButton
                    size="small"
                    onClick={handleSync}
                    disabled={isSyncing}
                  >
                    <SyncIcon
                      fontSize="small"
                      color={isSyncing ? 'disabled' : 'primary'}
                    />
                  </IconButton>
                </Tooltip>
              )}
              <IconButton size="small" onClick={handleRefresh}>
                <RefreshIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>

          <Divider sx={{ mb: 2 }} />

          <Box mb={2}>
            <Typography variant="body2" gutterBottom>
              Status:{' '}
              {isOfflineModeEnabled
                ? 'Offline Mode Enabled'
                : !online
                  ? 'Offline'
                  : !serverReachable
                    ? 'Limited Connectivity'
                    : 'Online'}
            </Typography>

            {online && !isOfflineModeEnabled && (
              <Typography variant="body2" gutterBottom>
                Connection: {effectiveType.toUpperCase()}
              </Typography>
            )}

            {pendingItemsCount > 0 && (
              <Typography variant="body2" color="warning.main" gutterBottom>
                Pending items: {pendingItemsCount}
                {lastSyncTime && (
                  <Typography
                    variant="caption"
                    display="block"
                    color="text.secondary"
                  >
                    Last sync: {lastSyncTime.toLocaleTimeString()}
                  </Typography>
                )}
              </Typography>
            )}

            {isSyncing && (
              <Typography variant="body2" color="primary.main" gutterBottom>
                Syncing changes...
              </Typography>
            )}
          </Box>

          <FormControlLabel
            control={
              <Switch
                checked={isOfflineModeEnabled}
                onChange={handleOfflineModeToggle}
                color="primary"
              />
            }
            label="Enable Offline Mode"
          />

          <Typography
            variant="caption"
            color="text.secondary"
            display="block"
            mt={1}
          >
            Offline mode allows you to work without an internet connection.
            Changes will be synchronized when you're back online.
          </Typography>
        </Box>
      </Popover>
    </>
  );
};

export default NetworkStatusIndicator;
