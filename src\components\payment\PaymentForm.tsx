import React, { useState, useEffect } from 'react';
import { CardElement } from '@stripe/react-stripe-js';
import {
  Box,
  Typography,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useStripePayment } from '../../hooks/payment/useStripe';
import { usePayment } from '../../hooks/payment/usePayment';
import { PaymentFormData } from '../../types/payment';
import { formatCurrency } from '../../utils/payment/stripeHelpers';
import { validateEmail } from '../../utils/payment/paymentValidation';
import {
  getCurrentUserId,
  getCurrentUserEmail,
} from '../../utils/auth/userHelpers';
import { useTheme as useThemeContext } from '../../contexts/ThemeContext';
import styles from './PaymentForm.module.css';
import CustomInput from '../common/input/CustomInput';

const PaymentForm: React.FC = () => {
  const {
    isReady,
    isProcessing,
    cardComplete,
    cardError,
    handleCardChange,
    processPayment,
    resetPayment,
  } = useStripePayment();

  const {
    currentPlan,
    paymentError,
    clearPaymentError,
    handlePaymentSuccess,
    handlePaymentError,
  } = usePayment();

  const themeContext = useThemeContext();

  // Get current user info for pre-filling form
  const currentUserId = getCurrentUserId();
  const currentUserEmail = getCurrentUserEmail();

  const [formData, setFormData] = useState<PaymentFormData>({
    planId: currentPlan?.id || '',
    customerEmail: currentUserEmail || '',
    customerName: '',
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (currentPlan) {
      setFormData((prev) => ({
        ...prev,
        planId: currentPlan.id,
        customerEmail: prev.customerEmail || currentUserEmail || '',
      }));
    }
  }, [currentPlan, currentUserEmail]);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.customerEmail) {
      errors.customerEmail = 'Email is required';
    } else if (!validateEmail(formData.customerEmail)) {
      errors.customerEmail = 'Please enter a valid email address';
    }

    if (!formData.customerName || formData.customerName.trim().length < 2) {
      errors.customerName = 'Please enter your full name';
    }

    if (!cardComplete) {
      errors.card = 'Please complete your card information';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange =
    (field: keyof PaymentFormData) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: event.target.value,
      }));

      // Clear field error when user starts typing
      if (formErrors[field]) {
        setFormErrors((prev) => ({
          ...prev,
          [field]: '',
        }));
      }
    };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!isReady || isProcessing || !currentPlan) {
      return;
    }

    // Check if user is authenticated
    if (!currentUserId) {
      handlePaymentError(
        'User not authenticated. Please log in and try again.'
      );
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    clearPaymentError();

    try {
      const paymentRecord = await processPayment(formData);
      handlePaymentSuccess(paymentRecord);
    } catch (error: any) {
      const errorMessage = error.message || 'Payment failed';
      handlePaymentError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    resetPayment();
    setFormData({
      planId: currentPlan?.id || '',
      customerEmail: '',
      customerName: '',
    });
    setFormErrors({});
    setIsSubmitting(false);
  };

  if (!currentPlan) {
    return (
      <Box className={styles.container}>
        <Alert severity="error">
          No payment plan selected. Please go back and select a plan.
        </Alert>
      </Box>
    );
  }

  return (
    <Box className={styles.container}>
      <div className={styles.header}>
        <Typography variant="h4" className={styles.title}>
          Complete Your Payment
        </Typography>
        <div className={styles.planSummary}>
          <Typography variant="h6">{currentPlan.name} Plan</Typography>
          <Typography variant="body2" className={styles.description}>
            {currentPlan.description}
          </Typography>
          <Typography variant="body2" className={styles.tokens}>
            {currentPlan.tokens} tokens • {currentPlan.maxGraphs} graphs
          </Typography>
          <Typography variant="h5" className={styles.price}>
            {formatCurrency(currentPlan.price, currentPlan.currency)}
            {currentPlan.interval && (
              <span className={styles.interval}>/{currentPlan.interval}</span>
            )}
          </Typography>
        </div>
      </div>

      <form onSubmit={handleSubmit} className={styles.form}>
        {/* Customer Information */}
        <div className={styles.section}>
          <Typography variant="h6" className={styles.sectionTitle}>
            Customer Information
          </Typography>

          <CustomInput
            label="Email Address"
            type="email"
            value={formData.customerEmail}
            onChange={handleInputChange('customerEmail')}
            error={!!formErrors.customerEmail}
            helperText={formErrors.customerEmail}
            required
            fullWidth
            className={styles.field}
          />

          <CustomInput
            label="Full Name"
            type="text"
            value={formData.customerName}
            onChange={handleInputChange('customerName')}
            error={!!formErrors.customerName}
            helperText={formErrors.customerName}
            required
            fullWidth
            className={styles.field}
          />
        </div>

        {/* Payment Information */}
        <div className={styles.section}>
          <Typography variant="h6" className={styles.sectionTitle}>
            Payment Information
          </Typography>

          <div className={styles.cardElementContainer}>
            <CardElement
              onChange={handleCardChange}
              options={{
                style: {
                  base: {
                    fontSize: '16px',
                    color: themeContext.isDarkMode ? '#ffffff' : '#424770',
                    backgroundColor: themeContext.isDarkMode
                      ? '#2d2d2d'
                      : '#ffffff',
                    '::placeholder': {
                      color: themeContext.isDarkMode ? '#b0bec5' : '#aab7c4',
                    },
                  },
                  invalid: {
                    color: '#f44336',
                  },
                  complete: {
                    color: themeContext.isDarkMode ? '#66bb6a' : '#2e7d32',
                  },
                },
                hidePostalCode: false,
              }}
              className={styles.cardElement}
            />
            {(cardError || formErrors.card) && (
              <Typography variant="body2" className={styles.cardError}>
                {cardError || formErrors.card}
              </Typography>
            )}
          </div>
        </div>

        {/* Error Display */}
        {paymentError && (
          <Alert severity="error" className={styles.alert}>
            {paymentError}
          </Alert>
        )}

        {/* Action Buttons */}
        <div className={styles.actions}>
          <Button
            type="button"
            variant="outlined"
            onClick={handleReset}
            disabled={isSubmitting || isProcessing}
            className={styles.resetButton}
          >
            Reset
          </Button>

          <Button
            type="submit"
            variant="contained"
            disabled={!isReady || isSubmitting || isProcessing || !cardComplete}
            className={styles.submitButton}
          >
            {isSubmitting || isProcessing ? (
              <>
                <CircularProgress size={20} className={styles.spinner} />
                Processing...
              </>
            ) : (
              `Pay ${formatCurrency(currentPlan.price, currentPlan.currency)}`
            )}
          </Button>
        </div>

        {/* Security Notice */}
        <Typography variant="body2" className={styles.securityNotice}>
          🔒 Your payment information is secure and encrypted
        </Typography>
      </form>
    </Box>
  );
};

export default PaymentForm;
