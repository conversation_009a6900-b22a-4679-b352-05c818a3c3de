.editor-controls-container {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 11px;
  height: auto;
  width: auto;
  z-index: 10000;
  pointer-events: none;
}

.editor-controls-container > * {
  pointer-events: auto;
}

.editor-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  width: auto;
  max-width: 240px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
  padding: 6px;
  z-index: 1000;
  backdrop-filter: blur(4px);
  transition: all 0.2s ease;
  transform-origin: top right;
}

.collapsed {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #174e86;
  color: white;
}

.collapsed-icon {
  font-size: 18px;
}

.section {
  margin-bottom: 6px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 6px;
  margin: 0 0 4px 0;
  cursor: pointer;
  background-color: #f5f5f5;
  border-radius: 3px;
  font-weight: 500;
  font-size: 11px;
  color: #333;
  transition: background-color 0.2s;
}

.section-header:hover {
  background-color: #e9ecef;
}

.caret-icon {
  width: 12px;
  height: 12px;
}

.control-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 2px;
}

.control-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  padding: 4px;
  width: 48px;
  height: 48px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  background: white;
  transition: all 0.2s ease;
  user-select: none;
}

.control-item svg {
  width: 14px;
  height: 14px;
  color: #174e86;
}

.control-item span {
  font-size: 9px;
  color: #677480;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
}

.control-item:hover {
  background: #f8f9fa;
  border-color: #174e86;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.control-item:active {
  transform: translateY(0);
  background: #e9ecef;
}

.control-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-item.disabled:hover {
  background: white;
  border-color: #e0e0e0;
  transform: none;
  box-shadow: none;
}

/* Toolbar style layout */
.toolbar {
  display: flex;
  flex-wrap: nowrap;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 4px;
  position: absolute;
  top: 10px;
  left: 40px; /* Space for the toggle button */
  transform: translateY(0);
  z-index: 10000;
  backdrop-filter: blur(4px);
  border: 1px solid #e0e0e0;
  flex-direction: column;
  transition: all 0.3s ease;
  opacity: 1;
  visibility: visible;
}

.toolbar.collapsed {
  left: -200px;
  opacity: 0;
  visibility: hidden;
}

/* Toggle button for toolbar */
.toolbar-toggle {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10001;
  transition: all 0.2s ease;
  color: #174e86;
}

.toolbar-toggle:hover {
  background-color: #174e86;
  color: #e9ecef;
}

.toolbar-toggle.active {
  transform: rotate(180deg);
  background-color: #174e86;
  color: #e9ecef;
}

.toolbar-toggle svg {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
}

.toolbar-group:not(:last-child) {
  border-right: none;
  border-bottom: 1px solid #e0e0e0;
  margin-right: 0;
  padding-right: 4px;
  margin-bottom: 4px;
  padding-bottom: 8px;
}

.toolbar-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.15s ease;
  background-color: transparent;
  color: #174e86;
}

.toolbar-item:hover {
  background-color: rgba(23, 78, 134, 0.1);
}

.toolbar-item.active {
  background-color: rgba(23, 78, 134, 0.15);
}

.toolbar-item svg {
  width: 18px;
  height: 18px;
}

.toolbar-item.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.toolbar-item.disabled:hover {
  background-color: transparent;
}

/* Emergency controls in a more accessible location */
.emergency-controls {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 45px;
  left: 10px;
  z-index: 10000;
  border: 1px solid #e0e0e0;
}

.emergency-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  color: #174e86;
  cursor: pointer;
  transition: all 0.2s ease;
}

.emergency-button:hover {
  background-color: #174e86;
  color: white;
}

.emergency-button:active {
  transform: translateY(1px);
}

/* Debug panel positioning */
.debug-panel {
  position: absolute;
  top: 45px;
  left: 10px;
  width: 220px;
  background-color: rgba(0, 0, 0, 0.85);
  color: white;
  border-radius: 6px;
  padding: 12px;
  font-size: 11px;
  z-index: 10500;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  gap: 6px;
  pointer-events: auto;
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.debug-panel-header h3 {
  margin: 0 0 8px 0;
  font-size: 13px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 4px;
  flex-grow: 1;
}

.debug-panel-close {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s;
}

.debug-panel-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.debug-panel-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.debug-panel-content div {
  margin-bottom: 3px;
  font-size: 10px;
}

.debug-panel-content strong {
  color: #8cc0ff;
}

.debug-panel button {
  background-color: #174e86;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 6px;
  transition: background-color 0.2s;
  font-size: 11px;
}

.debug-panel button:hover {
  background-color: #0d2c4f;
}

/* Debug message in a less intrusive location */
.debug-message {
  position: absolute;
  top: 45px;
  left: 10px;
  padding: 6px 12px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  font-size: 11px;
  z-index: 10700;
  max-width: 80%;
  text-align: left;
  animation: fadeIn 0.3s ease;
  pointer-events: none;
}

.debug-message strong {
  color: #8cc0ff;
  margin-right: 6px;
}

/* Custom tooltip using data-tooltip attribute for better positioning */
[data-tooltip] {
  position: relative;
}

/* Keep only one tooltip implementation - for data-tooltip attributes */
[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  pointer-events: none;
  z-index: 1500; /* Match MUI's tooltip z-index */
  margin-top: 4px;
}

/* Keyboard shortcuts modal styling */
.keyboard-shortcuts-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 10600;
  max-width: 500px;
  width: 90%;
  color: #000;
}

.keyboard-shortcuts-modal h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #333;
  font-size: 14px;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.shortcuts-grid h4 {
  margin: 0 0 8px 0;
  color: #555;
  font-size: 13px;
}

.shortcuts-grid ul {
  padding-left: 16px;
  margin: 0;
  font-size: 11px;
}

.shortcuts-grid li {
  margin-bottom: 4px;
}

.close-button {
  padding: 6px 12px;
  background-color: #174e86;
  color: white;
  border: none;
  border-radius: 4px;
  margin-top: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
}

.close-button:hover {
  background-color: #0d2c4f;
}

/* Scrollbar styling */
.editor-controls::-webkit-scrollbar {
  width: 4px;
}

.editor-controls::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.editor-controls::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 2px;
}

.editor-controls::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Media queries for responsiveness */
@media (max-width: 768px) {
  .toolbar {
    padding: 3px;
    left: 35px;
  }

  .toolbar-group {
    padding: 3px;
  }

  .toolbar-item {
    width: 26px;
    height: 26px;
  }

  .toolbar-item svg {
    width: 14px;
    height: 14px;
  }
}

/* Grid controls in a more compact form */
.grid-controls {
  position: absolute;
  top: 10px;
  left: 130px;
  transform: translateY(0);
  background: white;
  padding: 8px;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  z-index: 10400;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.grid-info {
  font-size: 10px;
  color: #666;
  white-space: nowrap;
}

.density-slider {
  width: 80px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.density-slider span {
  font-size: 10px;
  color: #666;
  white-space: nowrap;
}

.density-slider input[type='range'] {
  width: 60px;
  height: 4px;
  -webkit-appearance: none;
  background: #e0e0e0;
  border-radius: 2px;
  outline: none;
}

.density-slider input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #174e86;
  cursor: pointer;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Emergency controls tooltip positioning */
.emergency-controls [data-tooltip]:hover::after {
  bottom: auto;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20001;
}

/* Ensure tooltip visibility */
.toolbar-toggle:hover::after,
.toolbar-item:hover::after,
.emergency-button:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Update the dropdown styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: inherit;
}

.compact-type-label {
  font-size: 10px;
  font-weight: bold;
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: var(--primary-color, #174e86);
  color: white;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-menu {
  position: absolute;
  left: 100%;
  top: 0;
  background: var(--background-color, white);
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.2s,
    visibility 0.2s;
  z-index: 1000;
  min-width: 120px;
  margin-left: 8px;
}

.dropdown.open .dropdown-menu {
  opacity: 1;
  visibility: visible;
}

.dropdown-menu button {
  display: block;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  white-space: nowrap;
  color: inherit;
  font-size: 14px;
  color: #000;
}

.dropdown-menu button:hover {
  background: var(--hover-background, rgba(0, 0, 0, 0.05));
}

.dropdown-menu button.active {
  background: var(--active-background, rgba(23, 78, 134, 0.1));
  color: var(--primary-color, #174e86);
}

/* Style for the collision prevention button */
.toolbar-item.active {
  color: var(--active-background, #e6f0ff);
  background-color: var(--primary-color, #174e86);
}

/* Toolbar Header Styles */
.toolbar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 4px;
  border-bottom: 2px solid #174e86;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #174e86 0%, #1a5490 100%);
  border-radius: 4px;
  color: white;
}

.toolbar-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
}

.title-icon {
  width: 16px;
  height: 16px;
  color: #e6f0ff;
}

/* Toolbar Group Labels */
.toolbar-group-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  font-weight: 500;
  color: #174e86;
  margin-bottom: 4px;
  padding: 2px 4px;
  background: rgba(23, 78, 134, 0.1);
  border-radius: 3px;
  border-left: 3px solid #174e86;
}

.toolbar-group-label svg {
  width: 12px;
  height: 12px;
}

/* Help Panel Styles */
.help-panel {
  position: absolute;
  top: 10px;
  left: 200px;
  width: 280px;
  background-color: rgba(255, 255, 255, 0.98);
  border: 2px solid #174e86;
  border-radius: 8px;
  padding: 16px;
  font-size: 12px;
  z-index: 10500;
  box-shadow: 0 4px 20px rgba(23, 78, 134, 0.2);
  backdrop-filter: blur(8px);
}

.help-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.help-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #174e86;
}

.help-title h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.help-icon {
  width: 18px;
  height: 18px;
  color: #174e86;
}

.help-panel-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(23, 78, 134, 0.1);
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #174e86;
}

.help-panel-close:hover {
  background-color: rgba(23, 78, 134, 0.2);
}

.help-panel-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.help-section h4 {
  margin: 0 0 6px 0;
  font-size: 12px;
  font-weight: 600;
  color: #174e86;
  display: flex;
  align-items: center;
  gap: 6px;
}

.help-section p {
  margin: 0;
  font-size: 11px;
  color: #555;
  line-height: 1.4;
}

/* Modal Description Style */
.modal-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 16px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #174e86;
}
