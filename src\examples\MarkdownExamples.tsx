import React from 'react';
import MarkdownRenderer from '../components/common/chips/Markdown';
import ConfigurableMarkdown, { MARKDOWN_PRESETS } from '../components/common/chips/ConfigurableMarkdown';

/**
 * Example usage of the Enhanced Markdown Renderer
 */
const MarkdownExamples: React.FC = () => {
  // Example content with various features
  const sampleContent = `
# Enhanced Markdown Renderer Demo

## LaTeX and Greek Letters
The quadratic formula uses Alpha and Beta coefficients:
$$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$

Where Alpha = $a$, Beta = $b$, and the discriminant is $\\Delta = b^2 - 4ac$.

## Currency Protection
- Software license: $299.99
- Hardware cost: $1,500.00
- Total budget: $10,000-$15,000

The formula $E = mc^2$ costs $50 to compute.

## Enhanced Tables
| Algorithm | Time Complexity | Space | Cost |
|-----------|----------------|-------|------|
| QuickSort | $O(n \\log n)$ | $O(\\log n)$ | $50 |
| MergeSort | $O(n \\log n)$ | $O(n)$ | $75 |
| HeapSort | $O(n \\log n)$ | $O(1)$ | $60 |

## Task Lists
- [x] Implement LaTeX preprocessing
- [x] Add Greek letter conversion
- [x] Protect currency symbols
- [ ] Add inline HTML support
- [x] Enhance table styling

## Code Blocks with Syntax Highlighting

### JavaScript
\`\`\`javascript
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}
\`\`\`

### Python (auto-detected)
\`\`\`
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    return quicksort(left) + [pivot] + quicksort([x for x in arr if x > pivot])
\`\`\`

### SQL (auto-detected)
\`\`\`
SELECT users.name, COUNT(orders.id) as order_count
FROM users
LEFT JOIN orders ON users.id = orders.user_id
GROUP BY users.id
ORDER BY order_count DESC;
\`\`\`

## Links
- [Internal link](/docs/help)
- [External link](https://github.com)
- [Another external](https://google.com)

## Blockquotes
> This is a blockquote with **bold** and *italic* text.
> 
> It can span multiple lines and include other markdown elements.

## Strikethrough
~~This text is crossed out~~ but this is normal.
  `;

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Markdown Renderer Examples</h1>
      
      {/* Basic Usage */}
      <section style={{ marginBottom: '40px' }}>
        <h2>1. Basic Usage (Default Settings)</h2>
        <div style={{ 
          border: '1px solid #ddd', 
          borderRadius: '8px', 
          padding: '20px',
          backgroundColor: '#fff'
        }}>
          <MarkdownRenderer 
            content={sampleContent}
            baseUrl="https://example.com"
          />
        </div>
      </section>

      {/* Scientific Preset */}
      <section style={{ marginBottom: '40px' }}>
        <h2>2. Scientific Preset (Enhanced LaTeX)</h2>
        <div style={{ 
          border: '1px solid #ddd', 
          borderRadius: '8px', 
          padding: '20px',
          backgroundColor: '#fff'
        }}>
          <ConfigurableMarkdown
            content={sampleContent}
            config={MARKDOWN_PRESETS.scientific}
          />
        </div>
      </section>

      {/* Documentation Preset */}
      <section style={{ marginBottom: '40px' }}>
        <h2>3. Documentation Preset (Code-Focused)</h2>
        <div style={{ 
          border: '1px solid #ddd', 
          borderRadius: '8px', 
          padding: '20px',
          backgroundColor: '#fff'
        }}>
          <ConfigurableMarkdown
            content={sampleContent}
            config={MARKDOWN_PRESETS.documentation}
          />
        </div>
      </section>

      {/* Custom Configuration */}
      <section style={{ marginBottom: '40px' }}>
        <h2>4. Custom Configuration</h2>
        <div style={{ 
          border: '1px solid #ddd', 
          borderRadius: '8px', 
          padding: '20px',
          backgroundColor: '#fff'
        }}>
          <ConfigurableMarkdown
            content={sampleContent}
            config={{
              enableLatexPreprocessing: true,
              enableGreekConversion: true,
              enableCurrencyProtection: true,
              enableSyntaxHighlighting: true,
              showLineNumbers: true,
              lineNumberThreshold: 3,
              enableTableStyling: true,
              enableExternalLinkIndicators: true,
              openExternalLinksInNewTab: true,
              baseUrl: 'https://mydocs.com',
              theme: 'light',
            }}
            onError={(error) => console.error('Markdown rendering error:', error)}
          />
        </div>
      </section>

      {/* Minimal Configuration */}
      <section style={{ marginBottom: '40px' }}>
        <h2>5. Minimal Configuration (Basic Markdown Only)</h2>
        <div style={{ 
          border: '1px solid #ddd', 
          borderRadius: '8px', 
          padding: '20px',
          backgroundColor: '#fff'
        }}>
          <ConfigurableMarkdown
            content={sampleContent}
            config={MARKDOWN_PRESETS.minimal}
          />
        </div>
      </section>

      {/* Feature Comparison */}
      <section style={{ marginBottom: '40px' }}>
        <h2>6. Feature Comparison</h2>
        <table style={{ 
          width: '100%', 
          borderCollapse: 'collapse',
          border: '1px solid #ddd'
        }}>
          <thead>
            <tr style={{ backgroundColor: '#f5f5f5' }}>
              <th style={{ padding: '12px', border: '1px solid #ddd' }}>Feature</th>
              <th style={{ padding: '12px', border: '1px solid #ddd' }}>Basic</th>
              <th style={{ padding: '12px', border: '1px solid #ddd' }}>Scientific</th>
              <th style={{ padding: '12px', border: '1px solid #ddd' }}>Documentation</th>
              <th style={{ padding: '12px', border: '1px solid #ddd' }}>Minimal</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>LaTeX Processing</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>❌</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>❌</td>
            </tr>
            <tr>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>Greek Letter Conversion</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>❌</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>❌</td>
            </tr>
            <tr>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>Syntax Highlighting</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>❌</td>
            </tr>
            <tr>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>Line Numbers</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅ (5+ lines)</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅ (3+ lines)</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅ (5+ lines)</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>❌</td>
            </tr>
            <tr>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>Enhanced Tables</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>❌</td>
            </tr>
            <tr>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>External Link Indicators</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>✅</td>
              <td style={{ padding: '8px', border: '1px solid #ddd' }}>❌</td>
            </tr>
          </tbody>
        </table>
      </section>

      {/* Usage Tips */}
      <section style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '20px', 
        borderRadius: '8px',
        marginTop: '40px'
      }}>
        <h2>Usage Tips</h2>
        <ul>
          <li><strong>Performance:</strong> Use <code>React.memo</code> for components with large markdown content</li>
          <li><strong>Theming:</strong> Define CSS custom properties for consistent theming</li>
          <li><strong>Error Handling:</strong> Use the <code>onError</code> prop to handle rendering errors gracefully</li>
          <li><strong>Configuration:</strong> Start with presets and customize as needed</li>
          <li><strong>Accessibility:</strong> The component includes proper ARIA labels and semantic HTML</li>
        </ul>
      </section>
    </div>
  );
};

export default MarkdownExamples;
