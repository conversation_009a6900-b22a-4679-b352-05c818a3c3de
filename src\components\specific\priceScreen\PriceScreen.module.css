/* PriceScreen.module.css */
.priceScreen {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 16px;
  font-family: var(--font-family-primary);
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.backButtonContainer {
  position: absolute;
  top: 24px;
  left: 24px;
  z-index: 10;
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;
}

.backButtonContainer:hover {
  transform: translateX(-3px);
}

.backButtonLabel {
  margin-left: 10px;
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--color-primary);
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
}

.backButtonContainer:hover .backButtonLabel {
  opacity: 1;
  transform: translateX(0);
}

.heading {
  font-size: var(--font-size-4xl);
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center;
  padding: 40px;
  transition: color 0.3s ease;
}

.priceContainer {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.priceCard {
  width: 300px;
  padding: 24px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  background-color: var(--color-background-card);
  display: flex;
  flex-direction: column;
  transition:
    all 0.3s ease,
    background-color 0.3s ease,
    border-color 0.3s ease;
  color: var(--color-text-primary);
}

.priceCard:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.popular {
  border: 2px solid var(--color-primary-light);
  position: relative;
}

.popularBadge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(107.11deg, #c6abff 12.42%, #abd7ff 96.58%);
  color: #ffffff;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.planName {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text-primary);
  padding-bottom: 16px;
}

.price {
  font-size: 36px;
  font-weight: 700;
  color: var(--color-primary);
  padding-bottom: 24px;
}

.price span {
  font-size: 16px;
  font-weight: 400;
  color: var(--color-text-secondary);
}

.featureList {
  list-style-type: none;
  padding-bottom: 24px;
  flex-grow: 1;
}

.featureList li {
  font-size: 14px;
  color: #677480;
  padding-bottom: 8px;
  display: flex;
  align-items: center;
}

.featureList li::before {
  content: '✓';
  color: #c6abff;
  padding-right: 8px;
  font-weight: bold;
}

.selectButton {
  width: 100%;
  height: 48px;
  background: linear-gradient(107.11deg, #c6abff 12.42%, #abd7ff 96.58%);
  border: none;
  border-radius: 8px;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.selectButton:hover {
  opacity: 0.9;
}

/* TODO: Remove selected plan after  */
.selectedPlanDetails {
  margin-top: 32px;
  padding: 20px;
  display: flex;
  text-align: center;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #d1dfec;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.selectedPlanDetails h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1d192b;
}

.selectedPlanDetails ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.selectedPlanDetails p {
  font-size: 16px;
  font-weight: 400;
  color: #677480;
  padding: 10px;
  display: flex;
  align-items: center;
}

.selectedPlanDetails li {
  font-size: 14px;
  color: #677480;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.selectedPlanDetails li::before {
  content: '✓';
  color: #c6abff;
  padding: 8px;
  font-weight: bold;
}
