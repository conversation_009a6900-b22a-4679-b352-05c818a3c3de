import React, { createContext, useContext, useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Utility to store openDialog globally
let globalOpenDialog: (() => void) | null = null;

export const setGlobalOpenDialog = (fn: () => void) => {
  globalOpenDialog = fn;
};

export const getGlobalOpenDialog = () => globalOpenDialog;

interface DialogContextType {
  showDialog: boolean;
  openDialog: () => void;
  closeDialog: () => void;
  handleSessionExpiration: () => void;
}

const DialogContext = createContext<DialogContextType | undefined>(undefined);

export const DialogProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const navigate = useNavigate();
  const [showDialog, setShowDialog] = useState(false);

  const openDialog = () => setShowDialog(true);
  const closeDialog = () => setShowDialog(false);
  const handleSessionExpiration = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  // Register openDialog globally
  React.useEffect(() => {
    setGlobalOpenDialog(openDialog);
  }, []);

  return (
    <DialogContext.Provider
      value={{ showDialog, openDialog, closeDialog, handleSessionExpiration }}
    >
      {children}
    </DialogContext.Provider>
  );
};

export const useDialog = () => {
  const context = useContext(DialogContext);
  if (!context) {
    throw new Error('useDialog must be used within a DialogProvider');
  }
  return context;
};
