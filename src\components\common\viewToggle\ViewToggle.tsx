import React from 'react';
import { BsGrid, BsList } from 'react-icons/bs';
import styles from './ViewToggle.module.css';

export type ViewMode = 'grid' | 'list';

interface ViewToggleProps {
  viewMode: ViewMode;
  onViewChange: (mode: ViewMode) => void;
  className?: string;
}

const ViewToggle: React.FC<ViewToggleProps> = ({ 
  viewMode, 
  onViewChange,
  className = ''
}) => {
  return (
    <div className={`${styles.viewControls} ${className}`}>
      <div className={styles.viewToggle}>
        <button 
          className={`${styles.viewButton} ${viewMode === 'grid' ? styles.active : ''}`}
          onClick={() => onViewChange('grid')}
        >
          <BsGrid /> Grid
        </button>
        <button 
          className={`${styles.viewButton} ${viewMode === 'list' ? styles.active : ''}`}
          onClick={() => onViewChange('list')}
        >
          <BsList /> List
        </button>
      </div>
    </div>
  );
};

export default ViewToggle; 