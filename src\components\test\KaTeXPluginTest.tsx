import React, { useState } from 'react';
import ToastViewer from '../specific/sketchBookControler/components/ToastViewer';
import ToastEditor from '../specific/sketchBookControler/components/ToastEditor';

const KaTeXPluginTest: React.FC = () => {
  const [editorContent, setEditorContent] =
    useState(`# KaTeX Plugin Test for Toast UI Editor

## Test 1: Inline Math with $...$
The famous equation $E = mc^2$ should render as math.
Another example: $\\alpha + \\beta = \\gamma$

## Test 2: Display Math with $$...$$
$$\\int_0^1 f(x)dx = \\lim_{n \\to \\infty} \\sum_{i=1}^{n} f(x_i) \\Delta x$$

$$\\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$

## Test 3: Code Block with math language
\`\`\`math
\\sum_{i=1}^{n} x_i^2 = x_1^2 + x_2^2 + \\cdots + x_n^2
\`\`\`

## Test 4: Greek Letters
- Alpha: $\\alpha$
- Beta: $\\beta$
- Gamma: $\\gamma$
- Delta: $\\delta$
- Epsilon: $\\epsilon$

## Test 5: Complex Expressions
Matrix: $\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}$

Fraction: $\\frac{\\partial f}{\\partial x} = \\lim_{h \\to 0} \\frac{f(x+h) - f(x)}{h}$

## Test 6: Mixed Content
The cost is $100 (this should NOT render as math)
But this formula $x^2 + y^2 = z^2$ should render as math.
`);

  const testCases = [
    {
      title: 'Inline Math',
      content: 'Simple inline: $E = mc^2$ and $\\alpha + \\beta$',
    },
    {
      title: 'Display Math',
      content: '$$\\int_0^\\infty e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}$$',
    },
    {
      title: 'Code Block Math',
      content:
        '```math\n\\sum_{n=1}^\\infty \\frac{1}{n^2} = \\frac{\\pi^2}{6}\n```',
    },
    {
      title: 'Greek Letters',
      content:
        '$\\alpha$, $\\beta$, $\\gamma$, $\\delta$, $\\epsilon$, $\\zeta$',
    },
    {
      title: 'Currency vs Math',
      content: 'Price: $100, $200.50 vs Math: $x^2$ and $y = mx + b$',
    },
  ];

  const [selectedTest, setSelectedTest] = useState(0);

  return (
    <div style={{ padding: '20px', maxWidth: '1400px', margin: '0 auto' }}>
      <h1>Toast UI Editor KaTeX Plugin Test</h1>
      <p>This page tests your custom KaTeX plugin for Toast UI Editor.</p>

      {/* Quick Test Buttons */}
      <div style={{ marginBottom: '20px' }}>
        <h3>Quick Tests:</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          {testCases.map((test, index) => (
            <button
              key={index}
              onClick={() => setSelectedTest(index)}
              style={{
                padding: '8px 16px',
                border:
                  selectedTest === index
                    ? '2px solid #007bff'
                    : '1px solid #ccc',
                borderRadius: '4px',
                backgroundColor: selectedTest === index ? '#e7f3ff' : '#fff',
                cursor: 'pointer',
              }}
            >
              {test.title}
            </button>
          ))}
        </div>
      </div>

      {/* Selected Test Display */}
      <div
        style={{
          marginBottom: '30px',
          padding: '15px',
          border: '1px solid #ddd',
          borderRadius: '8px',
        }}
      >
        <h4>Test: {testCases[selectedTest].title}</h4>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '20px',
          }}
        >
          <div>
            <h5>Source:</h5>
            <pre
              style={{
                backgroundColor: '#f5f5f5',
                padding: '10px',
                borderRadius: '4px',
                fontSize: '12px',
              }}
            >
              {testCases[selectedTest].content}
            </pre>
          </div>
          <div>
            <h5>Rendered:</h5>
            <div
              style={{
                border: '1px solid #eee',
                padding: '10px',
                borderRadius: '4px',
                backgroundColor: '#fff',
              }}
            >
              <ToastViewer content={testCases[selectedTest].content} />
            </div>
          </div>
        </div>
      </div>

      {/* Full Editor Test */}
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '20px',
          minHeight: '600px',
        }}
      >
        <div>
          <h3>Editor (Edit the content):</h3>
          <ToastEditor
            value={editorContent}
            onChange={setEditorContent}
            height={500}
          />
        </div>

        <div>
          <h3>Viewer (Live Preview):</h3>
          <div
            style={{
              border: '1px solid #ddd',
              borderRadius: '8px',
              padding: '15px',
              backgroundColor: '#fff',
              height: '500px',
              overflow: 'auto',
            }}
          >
            <ToastViewer content={editorContent} />
          </div>
        </div>
      </div>

      {/* Debugging Info */}
      <div
        style={{
          marginTop: '30px',
          padding: '20px',
          backgroundColor: '#f8f9fa',
          borderRadius: '8px',
        }}
      >
        <h3>🔍 How to Check if Your Plugin is Working:</h3>
        <ol>
          <li>
            <strong>Visual Check:</strong> Math expressions should render as
            formatted equations, not plain text
          </li>
          <li>
            <strong>Console Check:</strong> Open browser DevTools (F12) and look
            for any KaTeX errors
          </li>
          <li>
            <strong>CSS Check:</strong> Verify KaTeX styles are loaded (math
            should have proper fonts)
          </li>
          <li>
            <strong>Plugin Check:</strong> Your plugin handles:
            <ul>
              <li>
                Inline math: <code>$...$</code>
              </li>
              <li>
                Display math: <code>$$...$$</code>
              </li>
              <li>
                Code blocks: <code>```math</code>
              </li>
            </ul>
          </li>
        </ol>

        <h4>✅ Expected Results:</h4>
        <ul>
          <li>
            Math expressions should render with proper mathematical typography
          </li>
          <li>Greek letters should display as symbols (α, β, γ)</li>
          <li>Fractions should display as proper fractions</li>
          <li>Currency amounts like $100 should remain as plain text</li>
        </ul>

        <h4>❌ If Not Working:</h4>
        <ul>
          <li>Check if KaTeX CSS is loaded properly</li>
          <li>Verify your plugin is correctly imported in ToastViewer</li>
          <li>Look for JavaScript errors in console</li>
          <li>
            Ensure KaTeX package is installed: <code>npm list katex</code>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default KaTeXPluginTest;
