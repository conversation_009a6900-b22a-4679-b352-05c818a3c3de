import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './Header.module.css';
import logo from '../../../assets/images/nuequiplogo.png';
import useLocalStorage from '../../../hooks/useLocalStorage';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store/store';
import DropdownButton from '../button/DropdownButton';
import { useProfileImage } from '../../../hooks/useProfileImage';
import { useLogoutMutation } from '../../../services/userMgtService';
import ThemeToggle from '../themeToggle/ThemeToggle';
import TourManager from '../TourManager';
import SimpleNetworkIndicator from '../SimpleNetworkIndicator';
import { IoNotifications } from 'react-icons/io5';
import NotificationPanel from '../../specific/notificationPanel/NotificationPanel';
import { selectUnreadCount } from '../../../store/slices/notificationSlice';

const Header = () => {
  const [user, setUser] = useLocalStorage('user', null);
  const { userDetails } = useSelector((state: RootState) => state.auth);
  const unreadCount = useSelector(selectUnreadCount);
  const { profileImage } = useProfileImage(user);
  const [logout] = useLogoutMutation();
  const [showDropdown, setShowDropdown] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      if (
        !target.closest(`.${styles.notificationIcon}`) &&
        !target.closest(`.${styles.notificationPanel}`)
      ) {
        setShowNotifications(false);
      }

      if (
        !target.closest(`.${styles.userImage}`) &&
        !target.closest(`.${styles.dropdownMenu}`)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const handleUserClick = () => setShowDropdown((prev) => !prev);

  const handleLogout = async () => {
    // setUser(null);
    const response: any = await logout();
    // console.log(response, 'hellele');
    if (response.data.success) {
      setUser(null);
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      navigate('/login');
    }
  };

  const handleEditProfileClick = () => {
    navigate('/edit-profile');
    setShowDropdown(false);
  };

  const handleDashbordClick = () => {
    navigate('/dashboard');
    setShowDropdown(false);
  };

  const handleNotificationClick = () => {
    setShowNotifications((prev) => !prev);
  };

  return (
    <div className={styles.header}>
      <div className={styles.left} onClick={() => navigate('/')}>
        <img src={logo} alt="Company Logo" className={styles.logo} />
      </div>
      <div className={styles.right}>
        <div className={styles.userContainer}>
          <div className={styles.creditsSection}>
            <button
              onClick={() => navigate('/price')}
              className={styles.getUnlimitedBtn}
            >
              <span className={styles.starIcon}>✦</span>
              Get unlimited AI
            </button>
            <div className={styles.credits}>
              <span className={styles.coinIcon}>💰</span>
              90 credits
            </div>
            <div className={styles.themeToggle}>
              <ThemeToggle />
            </div>
            <div className={styles.tourManager}>
              <TourManager />
            </div>
            <div className={styles.networkStatus}>
              <SimpleNetworkIndicator />
            </div>
            <div className={styles.notificationContainer}>
              <button
                className={styles.notificationIcon}
                onClick={handleNotificationClick}
                aria-label="Notifications"
              >
                <IoNotifications size={20} />
                {unreadCount > 0 && (
                  <span className={styles.notificationBadge}>
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </span>
                )}
              </button>
              {showNotifications && (
                <NotificationPanel
                  onClose={() => setShowNotifications(false)}
                />
              )}
            </div>
          </div>

          <div className={styles.user}>
            <img
              src={profileImage}
              alt={userDetails?.name || 'User'}
              className={styles.userImage}
              onClick={handleUserClick}
            />
            <span className={styles.userName}>{userDetails?.name}</span>
            {showDropdown && (
              <div className={styles.dropdownMenu}>
                {user?.role !== 'ROLE_USER' && (
                  <DropdownButton
                    style={{ marginBottom: '5px' }}
                    onClick={handleDashbordClick}
                    label="Dashboard"
                  />
                )}
                <DropdownButton
                  style={{ marginBottom: '5px' }}
                  onClick={handleEditProfileClick}
                  label="Edit Profile"
                />
                <DropdownButton
                  style={{ marginBottom: '5px' }}
                  onClick={handleLogout}
                  label="Logout"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
