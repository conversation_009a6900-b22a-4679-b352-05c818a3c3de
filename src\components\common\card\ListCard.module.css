.listCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--color-background-secondary);
  border-radius: 8px;
  cursor: pointer;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease;
  border: 1px solid var(--color-primary);
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  width: 100%;
  color: var(--color-text-primary);
}

.listCard:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.leftSection {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 2;
}

.initials {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-primary);
  transition: color 0.3s ease;
}

.middleSection {
  flex: 1;
}

.rightSection {
  display: flex;
  align-items: center;
  gap: 24px;
}

.avatarStack {
  display: flex;
  align-items: center;
}

.avatarWrapper {
  position: relative;
  margin-left: -8px;
}

.avatarWrapper:first-child {
  margin-left: 0;
}

.userAvatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid white;
  object-fit: cover;
}

.avatarHover {
  display: none;
  position: absolute;
  background: #333;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

.avatarWrapper:hover .avatarHover {
  display: block;
}

.extraUsers {
  background: #f5f5f5;
  color: #666;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border: 2px solid white;
  margin-left: -8px;
}

.timeInfo {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 14px;
}

.editIcon {
  color: #666;
}

.optionsWrapper {
  position: relative;
}

.optionsButton {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  color: #666;
  border-radius: 4px;
}

.optionsButton:hover {
  background: #f5f5f5;
}

.optionsMenu {
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 180px;
  z-index: 100;
  padding: 8px;
  animation: slideIn 0.2s ease-out;
}

.optionItem {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 10px 12px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #424242;
  font-size: 14px;
}

.optionItem:hover {
  background-color: #f5f5f5;
}

.optionIcon {
  font-size: 18px;
}

.deleteOption {
  color: #d32f2f;
}

.deleteOption:hover {
  background-color: #ffebee;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.optionsDivider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 8px 0;
}

.favButton {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  margin-left: 4px;
}

.favButton:hover {
  background: #f5f5f5;
  color: #4f46e5;
}

.starIcon {
  font-size: 18px;
}
