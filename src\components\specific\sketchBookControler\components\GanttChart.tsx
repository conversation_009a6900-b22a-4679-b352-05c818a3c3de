import { Gantt, Task, ViewMode } from 'gantt-task-react';
import 'gantt-task-react/dist/index.css';
import './GanttChart.css';
import { useMemo } from 'react';
import { useTheme } from '../../../../contexts/ThemeContext';

interface GanttChartProps {
  data: {
    tasks: Task[];
  };
  options?: {
    viewMode?: ViewMode;
    locale?: string;
    [key: string]: any;
  };
}

const GanttChart: React.FC<GanttChartProps> = ({ data, options }) => {
  const { isDarkMode } = useTheme();

  // Convert ISO strings to Date objects only for display
  const tasks = useMemo(() => {
    return data.tasks.map((task) => ({
      ...task,
      start: typeof task.start === 'string' ? new Date(task.start) : task.start,
      end: typeof task.end === 'string' ? new Date(task.end) : task.end,
      id: task.id || '',
      name: task.name || '',
      progress: task.progress || 0,
      type: task.type || 'task',
      project: task.project || undefined,
      hideChildren: task.hideChildren || false,
      displayOrder: task.displayOrder || 0,
    }));
  }, [data.tasks]);

  return (
    <div className="gantt-container" data-theme={isDarkMode ? 'dark' : 'light'}>
      <Gantt
        tasks={tasks}
        viewMode={options?.viewMode || ViewMode.Week}
        locale={options?.locale || 'en-GB'}
        listCellWidth={options?.listCellWidth || '155px'}
        columnWidth={options?.columnWidth || 60}
        barCornerRadius={options?.barCornerRadius || 3}
        fontSize={options?.fontSize || '12px'}
        rowHeight={options?.barHeight || 40}
        headerHeight={options?.headerHeight || 50}
        rtl={options?.rtl || false}
        TooltipContent={({ task }) => (
          <div className="gantt-tooltip">
            <p>
              <strong>{task.name}</strong>
            </p>
            <p>Start: {task.start.toLocaleDateString()}</p>
            <p>End: {task.end.toLocaleDateString()}</p>
            <p>Progress: {task.progress}%</p>
            {task.dependencies && task.dependencies.length > 0 && (
              <p>Dependencies: {task.dependencies.join(', ')}</p>
            )}
          </div>
        )}
      />
    </div>
  );
};

export default GanttChart;
