import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './baseQuery';
import {
  SAVE_WORKFLOW,
  GET_WORKFLOW_BY_CREATOR_AND_CURRENT_USER,
  GET_WORKFLOW_BY_CREATOR_AND_CURRENT_USER_PAGINATED,
  GET_WORKFLOW_BY_CREATOR,
  UPDATE_WORKFLOW_STATUS,
  GET_WORKFLOW_BY_PROJECT_ID,
  GET_WORKFLOW_BY_ID,
  GET_ALL_USERS,
  GET_ALL_USERS_PAGINATED,
} from './constants/workflowServicesConstants';

interface PaginationParams {
  page: number;
  size: number;
}

export const workflowServices = createApi({
  reducerPath: 'workflowServices',
  tagTypes: ['Workflows', 'Users'],
  baseQuery: baseQuery,
  endpoints: (builder) => ({
    saveWorkflow: builder.mutation({
      query: ({ payload }) => ({
        url: SAVE_WORKFLOW,
        method: 'POST',
        body: payload,
        headers: {
          'Content-Type': 'application/json',
        },
      }),
      invalidatesTags: ['Workflows'],
    }),
    getWorkflowByCreatorAndCurrentUser: builder.query({
      query: ({ creatorId }) =>
        GET_WORKFLOW_BY_CREATOR_AND_CURRENT_USER + creatorId,
      providesTags: ['Workflows'],
    }),
    getWorkflowByCreatorAndCurrentUserPaginated: builder.query({
      query: ({ creatorId, page, size }) =>
        `${GET_WORKFLOW_BY_CREATOR_AND_CURRENT_USER_PAGINATED}${creatorId}?page=${page}&size=${size}`,
      providesTags: ['Workflows'],
    }),
    getWorkflowByCreator: builder.query({
      query: ({ creatorId }) => GET_WORKFLOW_BY_CREATOR + creatorId,
      providesTags: ['Workflows'],
    }),
    getWorkflowById: builder.query({
      query: ({ workflowId }) => GET_WORKFLOW_BY_ID + workflowId,
      providesTags: ['Workflows'],
    }),
    updateWorkflow: builder.mutation({
      query: ({ payload, workflowId }) => ({
        url: UPDATE_WORKFLOW_STATUS + workflowId,
        method: 'PUT',
        body: payload,
      }),
      invalidatesTags: ['Workflows'],
    }),
    getWorkflowByProjectId: builder.query({
      query: ({ projectId }) => GET_WORKFLOW_BY_PROJECT_ID + projectId,
      providesTags: ['Workflows'],
    }),
    getAllUsers: builder.query({
      query: () => GET_ALL_USERS,
      providesTags: ['Users'],
    }),
    getAllUsersPaginated: builder.query<any, PaginationParams>({
      query: ({ page, size }) =>
        `${GET_ALL_USERS_PAGINATED}?page=${page}&size=${size}`,
      providesTags: ['Users'],
    }),
  }),
});

export const {
  useSaveWorkflowMutation,
  useGetWorkflowByCreatorAndCurrentUserQuery,
  useGetWorkflowByCreatorQuery,
  useGetWorkflowByIdQuery,
  useUpdateWorkflowMutation,
  useGetWorkflowByProjectIdQuery,
  useGetAllUsersQuery,
  useGetAllUsersPaginatedQuery,
  useGetWorkflowByCreatorAndCurrentUserPaginatedQuery,
} = workflowServices;
