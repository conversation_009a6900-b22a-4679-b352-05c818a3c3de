interface BaseFormData {
  userId: string;
  projectId: string;
  pageNumber: string;
  fileName: string;
  fileType: string;
  title?: string;
  prompt?: string;
  file?: File;
  response_type: 'Brief' | 'Elaborative' | string;
  visualization?: boolean;
  custom_prompt_name?: string;
  library_filename?: string;
}

export const createFormData = (data: BaseFormData): FormData => {
  const formData = new FormData();

  // Required fields
  formData.append('user_id', data.userId);
  formData.append('project_id', data.projectId);
  formData.append('page_number', data.pageNumber);
  formData.append('file_name', data.fileName);
  formData.append('file_type', data.fileType);
  formData.append('response_type', data.response_type);

  // Only append custom_prompt_name if it exists
  if (data.custom_prompt_name) {
    formData.append('custom_prompt_name', data.custom_prompt_name);
  }

  // Optional fields
  if (data.title) formData.append('title', data.title);
  if (data.prompt) formData.append('prompt', data.prompt);
  if (data.file) formData.append('file', data.file);
  if (data.library_filename)
    formData.append('library_filename', data.library_filename);

  // Always append visualization parameter if it exists in the data
  if (typeof data.visualization !== 'undefined') {
    formData.append('visualization', data.visualization.toString());
  }

  return formData;
};
