export const spacing = {
  0: '0',
  0.5: '0.125rem', // 2px
  1: '0.25rem', // 4px
  2: '0.5rem', // 8px
  3: '0.75rem', // 12px
  4: '1rem', // 16px
  5: '1.25rem', // 20px
  6: '1.5rem', // 24px
  8: '2rem', // 32px
  10: '2.5rem', // 40px
  12: '3rem', // 48px
  16: '4rem', // 64px
  20: '5rem', // 80px
  24: '6rem', // 96px
  32: '8rem', // 128px
} as const;

export const breakpoints = {
  xs: '0px',
  sm: '600px',
  md: '900px',
  tablet: '768px',
  tabletLg: '1024px',
  lg: '1200px',
  xl: '1536px',
} as const;

export const zIndex = {
  drawer: 1200,
  modal: 1300,
  snackbar: 1400,
  tooltip: 1500,
} as const;

export type Spacing = typeof spacing;
export type Breakpoints = typeof breakpoints;
export type ZIndex = typeof zIndex;
