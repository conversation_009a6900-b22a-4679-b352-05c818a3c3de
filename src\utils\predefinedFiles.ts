import { PredefinedFile } from '../types/predefinedFiles';

export const predefinedFiles: PredefinedFile[] = [
  {
    id: 'sample-business-report',
    name: 'Q3 Business Performance Report.pdf',
    description: 'Comprehensive quarterly business performance analysis with financial metrics, market trends, and strategic recommendations.',
    category: 'Business Reports',
    fileType: 'pdf',
    mimeType: 'application/pdf',
    size: '2.4 MB',
    pageCount: 45,
    lastModified: '3 days ago',
    tags: ['business', 'quarterly', 'performance', 'financial'],
    suggestedQuestions: [
      'What are the key performance indicators for Q3?',
      'Summarize the financial highlights',
      'What are the main strategic recommendations?',
      'How did we perform compared to Q2?'
    ],
    hasContext: true,
    contextDescription: 'AI model has full context of business metrics, financial data, and strategic analysis'
  },
  {
    id: 'sample-research-paper',
    name: 'AI in Healthcare Research Study.pdf',
    description: 'Academic research paper on artificial intelligence applications in healthcare, including case studies and statistical analysis.',
    category: 'Research Papers',
    fileType: 'pdf',
    mimeType: 'application/pdf',
    size: '1.8 MB',
    pageCount: 32,
    lastModified: '1 week ago',
    tags: ['research', 'AI', 'healthcare', 'academic'],
    suggestedQuestions: [
      'What are the main findings of this research?',
      'Summarize the methodology used',
      'What are the implications for healthcare?',
      'What future research directions are suggested?'
    ],
    hasContext: true,
    contextDescription: 'AI model has context of research methodology, findings, and healthcare applications'
  },
  {
    id: 'sample-financial-data',
    name: 'Annual Financial Analysis.xlsx',
    description: 'Detailed financial spreadsheet with revenue, expenses, profit margins, and trend analysis across multiple business units.',
    category: 'Financial Data',
    fileType: 'xlsx',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    size: '856 KB',
    pageCount: 12,
    lastModified: '5 days ago',
    tags: ['financial', 'analysis', 'revenue', 'expenses'],
    suggestedQuestions: [
      'What are the revenue trends by quarter?',
      'Which business unit is most profitable?',
      'Analyze the expense breakdown',
      'What are the key financial ratios?'
    ],
    hasContext: true,
    contextDescription: 'AI model has context of financial formulas, data relationships, and business metrics'
  },
  {
    id: 'sample-legal-contract',
    name: 'Software License Agreement.pdf',
    description: 'Standard software licensing agreement with terms, conditions, liability clauses, and compliance requirements.',
    category: 'Legal Documents',
    fileType: 'pdf',
    mimeType: 'application/pdf',
    size: '1.2 MB',
    pageCount: 28,
    lastModified: '2 weeks ago',
    tags: ['legal', 'contract', 'software', 'licensing'],
    suggestedQuestions: [
      'What are the key terms and conditions?',
      'Summarize the liability clauses',
      'What are the compliance requirements?',
      'What are the termination conditions?'
    ],
    hasContext: true,
    contextDescription: 'AI model has context of legal terminology, contract structure, and compliance requirements'
  },
  {
    id: 'sample-technical-manual',
    name: 'API Documentation Guide.pdf',
    description: 'Comprehensive technical documentation for REST API endpoints, authentication, and integration examples.',
    category: 'Technical Documentation',
    fileType: 'pdf',
    mimeType: 'application/pdf',
    size: '3.1 MB',
    pageCount: 67,
    lastModified: '4 days ago',
    tags: ['technical', 'API', 'documentation', 'integration'],
    suggestedQuestions: [
      'How do I authenticate with the API?',
      'What are the available endpoints?',
      'Show me integration examples',
      'What are the rate limits and restrictions?'
    ],
    hasContext: true,
    contextDescription: 'AI model has context of API structure, technical specifications, and code examples'
  },
  {
    id: 'sample-market-research',
    name: 'Market Analysis Report 2024.pdf',
    description: 'Market research report analyzing industry trends, competitor analysis, and consumer behavior patterns.',
    category: 'Market Research',
    fileType: 'pdf',
    mimeType: 'application/pdf',
    size: '2.7 MB',
    pageCount: 52,
    lastModified: '1 week ago',
    tags: ['market', 'research', 'trends', 'competitors'],
    suggestedQuestions: [
      'What are the key market trends?',
      'Who are the main competitors?',
      'What consumer behavior patterns were identified?',
      'What market opportunities exist?'
    ],
    hasContext: true,
    contextDescription: 'AI model has context of market data, competitive landscape, and trend analysis'
  },
  {
    id: 'sample-project-plan',
    name: 'Software Development Project Plan.docx',
    description: 'Detailed project plan for software development including timelines, milestones, resource allocation, and risk assessment.',
    category: 'Project Management',
    fileType: 'docx',
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    size: '945 KB',
    pageCount: 23,
    lastModified: '6 days ago',
    tags: ['project', 'planning', 'software', 'development'],
    suggestedQuestions: [
      'What are the key project milestones?',
      'Summarize the resource allocation',
      'What are the identified risks?',
      'What is the project timeline?'
    ],
    hasContext: true,
    contextDescription: 'AI model has context of project structure, timelines, and resource planning'
  },
  {
    id: 'sample-customer-data',
    name: 'Customer Satisfaction Survey.csv',
    description: 'Customer satisfaction survey results with ratings, feedback, and demographic data for analysis.',
    category: 'Customer Data',
    fileType: 'csv',
    mimeType: 'text/csv',
    size: '234 KB',
    pageCount: 1,
    lastModified: '2 days ago',
    tags: ['customer', 'satisfaction', 'survey', 'feedback'],
    suggestedQuestions: [
      'What is the overall satisfaction score?',
      'Which areas need improvement?',
      'Analyze satisfaction by demographics',
      'What are the common customer complaints?'
    ],
    hasContext: true,
    contextDescription: 'AI model has context of survey structure, rating scales, and customer feedback patterns'
  }
];

export const predefinedFileCategories = [
  'Business Reports',
  'Research Papers', 
  'Financial Data',
  'Legal Documents',
  'Technical Documentation',
  'Market Research',
  'Project Management',
  'Customer Data'
];
