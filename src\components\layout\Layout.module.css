.layoutContainer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-background-primary);
  overflow: hidden;
  transition: background-color 0.3s ease;
}

.headerContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1001;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.sidebarContainer {
  display: flex;
  height: calc(100vh - 60px);
  margin-top: 60px;
  position: relative;
}

.sidebar {
  position: fixed;
  left: 0;
  height: calc(100vh - 60px);
  z-index: 999;
}

.childrenContainer {
  margin-left: var(--sidebar-width, 280px);
  width: calc(100% - var(--sidebar-width, 280px));
  min-height: calc(100vh - 60px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: var(--color-background-primary);
}

.contentCollapsed {
  margin-left: var(--collapsed-sidebar-width, 60px);
  width: calc(100% - var(--collapsed-sidebar-width, 60px));
}

.scrollableChildren {
  overflow-y: auto;
  height: 100%;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-grey-300);
    border-radius: var(--radius-full);
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--color-grey-400);
  }
}
