.viewControls {
  display: flex;
  align-items: center;
}

.viewToggle {
  display: flex;
  gap: 8px;
  background-color: #f5f5f5;
  padding: 4px;
  border-radius: 8px;
}

.viewButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  background: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #000;
}

.viewButton:hover {
  background-color: #e0e0e0;
}

.viewButton.active {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: blue;
} 