import React from 'react';
import styles from './CircularLoader.module.css';

type LoaderSize = 'small' | 'medium' | 'large';

interface CircularLoaderProps {
  size?: LoaderSize;
  text?: string; // Optional text
  color?: 'primary' | 'warning' | 'error'; // Optional color
}

const CircularLoader: React.FC<CircularLoaderProps> = ({
  size = 'medium',
  text,
  color,
}) => {
  const loaderSizeClass = styles[size];

  // Generate color class if color is provided
  const colorClass = color
    ? styles[`color${color.charAt(0).toUpperCase() + color.slice(1)}`]
    : '';

  return (
    <div className={styles.loaderContainer}>
      <div className={styles.loaderWrapper}>
        {/* Horizontal rotating loader */}
        <div
          className={`${styles.loader} ${styles.loaderHorizontal} ${loaderSizeClass} ${colorClass}`}
        ></div>
        {/* Vertical rotating loader */}
        <div
          className={`${styles.loader} ${styles.loaderVertical} ${loaderSizeClass} ${colorClass}`}
        ></div>
      </div>

      {/* Conditionally render text if provided */}
      {text && <div className={styles.loaderText}>{text}</div>}
    </div>
  );
};

export default CircularLoader;
