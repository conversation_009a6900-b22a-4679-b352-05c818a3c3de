.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--color-background);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--color-border);
}

.title {
  color: var(--color-text-primary);
  margin-bottom: 1rem;
  font-weight: 600;
}

.planSummary {
  background: var(--color-primary-light);
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.planSummary h6 {
  color: var(--color-primary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.price {
  color: var(--color-primary);
  font-weight: 700;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
}

.interval {
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--color-text-secondary);
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sectionTitle {
  color: var(--color-text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--color-border-light);
}

.field {
  margin-bottom: 1rem;
}

.cardElementContainer {
  position: relative;
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  background: var(--color-background-secondary);
  transition:
    border-color 0.2s ease,
    background-color 0.2s ease;
}

.cardElementContainer:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
}

.cardElement {
  padding: 0.5rem 0;
}

.cardError {
  color: var(--color-error);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.alert {
  margin: 1rem 0;
}

.actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  justify-content: space-between;
}

.resetButton {
  flex: 1;
  padding: 0.75rem 1.5rem;
  border-color: var(--color-border);
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
}

.resetButton:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: rgba(var(--color-primary-rgb), 0.05);
}

.submitButton {
  flex: 2;
  padding: 0.75rem 1.5rem;
  background: var(--color-primary);
  color: white;
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;
}

.submitButton:hover:not(:disabled) {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
}

.submitButton:disabled {
  background: var(--color-disabled);
  color: var(--color-text-disabled);
  cursor: not-allowed;
}

.spinner {
  margin-right: 0.5rem;
  color: inherit;
}

.securityNotice {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  margin-top: 1rem;
  padding: 0.75rem;
  background: var(--color-success-light);
  border-radius: 6px;
  border: 1px solid var(--color-success-border);
}

/* Dark mode support */
[data-theme='dark'] .container {
  background: var(--color-background-secondary);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme='dark'] .planSummary {
  background: rgba(77, 143, 209, 0.1);
  border: 1px solid rgba(77, 143, 209, 0.2);
}

[data-theme='dark'] .cardElementContainer {
  background: var(--color-background-secondary);
  border-color: var(--color-border);
}

[data-theme='dark'] .cardElementContainer:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(77, 143, 209, 0.2);
}

[data-theme='dark'] .securityNotice {
  background: rgba(102, 187, 106, 0.1);
  border-color: rgba(102, 187, 106, 0.2);
  color: var(--color-text-primary);
}

[data-theme='dark'] .resetButton {
  border-color: var(--color-border);
  color: var(--color-text-secondary);
}

[data-theme='dark'] .resetButton:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: rgba(77, 143, 209, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    margin: 1rem;
    padding: 1.5rem;
  }

  .actions {
    flex-direction: column;
  }

  .resetButton,
  .submitButton {
    flex: none;
    width: 100%;
  }

  .price {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    margin: 0.5rem;
    padding: 1rem;
  }

  .header {
    margin-bottom: 1.5rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .form {
    gap: 1rem;
  }
}

/* Loading state */
.submitButton:disabled .spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Focus states for accessibility */
.cardElementContainer:focus-within,
.field:focus-within {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Error states */
.cardElementContainer.error {
  border-color: var(--color-error);
}

.field.error input {
  border-color: var(--color-error);
}

/* Success states */
.cardElementContainer.complete {
  border-color: var(--color-success);
}

/* Animation for form appearance */
.container {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
