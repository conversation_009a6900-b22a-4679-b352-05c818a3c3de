import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQueryAi } from './baseQuery';
import {
  GENERATE_COMPLIANCE_PDF,
  GENERATE_COMPLIANCE_REPORT,
  GET_COMPLIANCE_AS_MARKDOWN,
  GET_COMPLIANCE_PDF,
  GET_COMPLIANCE_REPORT,
  GET_FILES_FOR_COMPLIANCE,
} from './constants/complianceServiceConstants';

export const complianceService = createApi({
  reducerPath: 'complianceService',
  tagTypes: ['Files', 'ComplianceReports', 'PDFs'],
  baseQuery: baseQueryAi,
  endpoints: (builder) => ({
    getFilesForCompliance: builder.query<
      any,
      { userId: string; projectId: string }
    >({
      query: ({ userId, projectId }) => ({
        url: `${GET_FILES_FOR_COMPLIANCE}${userId}/${projectId}`,
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
      providesTags: ['Files'],
    }),
    generateComplianceReport: builder.mutation<any, any>({
      query: ({ formData }) => ({
        url: GENERATE_COMPLIANCE_REPORT,
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['Files', 'ComplianceReports'],
    }),
    generateCompliancePDF: builder.mutation<Blob, any>({
      query: ({ formData }) => ({
        url: GENERATE_COMPLIANCE_PDF,
        method: 'POST',
        body: formData,
        responseHandler: (response: any) => response.blob(),
      }),
    }),
    getPreviousComplianceReports: builder.query<any, any>({
      query: ({ userId, projectId }) => ({
        url: `${userId}/${projectId}${GET_COMPLIANCE_REPORT}`,
        method: 'GET',
      }),
      providesTags: ['ComplianceReports'],
      keepUnusedDataFor: 0,
    }),
    downloadPreviousComplianceReport: builder.mutation<any, any>({
      query: ({ userId, projectId }) => ({
        url: `${userId}/${projectId}${GET_COMPLIANCE_PDF}`,
        method: 'GET',
        responseHandler: (response: any) => response.blob(),
      }),
    }),
    getComplincesAsMakedown: builder.query<any, any>({
      query: ({ userId, projectId }) => ({
        url: `${userId}/${projectId}${GET_COMPLIANCE_AS_MARKDOWN}`,
        method: 'GET',
      }),
      providesTags: ['ComplianceReports'],
      keepUnusedDataFor: 0,
    }),
  }),
});

export const {
  useGetFilesForComplianceQuery,
  useGenerateComplianceReportMutation,
  useGenerateCompliancePDFMutation,
  useGetPreviousComplianceReportsQuery,
  useDownloadPreviousComplianceReportMutation,
  useGetComplincesAsMakedownQuery,
} = complianceService;
