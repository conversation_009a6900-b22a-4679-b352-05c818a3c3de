.graphCanvas {
  padding: 10px;
  width: 794px;
  height: 1123px;
  margin: 1px;
  position: relative;
  display: flex;
  background: #ffffff;
  flex-direction: column;
  float: right;
  font-family: 'Lato', sans-serif;
  color: #1d192b;
  overflow-x: hidden;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  gap: 10px; /* Reduce this value to decrease the gap */
}

.graphCanvas h2 {
  font-family: 'Lato', sans-serif;
  font-size: 30px;
  font-weight: 600;
  color: #292e33;
}

.graphCanvas p {
  font-family: 'Lato', sans-serif;
  font-size: 12.36px;
  font-weight: 400;
  line-height: 18.54px;
  color: #383e45;
  padding: 10px;
}

.graphContainer {
  display: flex;
  flex-direction: column;
  border: 1px solid #d1dfec;
  border-radius: 8px;
  background: #ffffff;
  overflow: hidden;
  margin: 0;
  padding: 0;
  transition: border 0.3s ease, box-shadow 0.3s ease;
}

.graphContainer.selected {
  border: 2px solid #174e86;
  box-shadow: 0 0 10px rgba(23, 78, 134, 0.5);
}

.graphHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  cursor: move;
  padding: 5px;
}

.graphTitle {
  font-weight: bold;
  font-size: 14px;
}

.graphControls {
  display: flex;
  gap: 5px;
}

.droppedGraph {
  width: 100%;
  height: 100%;
  padding: 0 10px;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  overflow: visible !important;
  margin: 0;
  padding: 5px;
}

.droppedGraph * {
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.droppedGraph:focus,
.droppedGraph:active {
  outline: none;
}

.dropTarget {
  color: #677480;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.frontGraph {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.graphContent {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.selectButton {
  color: #d1dfec;
  background: none;
  border: none; 
  cursor: pointer;
  padding: 0 20px;
  margin-top: 5px; 
  transition: background-color 0.3s ease;
}

.selectButton:hover {
  color: #174e86;
}

.selectButton.selected {
  color: #174e86;
}

.deleteButton {
  background: none;
  color: #e74c3c;
  border: none;
  cursor: pointer;
  padding: 0 20px;
  margin-top: 5px;
}

.deleteButton:hover {
  color: #f50000;
}

@media (max-width: 768px) {
  .graphCanvas {
    width: 100%;
    height: auto;
  }
}

.react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}


.dragHandle {
  cursor: move;
  color: #888;
}

.dataDisclaimer {
  margin: 0 0 5px 0;
}

.gridLayout {
  margin-top: 5px;
}
