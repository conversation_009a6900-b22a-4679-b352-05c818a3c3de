import { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { RootState } from '../../store/store';
import {
  addPage,
  removePage,
  setPages,
  setActivePage,
  updateCharts,
  updateLayout,
  setPageSize,
  resetSketchbook,
} from '../../store/sketchbookSlice';
import {
  useAddPageToSketchbookMutation,
  useRemovePageFromSketchbookMutation,
  useUpdateSketchbookPageSizeMutation,
} from '../../services/sketchbookServices';

import Header from '../../components/common/header/Header';
import BreadcrumbsComponent from '../../components/common/breadCrumb/BreadCrumbs';
import ChartPropertyController from '../../components/specific/sketchBookControler/ChartPropertyController';
import DropableEditor from '../../components/specific/sketchBookControler/DropableEditor';
import SketchBookController from '../../components/specific/sketchBookControler/SketchBookController';
import { StyledDrawer } from '../../components/common/drawer/StyledDrawer';
import { useDrawer } from '../../hooks/useDrawer';
import { useSketchbook } from '../../hooks/sketchbook';
import BottomControlBar from '../../components/specific/sketchBookControler/BottomControlBar';
import TourButton from '../../components/common/TourButton';

import styles from './SketchBookPage.module.css';

const SketchBookPage = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const params = useParams();

  // Get sketchbookId from URL params or location state
  const sketchbookIdFromParams = params.id;
  const sketchbookIdFromState = location.state?.sketchbookId;
  const sketchbookId = sketchbookIdFromParams || sketchbookIdFromState;

  // Reset sketchbook state when component mounts
  useEffect(() => {
    // Reset the Redux store to clear any previous sketchbook data
    dispatch(resetSketchbook());  
  }, [dispatch]);

  useEffect(() => {
    if (!sketchbookId) {
      toast.error('Sketchbook ID is missing.');
    }
  }, [sketchbookId]);

  const { pages, activePage, pageSize, charts, layouts, pageEnabled } =
    useSelector((state: RootState) => state.sketchbook);

  const {
    projectTitle,
    projectId,
    mongoIdOfInsightsForComplinces,
    sketchbookData,
  } = useSketchbook(sketchbookId);
  const {
    leftDrawerOpen,
    rightDrawerOpen,
    toggleLeftDrawer,
    toggleRightDrawer,
  } = useDrawer();

  const [selectedChart, setSelectedChart] = useState(null);
  const [dropableColor, setDropableColor] = useState('#f5f5f5');
  const [isAddingPage, setIsAddingPage] = useState(false);
  const [
    sketchbookIdFromCurrentSketchbook,
    setSketchbookIdFromCurrentSketchbook,
  ] = useState('');
  const [addPageToSketchbook] = useAddPageToSketchbookMutation();
  const [removePageFromSketchbook] = useRemovePageFromSketchbookMutation();
  const [updateSketchbookPageSize] = useUpdateSketchbookPageSizeMutation();
  // const { data: sketchbookData, error: sketchbookError } =
  //   useGetSketchbookByIdQuery({ sketchbookId }, { skip: !sketchbookId });

  // Initialize first page if no pages exist - but only if we've confirmed the backend has no pages
  // This prevents duplicate page creation on refresh
  useEffect(() => {
    // Skip if we're already adding a page or if pages already exist in Redux
    if (isAddingPage || (pages && pages.length > 0)) {
      return;
    }

    // Skip if we don't have sketchbook data yet
    if (!sketchbookData) {
      return;
    }

    // Skip if sketchbookData has pages
    if (sketchbookData.pages && sketchbookData.pages.length > 0) {
      return;
    }

    // Skip if we're already adding a page

    // Skip if pages is null but activePage is set to '01'
    // This indicates the backend has created a page but returned null in the response
    if (sketchbookData.pages === null && sketchbookData.activePage === '01') {
      console.log(
        'Sketchbook has activePage "01" but null pages - skipping page creation'
      );
      return;
    }

    // At this point, we know:
    // 1. We're not already adding a page
    // 2. There are no pages in Redux
    // 3. We have sketchbook data from the backend
    // 4. The backend data has no pages

    console.log(
      'No pages found in backend or Redux, initializing with default page'
    );
    setIsAddingPage(true);

    const initialPage = { id: '01', name: '01' };
    dispatch(setPages([initialPage]));
    dispatch(setActivePage('01'));

    if (sketchbookId) {
      console.log('Adding initial page to sketchbook:', sketchbookId);

      const payload = {
        id: '01',
        name: '01',
        custom_chart_ids: [],
        ai_chart_ids: [],
        ai_image_ids: [],
        ai_summary_ids: [],
        layouts: [],
      };

      toast.loading('Initializing sketchbook...', {
        id: 'init-sketchbook',
      });

      addPageToSketchbook({ payload, id: sketchbookId })
        .unwrap()
        .then((response) => {
          console.log('Initial page added successfully:', response);
          toast.dismiss('init-sketchbook');
          toast.success('Sketchbook initialized');

          // No need to reload - the useSketchbook hook will handle the data
        })
        .catch((error) => {
          console.error('Error adding initial page:', error);
          toast.dismiss('init-sketchbook');
          toast.error(`Failed to initialize sketchbook: ${error.message}`);

          if (error.status === 401 || error.status === 403) {
            window.location.href = '/login';
            return;
          }

          if (error.status === 500) {
            toast.error('Server error. Please try again later.');
            return;
          }
        })
        .finally(() => {
          setIsAddingPage(false);
        });
    } else {
      setIsAddingPage(false);
    }
  }, [
    dispatch,
    pages,
    sketchbookId,
    addPageToSketchbook,
    sketchbookData,
    isAddingPage,
  ]);

  const handleAddPage = useCallback(async () => {
    // Prevent multiple simultaneous page additions
    if (isAddingPage) {
      console.log('Already adding a page, ignoring duplicate request');
      return;
    }

    setIsAddingPage(true);

    try {
      // Ensure pages is an array before proceeding
      if (!Array.isArray(pages)) {
        console.error('Pages is not an array:', pages);
        toast.error('Cannot add page: Invalid page data');
        setIsAddingPage(false);
        return;
      }

      // Log existing pages for debugging
      console.log(
        'Current pages:',
        pages.map((p: any) => ({ id: p.id, name: p.name }))
      );

      // Get all existing page IDs from local state
      const existingPageIds = new Set<string>();
      pages.forEach((page: any) => {
        existingPageIds.add(page.id);
      });

      // Find the next available page number - using the original approach that worked
      const pageNumbers = Array.from(existingPageIds)
        .map((id) => parseInt(String(id), 10))
        .filter((num: number) => !isNaN(num))
        .sort((a: number, b: number) => a - b);

      let nextPageNumber = 1;
      for (const pageNum of pageNumbers) {
        if (pageNum !== nextPageNumber) {
          break;
        }
        nextPageNumber++;
      }

      console.log('Next page number calculated:', nextPageNumber);

      // Format the page number with leading zero
      const formattedPageNumber = nextPageNumber.toString().padStart(2, '0');

      // Check if this page already exists (to prevent duplicates)
      if (pages.some((page: any) => page.id === formattedPageNumber)) {
        console.log(
          `Page ${formattedPageNumber} already exists, skipping addition`
        );
        toast.error('This page already exists');
        setIsAddingPage(false);
        return;
      }

      // Special check for page '01' - make sure it doesn't exist in the backend
      if (
        formattedPageNumber === '01' &&
        sketchbookData &&
        sketchbookData.pages
      ) {
        const backendHasPage01 = sketchbookData.pages.some(
          (page: any) => page.id === '01'
        );
        if (backendHasPage01) {
          console.log(
            'Page 01 exists in backend but not in Redux, skipping addition'
          );
          // Update Redux to match backend
          dispatch(setPages([{ id: '01', name: '01' }, ...pages]));
          dispatch(setActivePage('01'));
          setIsAddingPage(false);
          return;
        }
      }

      const payload = {
        id: formattedPageNumber,
        name: formattedPageNumber,
        custom_chart_ids: [],
        ai_chart_ids: [],
        ai_image_ids: [],
        ai_summary_ids: [],
        layouts: [],
      };

      toast.loading('Adding new page...', { id: 'add-page' });
      console.log(
        `Attempting to add page ${formattedPageNumber} to sketchbook ${sketchbookId}`
      );

      // First update the Redux store to ensure UI consistency
      dispatch(addPage({ id: formattedPageNumber, name: formattedPageNumber }));

      // Then make the API call
      const response = await addPageToSketchbook({
        payload,
        id: sketchbookId,
      }).unwrap();

      console.log('API response for page addition:', response);

      if (response && response.success) {
        console.log(
          'API call successful, page added to database:',
          formattedPageNumber
        );
        toast.dismiss('add-page');
        toast.success('New page added');
      } else {
        // If API call fails, revert the Redux store change
        console.log('API call failed, reverting Redux store change');
        dispatch(removePage(formattedPageNumber));
        toast.dismiss('add-page');
        toast.error('Failed to add page: Server returned an error');
      }
    } catch (error) {
      console.error('Error adding page:', error);
      // If there's an error, try to revert the Redux store change
      try {
        // We can't access formattedPageNumber here, so we need to find the latest page
        const latestPage = [...pages].sort(
          (a: any, b: any) => parseInt(b.id, 10) - parseInt(a.id, 10)
        )[0];
        if (latestPage) {
          dispatch(removePage(latestPage.id));
        }
      } catch (e) {
        // Ignore errors in the cleanup
        console.error('Error during cleanup:', e);
      }
      toast.dismiss('add-page');
      toast.error('Failed to add page');
    } finally {
      // Always reset the flag when done
      setIsAddingPage(false);
    }
  }, [dispatch, pages, sketchbookId, addPageToSketchbook, isAddingPage]);

  const handleRemovePage = useCallback(
    async (id: string, index: number) => {
      if (index !== -1) {
        await removePageFromSketchbook({ id: sketchbookId, pageIndex: index });
        dispatch(removePage(id));
        toast.success('Page removed');
      } else {
        toast.error('Page not found');
      }
    },
    [dispatch, removePageFromSketchbook, sketchbookId]
  );

  const handleChartSelect = useCallback(
    (chartId: string) => {
      const selectedChart = charts[activePage]?.find(
        (chart: any) => chart.id === chartId
      );
      setSelectedChart(selectedChart || null);
    },
    [activePage, charts]
  );

  const handleChartUpdate = useCallback(
    (updatedChart: any) => {
      setSelectedChart(updatedChart);
      const updatedCharts = charts[activePage].map((chart: any) =>
        chart.id === updatedChart.id ? updatedChart : chart
      );
      dispatch(updateCharts({ pageId: activePage, charts: updatedCharts }));
    },
    [dispatch, activePage, charts]
  );

  const moveChart = useCallback(
    (chartId: string, targetPageId: string) => {
      if (activePage === targetPageId) {
        toast.error("Can't move chart to the same page");
        return;
      }

      const chartToMove = charts[activePage]?.find(
        (chart: any) => chart.id === chartId
      );
      if (!chartToMove) {
        toast.error('Chart not found');
        return;
      }

      const layoutToMove = layouts[activePage]?.find(
        (item: any) => item.i === chartId
      );
      const newSourceCharts =
        charts[activePage]?.filter((chart: any) => chart.id !== chartId) || [];
      const newTargetCharts = [...(charts[targetPageId] || []), chartToMove];

      dispatch(updateCharts({ pageId: activePage, charts: newSourceCharts }));
      dispatch(updateCharts({ pageId: targetPageId, charts: newTargetCharts }));

      if (layoutToMove) {
        const newSourceLayout =
          layouts[activePage]?.filter((item: any) => item.i !== chartId) || [];
        const newTargetLayout = [
          ...(layouts[targetPageId] || []),
          layoutToMove,
        ];

        dispatch(updateLayout({ pageId: activePage, layout: newSourceLayout }));
        dispatch(
          updateLayout({ pageId: targetPageId, layout: newTargetLayout })
        );
      }

      toast.success(`Chart moved to page ${targetPageId}`);
    },
    [activePage, charts, layouts, dispatch]
  );

  const customSelectStyles = {
    control: (provided: any) => ({
      ...provided,
      borderColor: '#d1dfec',
      boxShadow: 'none',
      '&:hover': {
        borderColor: '#b3c6d9',
      },
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected ? '#e0e0e0' : 'white',
      color: '#1d192b',
      '&:hover': {
        backgroundColor: '#f0f0f0',
      },
    }),
    singleValue: (provided: any) => ({
      ...provided,
      color: '#1d192b',
    }),
  };

  const pageSizes = [
    {
      value: 'a4',
      label: 'A4',
      width: 559,
      height: 793,
      orientation: 'portrait',
    },
    {
      value: 'a5',
      label: 'A5',
      width: 394,
      height: 559,
      orientation: 'portrait',
    },
    {
      value: 'letter',
      label: 'Letter',
      width: 612,
      height: 792,
      orientation: 'portrait',
    },
    {
      value: 'legal',
      label: 'Legal',
      width: 612,
      height: 1008,
      orientation: 'portrait',
    },
  ];

  const handlePageSizeChange = async (newSize: any) => {
    if (newSize) {
      try {
        const response = await updateSketchbookPageSize({
          payload: newSize,
          id: sketchbookId,
        });
        if (response.data.success) {
          dispatch(setPageSize(newSize));
          console.log('Page size updated successfully:', newSize);
        }
      } catch (error) {
        console.error('Error updating page size:', error);
      }
    }
  };

  const toggleOrientation = async () => {
    const newOrientation =
      pageSize.orientation === 'portrait' ? 'landscape' : 'portrait';
    const newSize = {
      ...pageSize,
      width: pageSize.height,
      height: pageSize.width,
      orientation: newOrientation,
    };
    try {
      const response = await updateSketchbookPageSize({
        payload: newSize,
        id: sketchbookId,
      });
      if (response.data.success) {
        dispatch(setPageSize(newSize));
      }
    } catch (error) {
      console.error('Error updating page size:', error);
    }
  };

  useEffect(() => {
    // Update the state when sketchbookData changes
    setSketchbookIdFromCurrentSketchbook(sketchbookData?.project_id);
  }, [sketchbookData]);

  return (
    <div className={styles.sketchbookcontainer}>
      <div id="sketchbook-header" className={styles.headerContainer}>
        <Header />
      </div>
      <div className={styles.breadcrumbsContainer}>
        <BreadcrumbsComponent
          projectId={projectId}
          sketchbookIdFromCurrentSketchbook={sketchbookIdFromCurrentSketchbook}
          mongoIdOfInsightsForComplinces={mongoIdOfInsightsForComplinces}
          projectTitle={projectTitle}
          sketchbookId={sketchbookId}
          onColorChange={setDropableColor}
          isFlowMode={!pageEnabled}
        />
      </div>

      <StyledDrawer
        open={leftDrawerOpen}
        onToggle={toggleLeftDrawer}
        anchor="left"
      >
        <SketchBookController sketchbookId={sketchbookId} />
      </StyledDrawer>

      <main
        className={`
          ${styles.mainContent}
          ${
            leftDrawerOpen && rightDrawerOpen && selectedChart
              ? styles.mainContentWithBothDrawers
              : leftDrawerOpen
                ? styles.mainContentWithLeftDrawer
                : rightDrawerOpen && selectedChart
                  ? styles.mainContentWithRightDrawer
                  : ''
          }`}
      >
        <DropableEditor
          sketchbookData={sketchbookData}
          sketchbookId={sketchbookId}
          chartData={charts[activePage] || []}
          activePage={activePage}
          pageSize={pageSize}
          key={`${activePage}-${pageSize?.value}-${pageSize?.orientation}`}
          onChartSelect={handleChartSelect}
          onMoveChart={moveChart}
          pages={pages}
          dropableColor={dropableColor}
        />
      </main>

      {selectedChart && (
        <StyledDrawer
          open={rightDrawerOpen}
          onToggle={toggleRightDrawer}
          anchor="right"
        >
          <ChartPropertyController
            sketchbookId={sketchbookId}
            selectedChart={selectedChart}
            activePage={activePage}
            onChartUpdate={handleChartUpdate}
          />
        </StyledDrawer>
      )}

      {pageEnabled && (
        <BottomControlBar
          pages={pages}
          activePage={activePage}
          setActivePage={(page: string) => dispatch(setActivePage(page))}
          addPage={handleAddPage}
          removePage={handleRemovePage}
          pageSize={pageSize}
          pageSizes={pageSizes}
          onPageSizeChange={handlePageSizeChange}
          toggleOrientation={toggleOrientation}
          customSelectStyles={customSelectStyles}
        />
      )}
    </div>
  );
};

export default SketchBookPage;
