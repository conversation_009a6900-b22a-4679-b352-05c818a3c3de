import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './EditProfile.module.css';
import useLocalStorage from '../../../hooks/useLocalStorage';
import CustomInput from '../../common/input/CustomInput';
import toast from 'react-hot-toast';
import ChangePassword from '../changePassword/ChangePassword';
import {
  useUpdateUserMutation,
  useUpdateUserProfileImageMutation,
} from '../../../services/userMgtService';
import { useDispatch, useSelector } from 'react-redux';
import { setAuth } from '../../../store/authSlice';
import { RootState } from '../../../store/store';
import { setSelectedImage } from '../../../store/imageSlice';
import { useProfileImage } from '../../../hooks/useProfileImage';

const EditProfile: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [user, setUser] = useLocalStorage('user', null);
  const [token] = useLocalStorage('token', null);

  const { profileImage, setProfileImage, getFullImageUrl } =
    useProfileImage(user);

  const [updateUser] = useUpdateUserMutation();
  const [updateProfileImage] = useUpdateUserProfileImageMutation();

  const [name, setName] = useState(user?.name || '');
  const [email, setEmail] = useState(user?.email || '');
  const [organization, setOrganization] = useState(user?.organization || '');
  const [newProfileImage, setNewProfileImage] = useState<File | null>(null);
  const [showChangePassword, setShowChangePassword] = useState(false);

  // Input Handlers
  const handleNameChange = (value: React.ChangeEvent<HTMLInputElement>) =>
    setName(value.target.value);
  const handleEmailChange = (value: React.ChangeEvent<HTMLInputElement>) =>
    setEmail(value.target.value);
  const handleOrganizationChange = (
    value: React.ChangeEvent<HTMLInputElement>
  ) => setOrganization(value.target.value);

  // Handle Image Change
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (!validateImageFile(file)) return;

      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImage(reader.result as string);
      };
      setNewProfileImage(file);
      reader.readAsDataURL(file);
    }
  };

  // Image validation
  const validateImageFile = (file: File) => {
    // const MAX_FILE_SIZE = 500 * 1024;
    // const MIN_FILE_SIZE = 50 * 1024;
    const allowedFormats = ['image/jpeg', 'image/jpg', 'image/png'];

    // if (file.size > MAX_FILE_SIZE || file.size < MIN_FILE_SIZE) {
    //   toast.error('File size should be between 50 KB and 500 KB.');
    //   return false;
    // }

    if (!allowedFormats.includes(file.type)) {
      toast.error('Please upload a .jpg, .jpeg, or .png file.');
      return false;
    }
    return true;
  };

  // Profile Image Update
  const handleProfileImageUpdate = async () => {
    if (!newProfileImage) return;

    try {
      const formData = new FormData();
      const fileExtension = newProfileImage.type.split('/')[1];
      const fileName = `profile_${user.id}.${fileExtension}`;
      formData.append('file', newProfileImage, fileName);

      const response: any = await updateProfileImage({
        userId: user.id,
        imageData: formData,
      });

      if (response.data.status === 200) {
        const updatedProfileImage = getFullImageUrl(response.data.data);
        dispatch(setSelectedImage(updatedProfileImage));
        updateUserData({ profileImg: updatedProfileImage });
      } else {
        throw new Error('Failed to upload profile image.');
      }
    } catch (error) {
      toast.error((error as Error).message);
    }
  };

  // Profile Data Update
  const handleProfileDataUpdate = async () => {
    if (name.trim().length < 3) {
      toast.error('Name must be at least 3 characters long.');
      return;
    }

    const userData = { name, email, organization };
    try {
      const response: any = await updateUser({ userId: user.id, userData });

      if (response.data.data) {
        const updatedUser = {
          ...user,
          ...response.data.data,
          profileImg: getFullImageUrl(response.data.data.profileImg),
          profileImgThumbnail: getFullImageUrl(
            response.data.data.profileImgThumbnail
          ),
        };

        updateUserData(response.data.data);
        setUser(updatedUser);
        toast.success('Profile updated successfully!');
        navigate('/');
      } else {
        throw new Error('Failed to update profile details.');
      }
    } catch (error) {
      toast.error((error as Error).message);
    }
  };

  // Update User Data in Local Storage and Redux
  const updateUserData = (updatedUserData: any) => {
    const updatedUser = {
      ...user,
      ...updatedUserData,
    };

    setUser(updatedUser);
    dispatch(setAuth({ token, userDetails: updatedUser }));
  };

  // Handle Form Submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await handleProfileImageUpdate();
    await handleProfileDataUpdate();
  };

  return (
    <div className={styles.editProfile}>
      <h2>Edit Profile</h2>
      <form onSubmit={handleSubmit}>
        <div className={styles.formGroup}>
          <CustomInput
            type="text"
            label="Name:"
            value={name}
            onChange={handleNameChange}
          />
        </div>
        <div className={styles.formGroup}>
          <CustomInput
            type="email"
            label="Email:"
            value={email}
            disabled
            onChange={handleEmailChange}
          />
        </div>
        <div className={styles.formGroup}>
          <CustomInput
            type="text"
            label="Organization:"
            value={organization}
            onChange={handleOrganizationChange}
          />
        </div>
        <div className={styles.formGroup}>
          <label htmlFor="profileImage" className={styles.customFileInputLabel}>
            <div className={styles.uploadImageContainer}>
              <span className={styles.customFileInputButton}>Upload Image</span>
              <input
                type="file"
                id="profileImage"
                accept="image/*"
                onChange={handleImageChange}
                className={styles.customFileInput}
              />
              <div className={styles.fileDetails}>
                <span className={styles.fileTypes}>
                  Accepted formats: .jpg, .jpeg, .png
                </span>
                <span className={styles.fileSize}>
                  Max file size: 50 - 500 KB
                </span>
              </div>
            </div>
          </label>
          <img
            src={profileImage}
            alt="Profile Preview"
            className={styles.profilePreview}
          />
        </div>
        <div className={styles.buttonsContainer}>
          <button type="submit" className={styles.saveButton}>
            Save Changes
          </button>
          <button
            type="button"
            className={styles.changePasswordButton}
            onClick={() => setShowChangePassword(true)}
          >
            Change Password
          </button>
        </div>
      </form>
      {showChangePassword && (
        <ChangePassword onClose={() => setShowChangePassword(false)} />
      )}
    </div>
  );
};

export default EditProfile;
