import React, { useState, useEffect } from 'react';
import styles from '../ChartPropertyController.module.css';
import { useUpdateCustomChartsMutation } from '../../../../services/sketchbookServices';
import { chartPayloadHandler } from '../utils/chartPayloadHandler';
import { toast } from 'react-hot-toast';
import ToastEditor from './ToastEditor';

interface TextControlsProps {
  selectedChart: any;
  onChartUpdate: (chart: any) => void;
  sketchbookId: string;
  activePage: string;
}

export const TextControls: React.FC<TextControlsProps> = ({
  selectedChart,
  onChartUpdate,
  sketchbookId,
  activePage,
}) => {
  const [updateCustomCharts] = useUpdateCustomChartsMutation();
  const [localMarkdown, setLocalMarkdown] = useState(
    selectedChart.data.markdown || ''
  );
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastSavedContent, setLastSavedContent] = useState(
    selectedChart.data.markdown || ''
  );
  // Toast UI Editor has built-in mode switching

  // Update localMarkdown when selectedChart changes
  useEffect(() => {
    // Only update if the chart ID has changed to avoid unnecessary updates
    const newMarkdown = selectedChart.data.markdown || '';
    setLocalMarkdown(newMarkdown);
    setLastSavedContent(newMarkdown);
  }, [selectedChart.id, selectedChart.data.markdown]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleMarkdownChange = (value: string | undefined) => {
    setLocalMarkdown(value || '');
    const updatedChart = {
      ...selectedChart,
      data: {
        ...selectedChart.data,
        markdown: value,
      },
    };
    onChartUpdate(updatedChart);
  };

  const handleMarkdownBlur = async () => {
    // Prevent multiple simultaneous updates and only save if content actually changed

    // console.log(isUpdating, localMarkdown, lastSavedContent);
    // if (isUpdating || localMarkdown === lastSavedContent) {
    //   console.log('Not updating - already updating or no change in content');
    //   return;
    // }
    setIsUpdating(true);

    try {
      const updatedChart = {
        ...selectedChart,
        data: {
          ...selectedChart.data,
          markdown: localMarkdown,
        },
      };

      const updatedChartPayload = chartPayloadHandler(
        updatedChart,
        'textarea',
        sketchbookId,
        activePage
      );

      const response = await updateCustomCharts({
        id: selectedChart.id,
        payload: updatedChartPayload,
        chartType: 'textarea',
      }).unwrap();

      if (response.success) {
        toast.success('Text updated successfully');
        setLastSavedContent(localMarkdown);
      } else {
        toast.error('Failed to update text');
      }

      onChartUpdate(updatedChart);
    } catch (error) {
      console.error('Error updating text:', error);
      toast.error('Failed to update text');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleSave = async () => {
    await handleMarkdownBlur();
    // toast.success('Text saved successfully');
  };

  // const handleStyleChange = async (property: string, value: string | number) => {
  //   const updatedChart = {
  //     ...selectedChart,
  //     data: {
  //       ...selectedChart.data,
  //       style: {
  //         ...selectedChart.data.style,
  //         [property]: value,
  //       },
  //     },
  //   };

  //   const updatedChartPayload = chartPayloadHandler(updatedChart, 'textarea', sketchbookId, activePage);

  //   await updateCustomCharts({
  //     id: selectedChart.id,
  //     payload: updatedChartPayload,
  //     chartType: 'textarea'
  //   }).unwrap();

  //   onChartUpdate(updatedChart);
  // };

  return (
    <div className={styles.section}>
      <div
        className={styles.heading}
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px',
        }}
      >
        Text Editor
        <button className={styles.saveButton} onClick={handleSave}>
          Save
        </button>
      </div>

      {/* Editor */}
      <ToastEditor
        key={selectedChart.id} // Force re-initialization when switching between text elements
        value={localMarkdown}
        onChange={handleMarkdownChange}
        // onBlur={handleMarkdownBlur} // Removed to prevent multiple saves, use Save button instead
        height={500}
        theme="light" // Always use light theme
        toolbarItems={[
          ['heading', 'bold', 'italic', 'strike'],
          ['hr', 'quote'],
          ['ul', 'ol', 'task', 'indent', 'outdent'],
          ['table'],
          ['scrollSync'],
        ]}
      />

      {/* Text Styling Controls */}
      {/* <div className={styles.styleControls}>
        <div className={styles.controlGroup}>
          <label>Font Size</label>
          <select
            value={selectedChart.data.style?.fontSize || '14px'}
            onChange={(e) => handleStyleChange('fontSize', e.target.value)}
          >
            {[
              '12px',
              '14px',
              '16px',
              '18px',
              '20px',
              '24px',
              '28px',
              '32px',
            ].map((size) => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
        </div>

        <div className={styles.controlGroup}>
          <label>Line Height</label>
          <select
            value={selectedChart.data.style?.lineHeight || '1.5'}
            onChange={(e) => handleStyleChange('lineHeight', e.target.value)}
          >
            {['1', '1.2', '1.5', '1.8', '2'].map((height) => (
              <option key={height} value={height}>
                {height}
              </option>
            ))}
          </select>
        </div>

        <div className={styles.controlGroup}>
          <label>Text Color</label>
          <div
            className={styles.colorPreview}
            style={{
              backgroundColor: selectedChart.data.style?.color || 'var(--color-text-primary)',
            }}
            onClick={() => setShowColorPicker(!showColorPicker)}
          />
          {showColorPicker && (
            <div className={styles.colorPickerPopover}>
              <HexColorPicker
                color={selectedChart.data.style?.color || 'var(--color-text-primary)'}
                onChange={(color) => handleStyleChange('color', color)}
              />
            </div>
          )}
        </div>

        <div className={styles.controlGroup}>
          <label>Text Align</label>
          <select
            value={selectedChart.data.style?.textAlign || 'left'}
            onChange={(e) => handleStyleChange('textAlign', e.target.value)}
          >
            {['left', 'center', 'right', 'justify'].map((align) => (
              <option key={align} value={align}>
                {align}
              </option>
            ))}
          </select>
        </div>

        <div className={styles.controlGroup}>
          <label>Font Weight</label>
          <select
            value={selectedChart.data.style?.fontWeight || 'normal'}
            onChange={(e) => handleStyleChange('fontWeight', e.target.value)}
          >
            {[
              'normal',
              'bold',
              '100',
              '200',
              '300',
              '400',
              '500',
              '600',
              '700',
              '800',
              '900',
            ].map((weight) => (
              <option key={weight} value={weight}>
                {weight}
              </option>
            ))}
          </select>
        </div>

        <div className={styles.controlGroup}>
          <label>Padding</label>
          <select
            value={selectedChart.data.style?.padding || '12px'}
            onChange={(e) => handleStyleChange('padding', e.target.value)}
          >
            {['8px', '12px', '16px', '20px', '24px', '32px'].map((padding) => (
              <option key={padding} value={padding}>
                {padding}
              </option>
            ))}
          </select>
        </div>
      </div> */}
    </div>
  );
};
