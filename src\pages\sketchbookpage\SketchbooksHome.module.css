.homeContainer {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 5px;
}
.projects {
  padding: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

.tutorialHeading {
  margin-top: 20px;
  font-size: 25px;
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  font-weight: 600;
}

.loaderStyles {
  margin: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.card {
  background: white;
  border-radius: 20px;
  padding: 1.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 10px 15px rgba(0, 0, 0, 0.1);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4299e1, #667eea);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover::before {
  opacity: 1;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.initialsWrapper {
  display: flex;
  align-items: center;
}

.initials {
  width: 52px;
  height: 52px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.25rem;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  padding-top: 1.25rem;
  border-top: 1px solid #edf2f7;
}

.timeInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #718096;
  font-size: 0.875rem;
}

.editIcon {
  font-size: 1.125rem;
  color: #718096;
}

.sketchbookIcon {
  width: 24px;
  height: 24px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.card:hover .sketchbookIcon {
  opacity: 1;
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .projects.grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    padding: 24px;
    max-width: 100%;
  }

  .headerSection {
    padding: 24px 32px;
    width: 95%;
  }

  .topRow {
    gap: 24px;
  }

  .searchContainer {
    min-width: 320px;
  }

  .createNewButton {
    height: 48px;
    padding: 12px 24px;
    font-size: 16px;
    min-width: 160px;
  }

  .card {
    padding: 20px;
    min-height: 240px;
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;
  }

  .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .title {
    font-size: 18px;
    line-height: 1.4;
  }

  .emptyState {
    padding: 60px 40px;
    margin: 32px;
  }

  .emptyState h2 {
    font-size: 24px;
    margin-bottom: 16px;
  }

  .emptyState p {
    font-size: 18px;
  }
}

/* Tablet portrait specific */
@media (min-width: 768px) and (max-width: 834px) and (orientation: portrait) {
  .projects.grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 20px;
  }

  .topRow {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .createNewButton {
    width: 100%;
    justify-content: center;
  }
}

/* Tablet landscape specific */
@media (min-width: 834px) and (max-width: 1024px) and (orientation: landscape) {
  .projects.grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 24px;
  }

  .headerSection {
    flex-direction: row;
    align-items: center;
  }

  .topRow {
    flex-direction: row;
    justify-content: space-between;
  }
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
  .projects {
    padding: 1rem;
    gap: 1rem;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .card {
    padding: 1.25rem;
    min-height: 200px;
  }

  .title {
    font-size: 1.125rem;
  }

  .createNewButton {
    height: 44px;
    font-size: 14px;
  }
}

.headerSection {
  display: flex;
  flex-direction: column;
  align-self: center;
  margin: auto;
  gap: 16px;
  padding: 20px 24px;
  width: 90%;
}

.topRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.searchContainer {
  /* min-width: 300px; */
  width: 100%;
  /* flex-shrink: 0; */
}

.projects {
  margin: auto;
  width: 90%;
  display: grid;
  transition: all 0.3s ease;
}

.projects.grid {
  grid-template-columns: repeat(4, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.projects.list {
  grid-template-columns: 1fr;
  gap: 12px;
  padding: 0 24px;
}

.loaderContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.emptyState {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  margin: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.emptyState h2 {
  margin-bottom: 12px;
  color: #333;
  font-size: 20px;
}

.emptyState p {
  color: #666;
  font-size: 16px;
}

.createNewButton {
  height: 40px;
  transition: all 0.2s ease;
  margin-left: 16px !important; /* Override MUI styles */
}

.createNewButton:hover {
  transform: translateY(-2px);
}

/* Remove any previous positioning styles that might interfere */
.topRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

@media (max-width: 768px) {
  .topRow {
    flex-direction: column;
    align-items: stretch;
  }

  .createNewButton {
    margin-left: 0 !important;
    margin-top: 8px;
  }
}

@media (max-width: 1200px) {
  .projects.grid {
    grid-template-columns: repeat(3, minmax(200px, 1fr));
    max-width: 900px;
  }
}

@media (max-width: 900px) {
  .projects.grid {
    grid-template-columns: repeat(2, minmax(200px, 1fr));
    max-width: 600px;
  }
}

@media (max-width: 600px) {
  .projects.grid {
    grid-template-columns: 1fr;
    max-width: 100%;
  }
}
