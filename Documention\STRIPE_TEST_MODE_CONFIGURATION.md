# Stripe Test Mode Configuration & Testing Guide

## Current Configuration Status

### ✅ **Frontend Configuration (Ready for Testing)**
The frontend is currently configured for **TEST MODE**:

```typescript
// src/services/config.ts
const DEV = true; // Currently set to true for testing

// This means the app will use:
export const BASE_URL = DEV ? DEV_BASE_URL : PRO_BASE_URL;
export const STRIPE_PUBLISHABLE_KEY = DEV ? STRIPE_PUBLISHABLE_KEY_TEST : STRIPE_PUBLISHABLE_KEY_LIVE;
```

### **Environment Variables Required**
Ensure these environment variables are set in your `.env` file:

```env
# Backend URLs
VITE_DEV_BASE_URL=http://**************:8182
VITE_PRO_BASE_URL=http://**************:8182

# Stripe Keys (TEST MODE)
VITE_STRIPE_PUBLISHABLE_KEY_TEST=pk_test_your_test_key_here
VITE_STRIPE_PUBLISHABLE_KEY_LIVE=pk_live_your_live_key_here
```

## Step 1: Frontend Test Configuration

### **Current Status**: ✅ Ready for Testing
- `DEV = true` means test mode is active
- Frontend will use `VITE_STRIPE_PUBLISHABLE_KEY_TEST`
- Backend URL points to your production server (which should use test secret keys)

### **To Verify Test Mode is Active**:
1. Check browser console for Stripe initialization
2. Look for test key format: `pk_test_...`
3. Stripe Elements should load normally

## Step 2: Backend Test Configuration

### **Required Backend Changes**:
Your backend at `http://**************:8182` needs to use **test secret keys**:

```javascript
// Backend should use:
const stripe = require('stripe')('sk_test_your_test_secret_key_here');
```

### **Backend Environment Variables**:
```env
# Backend .env file should have:
STRIPE_SECRET_KEY_TEST=sk_test_your_test_secret_key_here
STRIPE_SECRET_KEY_LIVE=sk_live_your_live_secret_key_here

# Use test key for testing:
STRIPE_SECRET_KEY=sk_test_your_test_secret_key_here
```

### **Webhook Configuration**:
- Configure Stripe webhooks for **test mode**
- Webhook URL: `http://**************:8182/api/v1/payments/webhook`
- Events to listen for: `payment_intent.succeeded`, `payment_intent.payment_failed`

## Step 3: Testing Plan

### **Phase 1: Basic Integration Test**
1. **Plan Selection**:
   - Go to pricing page
   - Select any paid plan
   - Verify navigation to payment page

2. **Payment Form Loading**:
   - Verify Stripe Elements load correctly
   - Check browser console for any errors
   - Confirm test key is being used

### **Phase 2: Test Card Transactions**

#### **Successful Payment Test**:
```
Card Number: ****************
Expiry: Any future date (e.g., 12/25)
CVC: Any 3 digits (e.g., 123)
ZIP: Any 5 digits (e.g., 12345)
```

#### **Declined Payment Test**:
```
Card Number: ****************
Expiry: Any future date
CVC: Any 3 digits
ZIP: Any 5 digits
```

#### **Insufficient Funds Test**:
```
Card Number: ****************
Expiry: Any future date
CVC: Any 3 digits
ZIP: Any 5 digits
```

### **Phase 3: End-to-End Flow Test**
1. **Complete Payment Flow**:
   - Fill payment form with test card
   - Submit payment
   - Verify payment intent creation
   - Check webhook delivery
   - Confirm user credit updates (if applicable)

2. **Error Handling Test**:
   - Test with declined card
   - Verify error messages display correctly
   - Ensure graceful failure handling

### **Phase 4: Backend Integration Verification**
1. **API Endpoint Tests**:
   - `POST /api/v1/payments/initiate` should return test payment intent
   - Response should have `clientSecret` starting with `pi_test_`
   - Payment history should record test transactions

2. **Webhook Tests**:
   - Monitor Stripe dashboard for webhook delivery
   - Check backend logs for webhook processing
   - Verify user data updates correctly

## Step 4: Production Deployment Process

### **Frontend Production Switch**:
```typescript
// In src/services/config.ts
const DEV = false; // Change from true to false for production
```

This will automatically switch to:
- `PRO_BASE_URL` for backend
- `STRIPE_PUBLISHABLE_KEY_LIVE` for Stripe

### **Backend Production Switch**:
```env
# Backend production environment
STRIPE_SECRET_KEY=sk_live_your_live_secret_key_here
```

### **Webhook Production Configuration**:
- Update webhook URL to production domain
- Configure live mode webhooks in Stripe dashboard
- Test webhook delivery in live mode

## Step 5: Key Management Coordination

### **Test Mode Coordination**:
✅ **Frontend**: Uses `pk_test_...` (when DEV = true)
✅ **Backend**: Uses `sk_test_...` (configured manually)
✅ **Webhooks**: Test mode webhooks configured

### **Production Mode Coordination**:
🔄 **Frontend**: Uses `pk_live_...` (when DEV = false)
🔄 **Backend**: Uses `sk_live_...` (configured manually)
🔄 **Webhooks**: Live mode webhooks configured

### **Deployment Checklist**:
- [ ] Frontend: Set `DEV = false`
- [ ] Backend: Switch to live secret key
- [ ] Webhooks: Configure for live mode
- [ ] Test with real card (small amount)
- [ ] Monitor for successful transactions
- [ ] Verify user credit updates work

## Expected Test Results

### **Successful Test Transaction**:
```json
{
  "clientSecret": "pi_test_1234567890_secret_abcdef",
  "paymentIntentId": "pi_test_1234567890",
  "subscriptionId": "test_subscription_id"
}
```

### **Stripe Dashboard (Test Mode)**:
- Payment intents will appear in test mode
- Webhooks will show delivery status
- No real money will be processed

### **Backend Behavior**:
- Test payment intents created successfully
- Webhooks received and processed
- User credits updated (depending on backend logic)
- Payment history records test transactions

## Troubleshooting

### **Common Issues**:
1. **Mixed Keys**: Ensure frontend test key matches backend test key
2. **Webhook Failures**: Check webhook URL and test mode configuration
3. **CORS Issues**: Verify backend allows requests from frontend domain
4. **Environment Variables**: Confirm all required env vars are set

### **Debug Steps**:
1. Check browser console for Stripe errors
2. Monitor network tab for API calls
3. Review Stripe dashboard logs
4. Check backend server logs
5. Verify webhook delivery in Stripe dashboard

## Summary

The frontend is **ready for testing** with Stripe test keys. The key steps are:

1. ✅ **Frontend**: Already configured for test mode (DEV = true)
2. 🔄 **Backend**: Needs test secret key configuration
3. 🔄 **Testing**: Use provided test card numbers
4. 🔄 **Production**: Switch DEV to false when ready

This setup allows safe testing of the complete payment flow without processing real transactions.
