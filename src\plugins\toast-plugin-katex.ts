// KaTeX integration for Toast UI Editor
// This file contains the KaTeX plugin implementation for rendering mathematical expressions
// in Toast UI Editor components.

import 'katex/dist/katex.min.css';

// Plugin function for Toast UI Editor (kept for compatibility)
export default function toastKatexPlugin() {
  return {
    // KaTeX processing is now handled directly in ToastViewer component
    // This plugin function is kept for backward compatibility
  };
}
