/**
 * Utility functions to test notification API integration
 * This file can be used for debugging and testing the notification system
 */

import { getCurrentUserId } from './auth/userHelpers';

/**
 * Test notification API endpoints
 */
export const testNotificationAPIs = async () => {
  const currentUserId = getCurrentUserId();
  
  if (!currentUserId) {
    console.error('No current user ID found. Please ensure user is logged in.');
    return false;
  }

  console.log('🧪 Testing Notification APIs...');
  console.log('Current User ID:', currentUserId);

  try {
    // Test 1: Create a test notification
    console.log('\n📝 Test 1: Creating test notification...');
    const createResponse = await fetch('/api/v1/notifications/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')?.replace(/"/g, '')}`,
      },
      body: JSON.stringify({
        workflowId: 'test-workflow-123',
        title: 'Test Notification',
        content: 'This is a test notification created by the API integration test.',
      }),
    });

    if (createResponse.ok) {
      const createData = await createResponse.json();
      console.log('✅ Create notification successful:', createData);
    } else {
      console.error('❌ Create notification failed:', createResponse.status, createResponse.statusText);
    }

    // Test 2: Fetch all notifications
    console.log('\n📋 Test 2: Fetching all notifications...');
    const fetchResponse = await fetch(`/api/v1/notifications/get-all/${currentUserId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.replace(/"/g, '')}`,
      },
    });

    if (fetchResponse.ok) {
      const fetchData = await fetchResponse.json();
      console.log('✅ Fetch notifications successful:', fetchData);
      
      // Test 3: Mark first notification as read (if any exist)
      if (fetchData.notifications && fetchData.notifications.length > 0) {
        const firstNotification = fetchData.notifications[0];
        console.log('\n✅ Test 3: Marking notification as read...');
        
        const markReadResponse = await fetch(
          `/api/v1/notifications/${firstNotification.id}/mark-as-read/${currentUserId}`,
          {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')?.replace(/"/g, '')}`,
            },
          }
        );

        if (markReadResponse.ok) {
          const markReadData = await markReadResponse.json();
          console.log('✅ Mark as read successful:', markReadData);
        } else {
          console.error('❌ Mark as read failed:', markReadResponse.status, markReadResponse.statusText);
        }
      } else {
        console.log('ℹ️ No notifications found to mark as read');
      }
    } else {
      console.error('❌ Fetch notifications failed:', fetchResponse.status, fetchResponse.statusText);
    }

    console.log('\n🎉 Notification API tests completed!');
    return true;

  } catch (error) {
    console.error('❌ Notification API test failed with error:', error);
    return false;
  }
};

/**
 * Test notification data transformation
 */
export const testNotificationTransformation = () => {
  console.log('🔄 Testing notification data transformation...');

  const mockApiResponse = {
    id: 'test-123',
    workflowId: 'workflow-456',
    title: 'Test Workflow',
    content: 'This is a test notification content',
    userId: 'user-789',
    createdAt: new Date().toISOString(),
    readAt: null,
    type: 'assignment' as const,
    priority: 'High' as const,
    actionRequired: true,
  };

  try {
    // Import the transformer function
    const { transformApiNotificationToWorkflowNotification } = require('./notificationTransformers');
    
    const transformed = transformApiNotificationToWorkflowNotification(mockApiResponse);
    
    console.log('Original API response:', mockApiResponse);
    console.log('Transformed notification:', transformed);
    
    // Validate transformation
    const isValid = 
      transformed.id === mockApiResponse.id &&
      transformed.workflowId === mockApiResponse.workflowId &&
      transformed.workflowTitle === mockApiResponse.title &&
      transformed.message === mockApiResponse.content &&
      transformed.read === false && // readAt is null, so read should be false
      transformed.type === mockApiResponse.type;

    if (isValid) {
      console.log('✅ Notification transformation test passed!');
      return true;
    } else {
      console.error('❌ Notification transformation test failed!');
      return false;
    }
  } catch (error) {
    console.error('❌ Transformation test error:', error);
    return false;
  }
};

/**
 * Validate notification API endpoints configuration
 */
export const validateNotificationEndpoints = () => {
  console.log('🔍 Validating notification API endpoints...');

  const expectedEndpoints = [
    '/api/v1/notifications/create',
    '/api/v1/notifications/get-all',
    '/api/v1/notifications',
  ];

  try {
    const { NOTIFICATION_ENDPOINTS } = require('../services/constants/notificationServiceConstants');
    
    console.log('Configured endpoints:', NOTIFICATION_ENDPOINTS);
    
    const hasAllEndpoints = 
      NOTIFICATION_ENDPOINTS.CREATE_NOTIFICATION &&
      NOTIFICATION_ENDPOINTS.GET_ALL_NOTIFICATIONS &&
      NOTIFICATION_ENDPOINTS.MARK_AS_READ;

    if (hasAllEndpoints) {
      console.log('✅ All notification endpoints are configured!');
      return true;
    } else {
      console.error('❌ Missing notification endpoint configurations!');
      return false;
    }
  } catch (error) {
    console.error('❌ Endpoint validation error:', error);
    return false;
  }
};

/**
 * Run all notification tests
 */
export const runAllNotificationTests = async () => {
  console.log('🚀 Running comprehensive notification system tests...\n');

  const results = {
    endpointValidation: validateNotificationEndpoints(),
    dataTransformation: testNotificationTransformation(),
    apiIntegration: await testNotificationAPIs(),
  };

  console.log('\n📊 Test Results Summary:');
  console.log('Endpoint Validation:', results.endpointValidation ? '✅ PASS' : '❌ FAIL');
  console.log('Data Transformation:', results.dataTransformation ? '✅ PASS' : '❌ FAIL');
  console.log('API Integration:', results.apiIntegration ? '✅ PASS' : '❌ FAIL');

  const allPassed = Object.values(results).every(result => result === true);
  console.log('\nOverall Result:', allPassed ? '🎉 ALL TESTS PASSED!' : '⚠️ SOME TESTS FAILED');

  return results;
};

// Export for console testing
if (typeof window !== 'undefined') {
  (window as any).testNotifications = {
    runAll: runAllNotificationTests,
    testAPIs: testNotificationAPIs,
    testTransformation: testNotificationTransformation,
    validateEndpoints: validateNotificationEndpoints,
  };
}
