export function getTimeAgo(timestamp: string | number): string {
  let date: number | string;

  // Handle ISO date string
  if (typeof timestamp === 'string' && timestamp.includes('T')) {
    date = Math.floor(new Date(timestamp).getTime() / 1000);
  } else {
    date = timestamp;
  }

  const now = Math.floor(Date.now() / 1000); // Current time in seconds
  const diff = now - date;

  if (diff < 60) return `${diff} seconds ago`;
  if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;
  if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;
  if (diff < 2592000) return `${Math.floor(diff / 86400)} days ago`;
  if (diff < 31536000) return `${Math.floor(diff / 2592000)} months ago`;
  return `${Math.floor(diff / 31536000)} years ago`;
}
export function formatTimestamp(timestamp: string | undefined) {
  if (!timestamp) return new Date().toLocaleString();

  try {
    const utcDate = new Date(timestamp);
    return utcDate.toLocaleString(undefined, {
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    console.error('Error formatting timestamp:', error);
    return new Date().toLocaleString();
  }
}
