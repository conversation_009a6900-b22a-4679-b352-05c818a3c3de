import React from 'react';
import styles from './IconButton.module.css';
import { Tooltip } from '@mui/material';

interface IconButtonProps {
  icon: React.ReactNode;
  onClick?: () => void;
  type?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  title?: string;
  tooltipPosition?: 'top' | 'right' | 'bottom' | 'left';
  disabled?: boolean;
  style?: React.CSSProperties;
}

const IconButton: React.FC<IconButtonProps> = ({
  icon,
  onClick,
  type = 'secondary',
  size = 'medium',
  title,
  tooltipPosition = 'top',
  disabled = false,
  style,
}) => {
  const button = (
    <button
      className={`${styles.iconButton} ${styles[type]} ${styles[size]}`}
      onClick={onClick}
      disabled={disabled}
      style={style}
      type="button"
    >
      <span className={styles.icon}>{icon}</span>
    </button>
  );

  if (title) {
    return (
      <Tooltip title={title} placement={tooltipPosition} color="primary">
        {button}
      </Tooltip>
    );
  }

  return button;
};

export default IconButton;
