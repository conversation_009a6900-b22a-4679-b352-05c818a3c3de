import React, { useState } from 'react';
import search from '../../../assets/images/svg/search.svg';
import styles from './Search.module.css';
interface SearchProps {
  placeholder?: string;
  onSearch?: (searchTerm: string) => void;
}

const Search: React.FC<SearchProps> = ({
  onSearch,
  placeholder = 'Search Here...',
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTerm = e.target.value;
    setSearchTerm(newTerm);
    onSearch && onSearch(newTerm);
  };

  return (
    <div className={styles.search}>
      <div className={styles.searchWrapper}>
        <img className={styles.searchIcon} src={search} alt="Search" />
        <input
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          placeholder={placeholder}
          className={styles.searchInput}
        />
      </div>
    </div>
  );
};

export default Search;
