import React from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  Typography,
  CircularProgress,
} from '@mui/material';
import ReactFlow, { Controls, ReactFlowProvider } from 'reactflow';
import { FaFileExport } from 'react-icons/fa';
import CustomButton from '../../button/CustomButton';
import { Node, Edge } from 'reactflow';
import ProcessNode from '../../../specific/sketchBookControler/components/nodes/ProcessNode';
import DecisionNode from '../../../specific/sketchBookControler/components/nodes/DecisionNode';
import OrgNode from '../../../specific/sketchBookControler/components/nodes/OrgNode';
import StartNode from '../../../specific/sketchBookControler/components/nodes/StartNode';
import EndNode from '../../../specific/sketchBookControler/components/nodes/EndNode';
import DatabaseNode from '../../../specific/sketchBookControler/components/nodes/DatabaseNode';
import DocumentNode from '../../../specific/sketchBookControler/components/nodes/DocumentNode';
import CloudNode from '../../../specific/sketchBookControler/components/nodes/CloudNode';
import OperationNode from '../../../specific/sketchBookControler/components/nodes/OperationNode';

const nodeTypes = {
  start: StartNode,
  end: EndNode,
  process: ProcessNode,
  decision: DecisionNode,
  database: DatabaseNode,
  document: DocumentNode,
  cloud: CloudNode,
  operation: OperationNode,
  organization: OrgNode,
};

interface FlowPreviewDialogProps {
  open: boolean;
  onClose: () => void;
  nodes: Node[];
  edges: Edge[];
  isExporting: boolean;
  onExport: () => void;
}

const FlowPreviewDialog: React.FC<FlowPreviewDialogProps> = ({
  open,
  onClose,
  nodes,
  edges,
  isExporting,
  onExport,
}) => {
  return (
    <Dialog
      open={open}
      onClose={() => !isExporting && onClose()}
      maxWidth={false}
      fullWidth
      PaperProps={{
        style: {
          width: '95vw',
          height: '95vh',
          maxWidth: '95vw',
          margin: '20px',
          backgroundColor: '#f8f9fa',
          borderRadius: '12px',
          overflow: 'hidden',
        },
      }}
    >
      <DialogTitle
        style={{
          borderBottom: '1px solid #e0e0e0',
          padding: '16px 24px',
          backgroundColor: '#ffffff',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <div>
            <Typography
              variant="h6"
              style={{
                color: '#174E86',
                fontWeight: 600,
                marginBottom: '4px',
              }}
            >
              Flow Chart Preview
            </Typography>
            <Typography variant="subtitle2" style={{ color: '#677480' }}>
              Review your flow chart before exporting
            </Typography>
          </div>
          <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
            {isExporting ? (
              <div
                style={{ display: 'flex', alignItems: 'center', gap: '12px' }}
              >
                <CircularProgress size={24} style={{ color: '#174E86' }} />
                <Typography style={{ color: '#174E86' }}>
                  Preparing PDF...
                </Typography>
              </div>
            ) : (
              <>
                <CustomButton
                  label="Cancel"
                  onClick={onClose}
                  type="secondary"
                  style={{
                    backgroundColor: '#f0f4f8',
                    color: '#174E86',
                  }}
                />
                <CustomButton
                  label="Export as PDF"
                  onClick={onExport}
                  type="secondary"
                  leftIcon={<FaFileExport size={20} />}
                  style={{
                    backgroundColor: '#174E86',
                    color: '#ffffff',
                  }}
                />
              </>
            )}
          </div>
        </div>
      </DialogTitle>
      <DialogContent
        style={{
          padding: '24px',
          backgroundColor: 'var(--color-background-tertiary)',
          height: 'calc(95vh - 100px)',
          display: 'flex',
          flexDirection: 'column',
          transition: 'background-color 0.3s ease',
        }}
      >
        <div
          style={{
            flex: 1,
            backgroundColor: 'var(--color-background-secondary)',
            borderRadius: '8px',
            padding: '20px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
            transition: 'background-color 0.3s ease',
          }}
        >
          <ReactFlowProvider>
            <div style={{ width: '100%', height: '100%' }}>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                nodeTypes={nodeTypes}
                fitView
                attributionPosition="bottom-left"
                minZoom={0.1}
                maxZoom={2}
                defaultViewport={{ x: 0, y: 0, zoom: 1 }}
                style={{
                  background: 'var(--color-background-secondary)',
                  transition: 'background-color 0.3s ease',
                }}
              >
                <Controls />
              </ReactFlow>
            </div>
          </ReactFlowProvider>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FlowPreviewDialog;
