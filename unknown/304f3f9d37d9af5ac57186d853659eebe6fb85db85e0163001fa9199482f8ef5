import React from 'react';
import { useDialog } from '../../contexts/DialogContext'; // Import the useDialog hook
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { CircularProgress, Box, Button } from '@mui/material';

const AuthExpiredDialog: React.FC = () => {
  const { showDialog, closeDialog, handleSessionExpiration } = useDialog();

  const handleClose = () => {
    closeDialog();
    handleSessionExpiration();
  };

  return (
    <Dialog
      open={showDialog}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 2,
        },
      }}
    >
      <DialogTitle
        sx={{
          textAlign: 'center',
          fontSize: '1.5rem',
          fontWeight: 600,
          color: 'error.main',
          pb: 1,
        }}
      >
        Your Session Has Expired
      </DialogTitle>
      <DialogContent>
        <DialogContentText
          sx={{
            textAlign: 'center',
            fontSize: '1.1rem',
            mb: 3,
          }}
        >
          To protect your account security, we've ended your session.
          <br />
          Don't worry! Simply log in again to continue where you left off.
        </DialogContentText>
        <Box display="flex" flexDirection="column" alignItems="center" gap={3}>
          <CircularProgress size={40} color="primary" />
          <Button
            onClick={handleClose}
            variant="contained"
            color="primary"
            sx={{
              minWidth: 200,
              borderRadius: 2,
              textTransform: 'none',
              fontSize: '1rem',
              py: 1,
            }}
          >
            Return to Login
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default AuthExpiredDialog;
