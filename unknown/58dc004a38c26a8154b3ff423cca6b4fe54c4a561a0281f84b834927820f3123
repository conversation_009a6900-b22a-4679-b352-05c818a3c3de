import { Handle, Position } from 'reactflow';
import styles from './Nodes.module.css';
import { useUpdateFlowNodeDataMutation } from '../../../../../services/sketchbookServices';

interface OrgNodeData {
  name: string;
  role: string;
  department: string;
  nodeId: string;
  index: number;
  sketchbookId: string;
  onOrgNodeChange: (nodeId: string, field: string, value: string) => void;
}

const OrgNode = ({ data }: { data: OrgNodeData }) => {
  const [updateFlowNodeData] = useUpdateFlowNodeDataMutation();

  const handleChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    data.onOrgNodeChange(data.nodeId, field, e.target.value);
  };

  const handleUpdateLabel = (field: string) => async (e: React.FocusEvent<HTMLInputElement>) => {
    try {

      console.log('data', data);
      // debugger;
      const payload = {
        data:data,
        nodeIndex: data.index,
      };
      const response: any = await updateFlowNodeData({ 
        payload, 
        id: data.sketchbookId 
      }).unwrap();
      console.log('response', response);
    } catch (error) {
      console.error(`Error updating ${field}:`, error);
    }
  };

  return (
    <div className={`${styles.node} ${styles.orgNode}`}>
      <Handle style={{zIndex: 1000}} type="target" position={Position.Top} />
      <div className={styles.content}>
        <input
          value={data.name || ''}
          onChange={handleChange('name')}
          onBlur={handleUpdateLabel('name')}
          className={styles.nodeInput}
          placeholder="Name"
          data-no-drag
        />
        <input
          value={data.role || ''}
          onChange={handleChange('role')}
          onBlur={handleUpdateLabel('role')}
          className={styles.nodeInput}
          placeholder="Role"
          data-no-drag
        />
        <input
          value={data.department || ''}
          onChange={handleChange('department')}
          onBlur={handleUpdateLabel('department')}
          className={styles.nodeInput}
          placeholder="Department"
          data-no-drag
        />
      </div>
      <Handle style={{zIndex: 1000}} type="source" position={Position.Bottom} />
    </div>
  );
};

export default OrgNode;
