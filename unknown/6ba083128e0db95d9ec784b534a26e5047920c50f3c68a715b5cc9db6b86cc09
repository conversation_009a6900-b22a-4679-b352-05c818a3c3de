import React, { useState, useRef, useEffect } from 'react';
import './InteractiveCanvas.css';

interface Position {
  x: number;
  y: number;
}

interface Item {
  id: number;
  type: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

interface InteractiveCanvasProps {
  initialItems?: Item[];
  onItemsChange?: (items: Item[]) => void;
}

const InteractiveCanvas: React.FC<InteractiveCanvasProps> = ({
  initialItems = [],
  onItemsChange,
}) => {
  const [scale, setScale] = useState<number>(0.6);
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 });
  const [items, setItems] = useState<Item[]>(
    initialItems.length > 0
      ? initialItems
      : [
          { id: 1, type: 'chart', x: 100, y: 100, width: 200, height: 150 },
          { id: 2, type: 'text', x: 400, y: 200, width: 150, height: 100 },
        ]
  );
  const [draggingItem, setDraggingItem] = useState<number | null>(null);
  const [resizingItem, setResizingItem] = useState<number | null>(null);
  const [dragStart, setDragStart] = useState<Position>({ x: 0, y: 0 });
  const [initialSize, setInitialSize] = useState<{
    width: number;
    height: number;
  }>({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    onItemsChange?.(items);
  }, [items, onItemsChange]);

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault(); // Prevent default behavior
    if (e.target === containerRef.current) {
      setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (draggingItem !== null) {
      const newItems = [...items];
      const item = newItems.find((i) => i.id === draggingItem);
      if (item) {
        item.x = e.clientX / scale - dragStart.x;
        item.y = e.clientY / scale - dragStart.y;
        setItems(newItems);
      }
    } else if (resizingItem !== null) {
      const newItems = [...items];
      const item = newItems.find((i) => i.id === resizingItem);
      if (item) {
        // Calculate the new width and height based on mouse position
        const deltaX = e.clientX / scale - (item.x + initialSize.width);
        const deltaY = e.clientY / scale - (item.y + initialSize.height);

        // Update the size with the delta, ensuring minimum size
        item.width = Math.max(50, initialSize.width + deltaX);
        item.height = Math.max(50, initialSize.height + deltaY);

        setItems(newItems);
      }
    } else if (e.buttons === 1 && e.target === containerRef.current) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  };

  const handleMouseUp = () => {
    setDraggingItem(null);
    setResizingItem(null);
  };

  const handleWheel = (e: React.WheelEvent<HTMLDivElement>) => {
    if (e.ctrlKey) {
      e.preventDefault();
      const delta = e.deltaY < 0 ? 0.1 : -0.1;
      setScale((prev) => Math.max(0.1, Math.min(5, prev + delta)));
    }
  };

  const startDragItem = (e: React.MouseEvent<HTMLDivElement>, id: number) => {
    e.stopPropagation();
    setDraggingItem(id);
    const item = items.find((i) => i.id === id);
    if (item) {
      setDragStart({
        x: e.clientX / scale - item.x,
        y: e.clientY / scale - item.y,
      });
    }
  };

  const startResizeItem = (e: React.MouseEvent<HTMLDivElement>, id: number) => {
    e.stopPropagation();
    setResizingItem(id);

    // Store the initial size when starting resize
    const item = items.find((i) => i.id === id);
    if (item) {
      setInitialSize({
        width: item.width,
        height: item.height,
      });
      // Set dragStart for resize reference point
      setDragStart({
        x: e.clientX / scale - item.x,
        y: e.clientY / scale - item.y,
      });
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const graphData = e.dataTransfer.getData('application/json'); // Get the JSON data
    const graph: Graph = JSON.parse(graphData); // Parse the JSON data

    const newItem: Item = {
      id: Date.now(), // Unique ID for the new item
      type: graph.name.toLowerCase(), // Use the graph name as the type
      x: e.clientX / scale - position.x, // Position based on drop location
      y: e.clientY / scale - position.y,
      width: 200, // Default width for the chart
      height: 150, // Default height for the chart
    };
    setItems((prevItems) => [...prevItems, newItem]); // Add new chart item to the state
  };

  useEffect(() => {
    // Add global mouse up handler
    const handleGlobalMouseUp = () => {
      setDraggingItem(null);
      setResizingItem(null);
    };

    // Add global mouse move handler
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (resizingItem !== null) {
        const newItems = [...items];
        const item = newItems.find((i) => i.id === resizingItem);
        if (item) {
          const deltaX = e.clientX / scale - (item.x + initialSize.width);
          const deltaY = e.clientY / scale - (item.y + initialSize.height);

          item.width = Math.max(50, initialSize.width + deltaX);
          item.height = Math.max(50, initialSize.height + deltaY);

          setItems(newItems);
        }
      }
    };

    window.addEventListener('mouseup', handleGlobalMouseUp);
    window.addEventListener('mousemove', handleGlobalMouseMove);

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);
      window.removeEventListener('mousemove', handleGlobalMouseMove);
    };
  }, [resizingItem, items, scale, initialSize]);

  return (
    <div
      ref={containerRef}
      className="container"
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onWheel={handleWheel}
      onDrop={handleDrop}
      onDragOver={(e) => e.preventDefault()}
    >
      <div
        className="canvas"
        style={{
          transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
          transformOrigin: '0 0',
        }}
      >
        {items.map((item) => (
          <div
            key={item.id}
            className="item"
            style={{
              left: `${item.x}px`,
              top: `${item.y}px`,
              width: `${item.width}px`,
              height: `${item.height}px`,
            }}
            onMouseDown={(e) => startDragItem(e, item.id)}
          >
            <div className="item-content">
              {item.type === 'chart' ? 'Chart Placeholder' : 'Text Placeholder'}
              {/* Render specific chart types based on item.type */}
              {item.type === 'bar' && <div>Bar Chart Placeholder</div>}
              {item.type === 'gauge' && <div>Gauge Chart Placeholder</div>}
              {item.type === 'pie' && <div>Pie Chart Placeholder</div>}
              {item.type === 'doughnut' && (
                <div>Doughnut Chart Placeholder</div>
              )}
              {item.type === 'line' && <div>Line Chart Placeholder</div>}
              {/* Add more chart types as needed */}
            </div>
            <div
              className="resize-handle"
              onMouseDown={(e) => startResizeItem(e, item.id)}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default InteractiveCanvas;
