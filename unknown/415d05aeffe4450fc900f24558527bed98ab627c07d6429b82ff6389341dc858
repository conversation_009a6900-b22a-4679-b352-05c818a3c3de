.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(1.5px);
}

.modal {
  background: var(--color-background-modal);
  padding: 20px;
  border-radius: var(--radius-lg);
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.formGroup {
  margin-bottom: 15px;
}

.formGroup label {
  display: block;
  margin-bottom: 5px;
}

.formGroup input {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.saveButton,
.cancelButton {
  background-color: transparent;
  margin-right: 10px;
  padding: 10px 20px;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.saveButton:hover,
.cancelButton:hover {
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
}
