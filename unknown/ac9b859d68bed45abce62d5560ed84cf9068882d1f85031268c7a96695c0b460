import React, { useState } from 'react';
import styles from './CustomSizeModal.module.css';

interface CustomSizeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (width: number, height: number) => void;
  currentWidth: number;
  currentHeight: number;
}

const CustomSizeModal: React.FC<CustomSizeModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentWidth,
  currentHeight,
}) => {
  const [width, setWidth] = useState(currentWidth);
  const [height, setHeight] = useState(currentHeight);

  if (!isOpen) return null;

  const handleSave = () => {
    onSave(width, height);
    onClose();
  };

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContent}>
        <h2>Custom Page Size</h2>
        <div className={styles.inputGroup}>
          <label htmlFor="width">Width (px):</label>
          <input
            type="number"
            id="width"
            value={width}
            onChange={(e) => setWidth(Number(e.target.value))}
            min="1"
          />
        </div>
        <div className={styles.inputGroup}>
          <label htmlFor="height">Height (px):</label>
          <input
            type="number"
            id="height"
            value={height}
            onChange={(e) => setHeight(Number(e.target.value))}
            min="1"
          />
        </div>
        <div className={styles.buttonGroup}>
          <button onClick={handleSave}>Save</button>
          <button onClick={onClose}>Cancel</button>
        </div>
      </div>
    </div>
  );
};

export default CustomSizeModal;
