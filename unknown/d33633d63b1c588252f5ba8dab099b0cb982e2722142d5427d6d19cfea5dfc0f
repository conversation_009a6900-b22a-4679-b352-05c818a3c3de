import React, { useState, useRef, useEffect } from 'react';
import styles from './ChartEditor.module.css';
import {
  Bar,
  Doughnut,
  Line,
  Pie,
  PolarArea,
  Radar,
  Bubble,
  Scatter,
} from 'react-chartjs-2';
import { Chart as ChartJS, registerables } from 'chart.js';
import { transformChartDataForSketchbookArray } from '../../../utils/transformChartForSketchbook';
import { FaCheckCircle, FaRegCircle } from 'react-icons/fa';

ChartJS.register(...registerables);

type GraphType =
  | 'Bar'
  | 'Gauge'
  | 'Pie'
  | 'Doughnut'
  | 'Line'
  | 'PolarArea'
  | 'Radar'
  | 'Bubble'
  | 'Scatter';

type Graph = {
  id: string;
  type: GraphType;
  data: any;
  options: any;
};

const generateRandomData = (count: number) => {
  return Array.from({ length: count }, () => Math.floor(Math.random() * 100));
};

const createGraphData = (type: GraphType): Graph => {
  const labels = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  const data = generateRandomData(12);

  const commonOptions = {
    responsive: true,
    maintainAspectRatio: false,
  };

  switch (type) {
    case 'Bar':
      return {
        id: `bar-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [
            {
              data,
              backgroundColor: [
                'rgba(197, 170, 255, 0.7)', // #C5AAFF
                'rgba(54, 162, 235, 0.7)', // #36A2EB
                'rgba(255, 206, 86, 0.7)', // #FFCE56
                'rgba(75, 192, 192, 0.7)', // #4BC0C0
                'rgba(153, 102, 255, 0.7)', // #9966FF
                'rgba(255, 4, 170, 0.7)', // #FF04AA
                'rgba(0, 255, 0, 0.7)', // #00FF00
                'rgba(0, 0, 255, 0.7)', // #0000FF
                'rgba(255, 204, 0, 0.7)', // #FFCC00
                'rgba(128, 128, 128, 0.7)', // #808080
                'rgba(192, 192, 192, 0.7)', // #C0C0C0
                'rgba(128, 0, 128, 0.7)', // #800080
              ],
              label: 'Sales',
            },
          ],
        },
        options: commonOptions,
      };
    case 'Line':
      return {
        id: `line-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [
            { data, borderColor: 'rgba(197, 170, 255)', label: 'Trend' },
          ],
        },
        options: commonOptions,
      };
    case 'Pie':
    case 'Doughnut':
      return {
        id: `${type.toLowerCase()}-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [
            {
              data,
              backgroundColor: [
                'rgba(197, 170, 255, 0.7)', // #C5AAFF
                'rgba(54, 162, 235, 0.7)', // #36A2EB
                'rgba(255, 206, 86, 0.7)', // #FFCE56
                'rgba(75, 192, 192, 0.7)', // #4BC0C0
                'rgba(153, 102, 255, 0.7)', // #9966FF
                'rgba(255, 4, 170, 0.7)', // #FF04AA
                'rgba(0, 255, 0, 0.7)', // #00FF00
                'rgba(0, 0, 255, 0.7)', // #0000FF
                'rgba(255, 204, 0, 0.7)', // #FFCC00
                'rgba(128, 128, 128, 0.7)', // #808080
                'rgba(192, 192, 192, 0.7)', // #C0C0C0
                'rgba(128, 0, 128, 0.7)', // #800080
              ],
            },
          ],
        },
        options: {
          commonOptions,
          plugins: {
            legend: { display: false },
          },
        },
      };
    case 'Gauge':
      return {
        id: `gauge-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [
            {
              data: Array.from({ length: 11 }, () => Math.random() * 100),
              backgroundColor: [
                'rgba(197, 170, 255, 0.7)', // #C5AAFF
                'rgba(54, 162, 235, 0.7)', // #36A2EB
                'rgba(255, 206, 86, 0.7)', // #FFCE56
                'rgba(75, 192, 192, 0.7)', // #4BC0C0
                'rgba(153, 102, 255, 0.7)', // #9966FF
                'rgba(255, 4, 170, 0.7)', // #FF04AA
                'rgba(0, 255, 0, 0.7)', // #00FF00
                'rgba(0, 0, 255, 0.7)', // #0000FF
                'rgba(255, 204, 0, 0.7)', // #FFCC00
                'rgba(128, 128, 128, 0.7)', // #808080
                'rgba(192, 192, 192, 0.7)', // #C0C0C0
                'rgba(128, 0, 128, 0.7)', // #800080
              ],
              circumference: 180,
              rotation: 270,
            },
          ],
        },
        options: {
          ...commonOptions,
          cutout: '75%',
          plugins: {
            // tooltip: { enabled: true },
            legend: { display: false },
          },
        },
      };
    case 'PolarArea':
      return {
        id: `area-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [
            {
              data,
              backgroundColor: [
                'rgba(197, 170, 255, 0.7)', // #C5AAFF
                'rgba(54, 162, 235, 0.7)', // #36A2EB
                'rgba(255, 206, 86, 0.7)', // #FFCE56
                'rgba(75, 192, 192, 0.7)', // #4BC0C0
                'rgba(153, 102, 255, 0.7)', // #9966FF
                'rgba(255, 4, 170, 0.7)', // #FF04AA
                'rgba(0, 255, 0, 0.7)', // #00FF00
                'rgba(0, 0, 255, 0.7)', // #0000FF
                'rgba(255, 204, 0, 0.7)', // #FFCC00
                'rgba(128, 128, 128, 0.7)', // #808080
                'rgba(192, 192, 192, 0.7)', // #C0C0C0
                'rgba(128, 0, 128, 0.7)', // #800080
              ],
            },
          ],
        },
        options: {
          commonOptions,
          plugins: {
            legend: { display: false },
          },
        },
      };
    case 'Radar':
      return {
        id: `radar-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [
            {
              data,
              backgroundColor: [
                'rgba(197, 170, 255, 0.7)', // #C5AAFF
                'rgba(54, 162, 235, 0.7)', // #36A2EB
                'rgba(255, 206, 86, 0.7)', // #FFCE56
                'rgba(75, 192, 192, 0.7)', // #4BC0C0
                'rgba(153, 102, 255, 0.7)', // #9966FF
                'rgba(255, 4, 170, 0.7)', // #FF04AA
                'rgba(0, 255, 0, 0.7)', // #00FF00
                'rgba(0, 0, 255, 0.7)', // #0000FF
                'rgba(255, 204, 0, 0.7)', // #FFCC00
                'rgba(128, 128, 128, 0.7)', // #808080
                'rgba(192, 192, 192, 0.7)', // #C0C0C0
                'rgba(128, 0, 128, 0.7)', // #800080
              ],
              label: 'Sales',
            },
          ],
        },
        options: commonOptions,
      };
    case 'Bubble':
      return {
        id: `bubble-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [
            {
              label: 'Bubble Data',
              data: data.map((d) => ({
                x: Math.random() * 100,
                y: Math.random() * 100,
                r: d / 10,
              })),
              backgroundColor: [
                'rgba(197, 170, 255, 0.7)', // #C5AAFF
                'rgba(54, 162, 235, 0.7)', // #36A2EB
                'rgba(255, 206, 86, 0.7)', // #FFCE56
                'rgba(75, 192, 192, 0.7)', // #4BC0C0
                'rgba(153, 102, 255, 0.7)', // #9966FF
                'rgba(255, 4, 170, 0.7)', // #FF04AA
                'rgba(0, 255, 0, 0.7)', // #00FF00
                'rgba(0, 0, 255, 0.7)', // #0000FF
                'rgba(255, 204, 0, 0.7)', // #FFCC00
                'rgba(128, 128, 128, 0.7)', // #808080
                'rgba(192, 192, 192, 0.7)', // #C0C0C0
                'rgba(128, 0, 128, 0.7)', // #800080
              ],
            },
          ],
        },
        options: commonOptions,
      };
    case 'Scatter':
      return {
        id: `scatter-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [
            {
              label: 'Scatter Data',
              data: data.map(() => ({
                x: Math.random() * 100,
                y: Math.random() * 100,
              })),
              backgroundColor: [
                'rgba(197, 170, 255, 0.7)', // #C5AAFF
                'rgba(54, 162, 235, 0.7)', // #36A2EB
                'rgba(255, 206, 86, 0.7)', // #FFCE56
                'rgba(75, 192, 192, 0.7)', // #4BC0C0
                'rgba(153, 102, 255, 0.7)', // #9966FF
                'rgba(255, 4, 170, 0.7)', // #FF04AA
                'rgba(0, 255, 0, 0.7)', // #00FF00
                'rgba(0, 0, 255, 0.7)', // #0000FF
                'rgba(255, 204, 0, 0.7)', // #FFCC00
                'rgba(128, 128, 128, 0.7)', // #808080
                'rgba(192, 192, 192, 0.7)', // #C0C0C0
                'rgba(128, 0, 128, 0.7)', // #800080
              ],
            },
          ],
        },
        options: commonOptions,
      };
    default:
      throw new Error(`Unsupported graph type: ${type}`);
  }
};

const ChartEditor = ({ chartData }: any) => {
  // console.log(chartData, 'kakaakkakaka');
  const transformed: any = transformChartDataForSketchbookArray(
    chartData.charts
  );

  useEffect(() => {
    const initialPositions: {
      [key: string]: { x: number; y: number; width: number; height: number };
    } = {};

    transformed.forEach((graph: Graph, index: number) => {
      initialPositions[graph.id] = {
        x: index * 50, // Assign some default x position, or retrieve if it exists in the props
        y: index * 50, // Assign some default y position
        width: 300, // Default width
        height: 200, // Default height
      };
    });

    setPositions(initialPositions);
    setDroppedGraphs(transformed);
  }, []);

  console.log(transformed, 'transformed charts');

  const [droppedGraphs, setDroppedGraphs] = useState<Graph[]>(
    transformed || []
  );
  const [positions, setPositions] = useState<{
    [key: string]: { x: number; y: number; width: number; height: number };
  }>({});

  const graphRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const canvasRef = useRef<HTMLDivElement>(null);
  const dragRef = useRef<{
    id: string | null;
    startX: number;
    startY: number;
    isResizing: boolean;
  }>({ id: null, startX: 0, startY: 0, isResizing: false });

  const [selectedGraphIds, setSelectedGraphIds] = useState<Set<string>>(
    new Set()
  );
  const [frontGraphId, setFrontGraphId] = useState<string | null>(null);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const graphType = e.dataTransfer.getData('text') as GraphType;
    const newGraph = createGraphData(graphType);
    const rect = canvasRef.current?.getBoundingClientRect();
    if (rect) {
      // Correctly calculate the position based on the canvas
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      setDroppedGraphs((prev) => [...prev, newGraph]);
      setPositions((prev) => ({
        ...prev,
        [newGraph.id]: {
          x: Math.max(x, 0), // Ensure x is not negative
          y: Math.max(y, 0), // Ensure y is not negative
          width: 300,
          height: 200,
        },
      }));
    }
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>, id: string) => {
    e.preventDefault();
    const rect = e.currentTarget.getBoundingClientRect();
    const isResizing =
      e.clientX > rect.right - 10 && e.clientY > rect.bottom - 10;

    dragRef.current = {
      id,
      startX: e.clientX,
      startY: e.clientY,
      isResizing,
    };
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (dragRef.current.id) {
      const { id, startX, startY, isResizing } = dragRef.current;
      const dx = e.clientX - startX;
      const dy = e.clientY - startY;

      setPositions((prev) => {
        const newPos = { ...prev[id] };
        if (isResizing) {
          newPos.width = Math.max(newPos.width + dx, 100);
          newPos.height = Math.max(newPos.height + dy, 100);
        } else {
          newPos.x += dx;
          newPos.y += dy;
        }
        return { ...prev, [id]: newPos };
      });

      dragRef.current.startX = e.clientX;
      dragRef.current.startY = e.clientY;
    }
  };

  const handleMouseUp = () => {
    dragRef.current = { id: null, startX: 0, startY: 0, isResizing: false };
  };

  const handleGraphClick = (
    e: React.MouseEvent<HTMLDivElement>,
    id: string
  ) => {
    e.stopPropagation();
    setFrontGraphId(id);
  };

  const handleCanvasClick = () => {
    setFrontGraphId(null);
  };

  const handleCheckboxClick = (e: React.MouseEvent<SVGElement>, id: string) => {
    e.stopPropagation();
    setSelectedGraphIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const renderGraph = (graph: Graph) => {
    switch (graph.type) {
      case 'Gauge':
        return <Doughnut data={graph.data} options={graph.options} />;
      case 'Bar':
        return <Bar data={graph.data} options={graph.options} />;
      case 'Line':
        return <Line data={graph.data} options={graph.options} />;
      case 'Pie':
        return <Pie data={graph.data} options={graph.options} />;
      case 'Doughnut':
        return <Doughnut data={graph.data} options={graph.options} />;
      case 'PolarArea':
        return <PolarArea data={graph.data} options={graph.options} />;
      case 'Radar':
        return <Radar data={graph.data} options={graph.options} />;
      case 'Bubble':
        return <Bubble data={graph.data} options={graph.options} />;
      case 'Scatter':
        return <Scatter data={graph.data} options={graph.options} />;
      default:
        return null; // Or you can show a fallback UI or error message
    }
  };

  useEffect(() => {
    graphRefs.current = droppedGraphs.reduce(
      (refs, graph) => {
        refs[graph.id] = null;
        return refs;
      },
      {} as { [key: string]: HTMLDivElement | null }
    );
  }, [droppedGraphs]);

  return (
    <div
      ref={canvasRef}
      className={styles.graphCanvas}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onClick={handleCanvasClick}
    >
      {droppedGraphs.length === 0 && (
        <div className={styles.dropTarget}>Drag & Drop Here</div>
      )}
      <div className={styles.droppedGraphs}>
        {droppedGraphs.map((graph) => (
          <div
            key={graph.id}
            ref={(el) => (graphRefs.current[graph.id] = el)}
            className={`${styles.droppedGraph} ${selectedGraphIds.has(graph.id) ? styles.selected : ''}`}
            style={{
              position: 'absolute',
              left: `${positions[graph.id]?.x || 0}px`,
              top: `${positions[graph.id]?.y || 0}px`,
              width: `${positions[graph.id]?.width || 300}px`,
              height: `${positions[graph.id]?.height || 200}px`,
              cursor: 'move',
              zIndex: frontGraphId === graph.id ? 2 : 1,
            }}
            onMouseDown={(e) => handleMouseDown(e, graph.id)}
            onClick={(e) => handleGraphClick(e, graph.id)}
          >
            {renderGraph(graph)}
            <div
              className={styles.resizeHandle}
              style={{
                zIndex: frontGraphId === graph.id ? 3 : 1,
              }}
            ></div>
            <div
              className={`${styles.checkbox} ${selectedGraphIds.has(graph.id) ? styles.checked : ''}`}
              onClick={(e) => e.stopPropagation()}
            >
              {' '}
              {selectedGraphIds.has(graph.id) ? (
                <FaCheckCircle
                  onClick={(e) => handleCheckboxClick(e, graph.id)}
                />
              ) : (
                <FaRegCircle
                  onClick={(e) => handleCheckboxClick(e, graph.id)}
                />
              )}
            </div>
          </div>
        ))}
      </div>
      {/* {droppedGraphs.length !== 0 && (
        <p>
          So the ratio graph is telling us that there is too much fluctuation in
          the revenue stream of our company
        </p>
      )} */}
    </div>
  );
};

export default ChartEditor;
