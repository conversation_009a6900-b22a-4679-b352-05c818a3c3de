export function parsePageNumbers(page_no: string) {
  const pages: number[] = [];

  // Split the string by commas
  page_no.split(',').forEach((part) => {
    if (part.includes('-')) {
      // Handle ranges (e.g., "1-4")
      const [start, end] = part.split('-').map(Number);
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    } else {
      // Handle individual numbers
      pages.push(Number(part));
    }
  });

  return pages;
}
