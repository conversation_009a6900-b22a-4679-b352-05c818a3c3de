import { useState } from 'react';
import styles from './WorkflowNotes.module.css';
import CustomButton from '../common/button/CustomButton';
import IconButton from '../common/button/IconButton';
import { IoTrash } from 'react-icons/io5';

interface WorkflowNotesProps {
  notes: string[];
  onAddNote: (note: string) => void;
  onDeleteNote: (index: number) => void;
}

const WorkflowNotes: React.FC<WorkflowNotesProps> = ({
  notes,
  onAddNote,
  onDeleteNote,
}) => {
  const [noteInput, setNoteInput] = useState('');

  const handleAddNote = () => {
    if (noteInput.trim()) {
      onAddNote(noteInput.trim());
      setNoteInput('');
    }
  };

  return (
    <div className={styles.notesSection}>
      <div className={styles.noteInputContainer}>
        <div className={styles.inputGroup}>
          <input
            type="text"
            placeholder="Add a note..."
            value={noteInput}
            onChange={(e) => setNoteInput(e.target.value)}
            className={styles.standardInput}
            onKeyPress={(e) => e.key === 'Enter' && handleAddNote()}
          />
        </div>
        <CustomButton
          type="secondary"
          label="Add Note"
          onClick={handleAddNote}
          style={{ width: '15%' }}
        />
      </div>

      <div className={styles.notesList}>
        {notes.map((note, index) => (
          <div key={index} className={styles.noteItem}>
            <span>{note}</span>
            <IconButton
              type="danger"
              icon={<IoTrash size={16} />}
              onClick={() => onDeleteNote(index)}
              size="small"
              title="Delete note"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default WorkflowNotes;
