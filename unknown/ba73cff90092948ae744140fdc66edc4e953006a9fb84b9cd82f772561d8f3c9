import React from 'react';
import {
  Dialog as Mui<PERSON><PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
} from '@mui/material';
import { styled } from '@mui/material/styles';

interface DialogProps {
  isDialogOpen: boolean;
  setIsDialogOpen: (isOpen: boolean) => void;
  pageRangeChanges: string;
  handlePageRangeChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSavePageRange: () => void;
  handleCloseDialog: () => void;
  style: any; // Keep for backward compatibility
  uploadFileWithinChat: File;
  handleFileUpload: (file: File) => void;
  isPageRangeDialogOpen: boolean;
  setIsPageRangeDialogOpen: (isOpen: boolean) => void;
}

// Styled components for consistent theming
const StyledDialog = styled(MuiDialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: '8px',
    backgroundColor: theme.palette.background.paper,
    boxShadow:
      theme.palette.mode === 'dark'
        ? '0 8px 32px rgba(0, 0, 0, 0.3)'
        : '0 8px 32px rgba(0, 0, 0, 0.08)',
  },
}));

const Dialog: React.FC<DialogProps> = ({
  isDialogOpen,
  pageRangeChanges,
  handlePageRangeChange,
  handleSavePageRange,
  handleCloseDialog,
  style, // Kept for backward compatibility
  uploadFileWithinChat,
  handleFileUpload,
  isPageRangeDialogOpen,
  setIsPageRangeDialogOpen,
}) => {
  return (
    <>
      {/* Page Range Dialog */}
      <StyledDialog
        open={isDialogOpen}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6">Change Referenced Page Numbers</Typography>
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            value={pageRangeChanges}
            onChange={handlePageRangeChange}
            placeholder="e.g. 1-5,7"
            margin="normal"
            variant="outlined"
            size="small"
          />
        </DialogContent>
        <DialogActions sx={{ padding: 2 }}>
          <Button
            variant="contained"
            onClick={handleSavePageRange}
            color="primary"
          >
            Save
          </Button>
          <Button
            variant="outlined"
            onClick={handleCloseDialog}
            color="primary"
          >
            Cancel
          </Button>
        </DialogActions>
      </StyledDialog>

      {/* Upload File Dialog */}
      <StyledDialog
        open={isPageRangeDialogOpen}
        onClose={() => setIsPageRangeDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6">Enter Page Numbers/Ranges</Typography>
          <Typography variant="caption" color="textSecondary">
            Format: 1, 3, 6-8, 10, 11-17
          </Typography>
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            value={pageRangeChanges}
            onChange={handlePageRangeChange}
            placeholder="e.g. 1-5,7"
            margin="normal"
            variant="outlined"
            size="small"
          />
        </DialogContent>
        <DialogActions sx={{ padding: 2 }}>
          <Button
            variant="contained"
            onClick={() => handleFileUpload(uploadFileWithinChat)}
            color="primary"
          >
            Upload File
          </Button>
          <Button
            variant="outlined"
            onClick={() => setIsPageRangeDialogOpen(false)}
            color="primary"
          >
            Cancel
          </Button>
        </DialogActions>
      </StyledDialog>
    </>
  );
};

export default Dialog;
