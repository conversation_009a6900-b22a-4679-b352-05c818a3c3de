import { useState } from 'react';
import { useUpdateWorkflowMutation } from '../services/workflowServices';
import { ActionType, Workflow } from '../types/workflow';
import { useNavigate } from 'react-router-dom';

export const useWorkflowActions = (workflow: Workflow) => {
  const [message, setMessage] = useState('');
  const [showMessageInput, setShowMessageInput] = useState(false);
  const [actionType, setActionType] = useState<ActionType>(null);
  const [updateWorkflow] = useUpdateWorkflowMutation();
  const navigate = useNavigate();

  const handleMessageSubmit = async () => {
    if (!message.trim()) return;

    try {
      const currentIndex = workflow.participants.findIndex(
        (p) => p.userId === workflow.currentUserId
      );

      if (currentIndex === -1)
        throw new Error('Current user not found in participants.');

      const participantData = {
        note: message,
        approval_message: actionType === 'approve' ? message : '',
        rejection_message: actionType === 'reject' ? message : '',
        conditional_approved_message:
          actionType === 'conditional' ? message : '',
        status:
          actionType === 'reject'
            ? 'rejected'
            : actionType === 'conditional'
              ? 'conditional_approved'
              : 'approved',
      };

      const nextParticipant = workflow.participants
        .slice(currentIndex + 1)
        .find((p) => p.status === '' && p.userId !== workflow.creatorId);

      const payload = {
        fullyApproved:
          (actionType === 'approve' || actionType === 'conditional') &&
          !nextParticipant,
        currentUserId:
          actionType === 'reject' ? null : nextParticipant?.userId || null,
        approvalStatus:
          actionType === 'reject'
            ? 'rejected'
            : !nextParticipant
              ? 'approved'
              : 'in-progress',
        status: actionType === 'reject' ? 'pending' : 'pending',
        participantIndex: currentIndex,
        participantData: participantData,
      };

      await updateWorkflow({
        payload,
        workflowId: workflow.id,
      }).unwrap();

      setMessage('');
      setShowMessageInput(false);
      setActionType(null);
      navigate(-1);
    } catch (error) {
      console.error('Error updating workflow:', error);
    }
  };

  const handleAction = (type: ActionType) => {
    setActionType(type);
    setShowMessageInput(true);
  };

  const cancelAction = () => {
    setShowMessageInput(false);
    setMessage('');
    setActionType(null);
  };

  return {
    message,
    setMessage,
    showMessageInput,
    actionType,
    handleMessageSubmit,
    handleAction,
    cancelAction,
  };
};

export default useWorkflowActions;
