import { PDFDocument } from 'pdf-lib';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';

export const countPagesInFile = async (file: File): Promise<number | null> => {
  const fileType = file.type;

  if (fileType === 'application/pdf') {
    return await countPdfPages(file);
  } else if (
    fileType ===
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ) {
    return await countWordPages(file);
  } else if (
    fileType ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    fileType === 'application/vnd.ms-excel' ||
    file.name.endsWith('.xlsx') ||
    file.name.endsWith('.xls')
  ) {
    return await countExcelSheets(file);
  }

  return null; // Return null for unsupported file types
};

const countPdfPages = async (file: File): Promise<number> => {
  const reader = new FileReader();

  return new Promise((resolve, reject) => {
    reader.onload = async () => {
      try {
        const typedArray = new Uint8Array(reader.result as ArrayBuffer);
        const pdfDoc = await PDFDocument.load(typedArray);
        resolve(pdfDoc.getPageCount());
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = () => {
      reject('Error reading PDF file');
    };

    reader.readAsArrayBuffer(file);
  });
};

const countWordPages = async (file: File): Promise<number> => {
  const reader = new FileReader();

  return new Promise((resolve, reject) => {
    reader.onload = async () => {
      try {
        const result = await mammoth.extractRawText({
          arrayBuffer: reader.result as ArrayBuffer,
        });
        const pageCount = Math.ceil(result.value.split('\n').length / 50); // Approximate page count
        resolve(pageCount);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = () => {
      reject('Error reading Word file');
    };

    reader.readAsArrayBuffer(file);
  });
};

const countExcelSheets = async (file: File): Promise<number> => {
  const reader = new FileReader();

  return new Promise((resolve, reject) => {
    reader.onload = async () => {
      try {
        const workbook = XLSX.read(reader.result, { type: 'array' });
        resolve(workbook.SheetNames.length);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = () => {
      reject('Error reading Excel file');
    };

    reader.readAsArrayBuffer(file);
  });
};
