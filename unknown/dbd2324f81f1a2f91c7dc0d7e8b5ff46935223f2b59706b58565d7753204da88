.gantt-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.gantt-container > div {
  height: 100% !important;
}

/* Custom styling for the Gantt chart */
.gantt-tooltip {
  background: var(--color-background-card);
  color: var(--color-text-primary);
  padding: 12px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  border-radius: 4px;
}

.gantt-tooltip p {
  margin: 4px 0;
}

/* Override default Gantt styles */
.gantt-container .bar-wrapper {
  stroke-width: 1px;
}

.gantt-container .bar-progress {
  stroke-width: 0;
}

.gantt-container .project {
  fill: #a3a3ff;
  stroke: #7676f4;
}

.gantt-container .milestone {
  fill: #f1c453;
  stroke: #d9ab3c;
}

.gantt-container .task {
  fill: #59b259;
  stroke: #46a146;
}

/* Dark theme support */
[data-theme='dark'] .gantt-container .calendar-header {
  background-color: var(--color-background-tertiary);
  color: var(--color-text-primary);
}

[data-theme='dark'] .gantt-container .calendar {
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
}

[data-theme='dark'] .gantt-container .lower-text,
[data-theme='dark'] .gantt-container .upper-text {
  fill: var(--color-text-primary);
}

[data-theme='dark'] .gantt-container .header-cell {
  stroke: var(--color-border);
}

[data-theme='dark'] .gantt-container .row-line {
  stroke: var(--color-border);
}

[data-theme='dark'] .gantt-container .grid-line {
  stroke: var(--color-border);
}

[data-theme='dark'] .gantt-container .calendar-row {
  stroke: var(--color-border);
}

[data-theme='dark'] .gantt-container .task-list-header {
  background-color: var(--color-background-tertiary);
  color: var(--color-text-primary);
}

[data-theme='dark'] .gantt-container .task-list {
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
}

[data-theme='dark'] .gantt-container .task-list-item {
  color: var(--color-text-primary);
  border-color: var(--color-border);
}

[data-theme='dark'] .gantt-container .task-name {
  color: var(--color-text-primary);
}
