.container {
  display: flex;
  width: calc(100% - 240px);
  padding: 20px 20px 10px 20px;
  position: fixed;
  bottom: 0;    
  left: 240px;  
  align-items: center;
  gap: 5px;
  justify-content: space-between;
  background: #FFFFFF;
  border: 1px solid #D1DFEC;
  z-index: 1000;
}


 .container img {
    height: 20px;
    width: 20px;
  }
  
  .pagination {
    display: flex;
    color: #677480;
    border: 1px solid #D1DFEC;
    border-radius: 8px;
    
  }
  
  .actions {
    display: flex;
    gap: 1rem;
    }

  .defaultIcon {
    background-color: none; 
    
  }
  
  .clickedIcon {
    background-color: #ffffff;
    border: none;
    border-radius: 50%;
  }

  .clickedIcon1 {
    background-color: #ffffff;
    border: none;
    border-radius: 4px;
  }


  