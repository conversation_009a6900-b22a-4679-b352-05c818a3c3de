@import './styles/theme.css';

:root {
  --sidebar-width: 280px;
  --collapsed-sidebar-width: 60px;
}

/* body {
  --sb-track-color: #b2ccff;
  --sb-thumb-color: #c1b2ff;
  --sb-size: 4px;
}

body::-webkit-scrollbar {
  width: var(--sb-size);
}

body::-webkit-scrollbar-track {
  background: var(--sb-track-color);
  border-radius: 2px;
}

body::-webkit-scrollbar-thumb {
  background: var(--sb-thumb-color);
  border-radius: 2px;
}

@supports not selector(::-webkit-scrollbar) {
  body {
    scrollbar-color: var(--sb-thumb-color) var(--sb-track-color);
  }
} */

body {
  --sb-track-color: var(--color-background-tertiary);
  --sb-thumb-color: var(--color-primary-light);
  --sb-size: 4px; /* Adjusted size for better visibility */
}

/* For WebKit browsers (Chrome, Safari, Edge) */
body::-webkit-scrollbar {
  width: var(--sb-size);
  height: var(--sb-size); /* Include this for horizontal scrollbars */
}

body::-webkit-scrollbar-track {
  /* background: var(--sb-track-color); */
  border-radius: 2px;
}

body::-webkit-scrollbar-thumb {
  /* background: var(--sb-thumb-color); */
  border-radius: 2px;
}

/* For Firefox */
body {
  scrollbar-width: thin;
  /* scrollbar-color: var(--sb-thumb-color) var(--sb-track-color); thumb and track */
}

/* Optional: To ensure consistency across other elements with scrollbars */
/* * {
  scrollbar-width: thin;
  scrollbar-color: #D1DFEC #ffffff;
} */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #ffffff;
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-light);
}

/* Add these styles to ensure React-Select dropdowns are properly displayed */
body .select__menu-portal {
  z-index: 9999 !important;
}

body .select__menu {
  z-index: 9999 !important;
}
