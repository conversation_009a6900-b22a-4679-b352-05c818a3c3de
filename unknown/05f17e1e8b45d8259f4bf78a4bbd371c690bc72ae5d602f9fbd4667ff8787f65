import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ProjectState {
  selectedProjectId: string | null;
  isProjectWindowOpen: boolean;
}

const initialState: ProjectState = {
  selectedProjectId: null,
  isProjectWindowOpen: false,
};

const projectSlice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    setProjectDataId: (state, action: PayloadAction<string>) => {
      state.selectedProjectId = action.payload;
    },
    setIsProjectWindowOpen: (state, action: PayloadAction<boolean>) => {
      state.isProjectWindowOpen = action.payload;
    },
  },
});

export const { setProjectDataId, setIsProjectWindowOpen } = projectSlice.actions;
export default projectSlice.reducer; 