// import { useEffect } from 'react';
// import { useNavigate } from 'react-router-dom';
// import axios from 'axios';
// import { useDialog } from '../contexts/DialogContext';

// export const useAuthInterceptor = () => {
//   const navigate = useNavigate();
//   const { openDialog } = useDialog();

//   useEffect(() => {
//     const interceptor = axios.interceptors.response.use(
//       (response) => response,
//       async (error) => {
//         if (error.response?.status === 401) {
//           openDialog();
//           localStorage.removeItem('token');

//           await new Promise((resolve) => setTimeout(resolve, 2000));
//           navigate('/login');
//         }
//         return Promise.reject(error);
//       }
//     );

//     return () => {
//       axios.interceptors.response.eject(interceptor);
//     };
//   }, [navigate, openDialog]);
// };
