// Sketchbook-related API endpoints
export const GET_SKETCHBOOK_BY_USER_ID = '/api/v1/sketchbook/get-by-userid/';
export const GET_SKETCHBOOK_BY_USER_ID_PAGINATED =
  '/api/v1/sketchbook/get-by-userid/';
export const GET_SKETCHBOOK_BY_ID = '/api/v1/sketchbook/get-by-id/';
export const SAVE_TO_SKETCHBOOK = '/api/v1/sketchbook/save';
export const UPDATE_SKETCHBOOK = '/api/v1/sketchbook/update/';
export const UPDATE_SKETCHBOOK_LAYOUT =
  '/api/v1/sketchbook/update/page-layout/';
export const UPDATE_SKETCHBOOK_AI_DATA =
  '/api/v1/sketchbook/update/page-layout-and-ai-data/';
export const SAVE_CUSTOM_CHARTS = '/api/v1/custom-charts/save/';
export const UPDATE_CUSTOM_CHARTS = '/api/v1/custom-charts/update/';
export const REMOVE_CHART_FROM_SKETCHBOOK =
  '/api/v1/sketchbook/update/remove/chart-ids/';
export const ADD_PAGE_TO_SKETCHBOOK = '/api/v1/sketchbook/update/add-page/';
export const REMOVE_PAGE_FROM_SKETCHBOOK =
  '/api/v1/sketchbook/update/remove-page/';
export const ADD_FLOW_NODE_TO_SKETCHBOOK =
  '/api/v1/sketchbook/update/add-flow-node/';
export const ADD_FLOW_EDGE_TO_SKETCHBOOK =
  '/api/v1/sketchbook/update/add-flow-edge/';
export const UPDATE_FLOW_NODE_LABEL =
  '/api/v1/sketchbook/update/flow-node/label/';
export const UPDATE_FLOW_NODE_POSITION =
  '/api/v1/sketchbook/update/flow-node/position/';
export const UPDATE_FLOW_NODE_DATA =
  '/api/v1/sketchbook/update/flow-node/data/';
export const REMOVE_FLOW_NODE_FROM_SKETCHBOOK =
  '/api/v1/sketchbook/update/remove-flow-node/';
export const REMOVE_FLOW_EDGE_FROM_SKETCHBOOK =
  '/api/v1/sketchbook/update/remove-flow-edge/';
export const TOGGLE_PAGE_ENABLED = '/api/v1/sketchbook/update/page-enabled/';
export const MOVE_CHART_TO_ANOTHER_PAGE =
  '/api/v1/sketchbook/update/move-chart-to-page/';
export const UPDATE_SKETCHBOOK_PAGE_SIZE =
  '/api/v1/sketchbook/update/page-size/';

// Sketchbook Template-related API endpoints
// Legacy endpoints
export const SAVE_SKETCHBOOK_TEMPLATE = '/api/v1/sketchbook-template/save';
export const GET_SKETCHBOOK_TEMPLATE_BY_ID =
  '/api/v1/sketchbook-template/get-by-id/';
export const GET_SKETCHBOOK_TEMPLATE_BY_USER_ID =
  '/api/v1/sketchbook-template/get-by-userid/';
export const GET_SKETCHBOOK_TEMPLATE_BY_USER_ID_PAGINATED =
  '/api/v1/sketchbook-template/get-by-userid/';

// New template endpoints
export const SAVE_SKETCHBOOK_AS_TEMPLATE =
  '/api/v1/sketchbook/save-as-template';
export const USE_SKETCHBOOK_TEMPLATE = '/api/v1/sketchbook/use-template';
export const DELETE_SKETCHBOOK_TEMPLATE = '/api/v1/sketchbook/delete-template/';

// Additional sketchbook operations
export const UPDATE_SKETCHBOOK_NAME = '/api/v1/sketchbook/update/name/';

// Email-related endpoints
export const SAVE_FILE_FOR_EMAIL = '/api/v1/upload/email/attachment';
export const SEND_EMAIL_WITH_ATTACHMENT = '/api/v1/send/email';
