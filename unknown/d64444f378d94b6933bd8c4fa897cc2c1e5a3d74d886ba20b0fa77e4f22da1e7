export const getErrorMessage = (err: any): string => {
  if (err.data) {
    const { message, errorCode } = err.data;

    if (
      errorCode === 'INTERNAL_SERVER_ERROR' &&
      message.includes('E11000 duplicate key error')
    ) {
      return 'The email address is already registered. Please use a different email.';
    }

    return message || 'A server error occurred. Please try again later.';
  }

  return err.message || 'An error occurred. Please try again.';
};
