import { Handle, Position } from 'reactflow';
import styles from './Nodes.module.css';
import { useUpdateFlowNodeLabelMutation } from '../../../../../services/sketchbookServices';

interface NodeData {
  label: string;
  nodeId: string;
  index: number;
  onNodeChange: (nodeId: string, value: string) => void;
  sketchbookId: string;
}

const StartNode = ({ data }: { data: NodeData }) => {
  const [updateFlowNodeLabel] = useUpdateFlowNodeLabelMutation();
  
  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      data.onNodeChange(data.nodeId, e.target.value);
    } catch (error) {
      console.error('Error updating flow node label:', error);
    }
  };

  const handleUpdateLabel = async (e: React.FocusEvent<HTMLInputElement>) => {
    try {
      const payload = {
        label: (e.target as HTMLInputElement).value,
        nodeIndex: data.index,
      };
      const response: any = await updateFlowNodeLabel({ payload, id: data.sketchbookId }).unwrap();
      console.log('response', response);
    } catch (error) {
      console.error('Error updating flow node label:', error);
    }
  };

  return (
    <div className={`${styles.node} ${styles.startNode}`}>
      <div className={styles.content}>
        <input
          value={data.label || ''}
          onChange={handleChange}
          onBlur={handleUpdateLabel}
          className={styles.nodeInput}
          placeholder="Start"
          data-no-drag
        />
      </div>
      <Handle style={{zIndex: 1000}} type="source" position={Position.Bottom} />
    </div>
  );
};

export default StartNode;
