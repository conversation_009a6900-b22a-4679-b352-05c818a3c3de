// Import specific icons from React Icons
import {
  FaFilePdf,
  FaFileWord,
  FaFileImage,
  FaFileExcel,
  FaFilePowerpoint,
  FaFileArchive,
  FaFileAlt,
  FaFile,
} from 'react-icons/fa';

// Function to get file extension
export const getFileExtension = (fileName: string): string | undefined => {
  return fileName.split('.').pop()?.toLowerCase();
};

export function getSimplifiedFileExtension(input: File | string) {
  let fileExtension;

  // Check if input is a File object (contains `type`) or a MIME type string
  if (input instanceof File) {
    // For File object, extract the extension from MIME type
    fileExtension = input.type.split('/').pop();
  } else if (typeof input === 'string') {
    // If input is a MIME type string like 'application/pdf'
    fileExtension = input.includes('/')
      ? input.split('/').pop()
      : input.replace('.', '');
  }

  switch (fileExtension) {
    case 'pdf':
      return 'pdf';
    case 'vnd.ms-excel':
      return 'xls'; // For older Excel files
    case 'vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      return 'xlsx'; // For newer Excel files
    case 'plain':
      return 'txt'; // For text files
    case 'xls':
      return 'xls'; // Handle direct `.xls` extension
    case 'xlsx':
      return 'xlsx'; // Handle direct `.xlsx` extension
    case 'txt':
      return 'txt'; // Handle direct `.txt` extension
    case 'csv':
      return 'csv'; // Handle direct `.csv` extension
    default:
      return 'unknown'; // Handle other or unsupported file types
  }
}

// Function to get the file icon based on the file extension
export const getFileIcon = (fileName: string) => {
  const fileExtension = getFileExtension(fileName);

  let size = '30px';

  switch (fileExtension) {
    case 'pdf':
      return <FaFilePdf style={{ color: '#D32F2F', fontSize: size }} />;
    case 'doc':
    case 'docx':
      return <FaFileWord style={{ color: '#1976D2', fontSize: size }} />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return <FaFileImage style={{ color: '#FFA000 ', fontSize: size }} />;
    case 'xls':
    case 'xlsx':
      return <FaFileExcel style={{ color: '#4CAF50 ', fontSize: size }} />;
    case 'ppt':
    case 'pptx':
      return <FaFilePowerpoint style={{ color: '#FF5722 ', fontSize: size }} />;
    case 'zip':
    case 'rar':
      return <FaFileArchive style={{ color: '#FFC107 ', fontSize: size }} />;
    case 'txt':
      return <FaFileAlt style={{ color: '#757575 ', fontSize: size }} />;
    default:
      return <FaFile style={{ color: '#9E9E9E ', fontSize: size }} />;
  }
};
