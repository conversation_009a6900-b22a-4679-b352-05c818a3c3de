.awaitingactions {
  display: flex;
  flex-direction: column;
}

.projects {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 10px;
}

.loaderContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.emptyState {
  text-align: center;
  padding: 40px;
  background: var(--color-background-secondary);
  border-radius: 8px;
  margin: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}

.emptyState h2 {
  margin-bottom: 12px;
  color: var(--color-text-primary);
  font-size: 20px;
  transition: color 0.3s ease;
}

.emptyState p {
  color: var(--color-text-secondary);
  font-size: 16px;
  transition: color 0.3s ease;
}

.buttonContainer {
  width: 155px;
  height: 48px;
  margin: 10px;
  display: flex;
  justify-content: flex-start;
  gap: 5px;
}

.filterContainer {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  width: 100%;
}

.filterButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  background: var(--color-background-tertiary);
  cursor: pointer;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease;
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-right: 8px;
}

.filterButton:hover {
  background: var(--color-background-hover);
  transform: translateY(-1px);
}

.filterButton.active {
  background: var(--color-primary);
  color: var(--color-primary-contrast);
}

.filterButton.active .filterIcon {
  color: var(--color-primary-contrast);
}

.filterIcon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  font-size: 18px;
}

.filterLabel {
  font-weight: 500;
}

.filterCount {
  background: var(--color-background-tertiary);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-left: 4px;
  transition: background-color 0.3s ease;
}

.filterButton.active .filterCount {
  background: rgba(var(--color-primary-contrast-rgb), 0.2);
}

.headerSection {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 90%;
  align-self: center;
}

.topRow {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.projects {
  display: grid;
  align-self: center;
  gap: 20px;
  width: 90%;
}

.projects.grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  /* padding: 24px; */
}

.projects.list {
  grid-template-columns: 1fr;
  margin: 20px 25px;
}

/* Adjust the search component container if needed */
.searchContainer {
  width: 100%;
}

/* Add responsive behavior */
@media (max-width: 768px) {
  .topRow {
    flex-direction: column;
    align-items: stretch;
  }

  .searchContainer {
    width: 100%;
    margin-bottom: 16px;
  }
}
