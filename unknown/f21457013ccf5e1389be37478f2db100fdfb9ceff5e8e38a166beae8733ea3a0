import React from 'react';
import styles from './Modal.module.css';
import close from '../../../assets/images/Cross.png'

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;

  return (
    <>
      <div className={styles.overlay} onClick={onClose} />
      <div className={styles.modal}>
        <img src={close} className={styles.closeButton} onClick={onClose}/>
        {children}
      </div>
    </>
  );
};

export default Modal;
