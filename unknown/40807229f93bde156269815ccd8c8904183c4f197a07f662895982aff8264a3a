import { useState, useEffect, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  Switch,
  Typography,
  Box,
  Container,
  CircularProgress,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Chip,
  Pagination,
} from '@mui/material';
import {
  useGetUsersPaginatedQuery,
  useUpdateUserByAdminMutation,
} from '../../services/userMgtService';
import { styles } from './AdminPage.styles';
import { Search } from '@mui/icons-material';
import toast from 'react-hot-toast';

// Add this constant at the top of the file, before the AdminPage component
const AVAILABLE_ROLES = ['USER', 'ADMIN'];

const AdminPage = () => {
  const [page, setPage] = useState(0); // Page is 0-based
  const [size, setSize] = useState(10); // Default page size

  const {
    data: allUsers,
    error,
    isLoading,
    refetch,
  } = useGetUsersPaginatedQuery({
    page,
    size,
  });

  const [updateUserByAdmin] = useUpdateUserByAdminMutation();

  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [tierFilter, setTierFilter] = useState('all');
  const [enabledFilter, setEnabledFilter] = useState('all');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const filteredUsers = useMemo(() => {
    return allUsers?.data?.content?.filter((user: any) => {
      // Search filter
      const matchesSearch = !debouncedSearchQuery
        ? true
        : user.name
            .toLowerCase()
            .includes(debouncedSearchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(debouncedSearchQuery.toLowerCase());

      // Role filter - compare with the role after removing 'ROLE_' prefix
      const userRole = user.role.replace('ROLE_', '').toUpperCase();
      const matchesRole =
        roleFilter === 'all' || userRole === roleFilter.toUpperCase();

      // Tier filter - compare with the tier value directly
      const userTier = user.tier?.toLowerCase();
      const matchesTier =
        tierFilter === 'all' || userTier === tierFilter?.toLowerCase();

      // Enabled filter
      const matchesEnabled =
        enabledFilter === 'all' ||
        (enabledFilter === 'enabled' ? user.enabled : !user.enabled);

      return matchesSearch && matchesRole && matchesTier && matchesEnabled;
    });
  }, [
    allUsers?.data?.content,
    debouncedSearchQuery,
    roleFilter,
    tierFilter,
    enabledFilter,
  ]);

  const handleAccessToggle = async (userId: string, currentAccess: boolean) => {
    try {
      const response: any = await updateUserByAdmin({
        userId,
        userData: {
          isEnabled: !currentAccess,
        },
      }).unwrap();

      if (response.success) {
        toast.success('User access updated successfully');
        refetch();
      } else {
        toast.error('Failed to update user access');
      }
    } catch (error) {
      console.error('Error updating user access:', error);
      toast.error('Failed to update user access');
    }
  };

  const handleRoleChange = async (userId: string, newRole: string) => {
    try {
      const response: any = await updateUserByAdmin({
        userId,
        userData: {
          role: `ROLE_${newRole}`,
        },
      }).unwrap();

      if (response.success) {
        toast.success('User role updated successfully');
        refetch();
      } else {
        toast.error('Failed to update user role');
      }
    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error('Failed to update user role');
    }
  };

  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="80vh"
        flexDirection="column"
        gap={2}
      >
        <CircularProgress color="primary" />
        <Typography variant="body1" color="text.secondary">
          Loading users...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="80vh"
        px={2}
      >
        <Alert
          severity="error"
          variant="outlined"
          sx={{ maxWidth: 'sm', width: '100%' }}
        >
          {'An unexpected error occurred'}
        </Alert>
      </Box>
    );
  }

  if (!allUsers?.data?.content?.length) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="80vh"
        px={2}
      >
        <Alert
          severity="info"
          variant="outlined"
          sx={{ maxWidth: 'sm', width: '100%' }}
        >
          No users found
        </Alert>
      </Box>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box>
        {/* Filters Section */}
        <Paper
          elevation={0}
          sx={{
            ...styles.container,
            mb: 3,
            p: 3,
            borderRadius: 2,
            backgroundColor: 'background.default',
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Search and Filters Row */}
            <Box
              sx={{
                display: 'flex',
                gap: 2,
                flexWrap: 'wrap',
                alignItems: 'center',
              }}
            >
              {/* Search Field */}
              <TextField
                label="Search by name or email"
                variant="outlined"
                size="small"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                sx={{
                  flex: 2,
                  minWidth: 300,
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'background.paper',
                    borderRadius: 1.5,
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#4141e4',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#4141e4',
                  },
                }}
                InputProps={{
                  startAdornment: <Search sx={{ color: '#4141e4', mr: 1 }} />,
                }}
              />

              {/* Role Filter */}
              <FormControl
                size="small"
                sx={{
                  flex: 1,
                  minWidth: 150,
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'background.paper',
                    borderRadius: 1.5,
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#4141e4',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#4141e4',
                  },
                }}
              >
                <InputLabel sx={{ color: '#4141e4' }}>Role</InputLabel>
                <Select
                  value={roleFilter}
                  onChange={(e) => setRoleFilter(e.target.value)}
                  label="Role"
                  sx={{ color: '#4141e4' }}
                >
                  <MenuItem value="all">All Roles</MenuItem>
                  {AVAILABLE_ROLES.map((role) => (
                    <MenuItem key={role} value={role.toLowerCase()}>
                      {role}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Tier Filter */}
              <FormControl
                size="small"
                sx={{
                  flex: 1,
                  minWidth: 150,
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'background.paper',
                    borderRadius: 1.5,
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#4141e4',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#4141e4',
                  },
                }}
              >
                <InputLabel sx={{ color: '#4141e4' }}>Tier</InputLabel>
                <Select
                  value={tierFilter}
                  onChange={(e) => setTierFilter(e.target.value)}
                  label="Tier"
                  sx={{ color: '#4141e4' }}
                >
                  <MenuItem value="all">All Tiers</MenuItem>
                  <MenuItem value="free">Free</MenuItem>
                  <MenuItem value="premium">Premium</MenuItem>
                </Select>
              </FormControl>

              {/* Status Filter */}
              <FormControl
                size="small"
                sx={{
                  flex: 1,
                  minWidth: 150,
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'background.paper',
                    borderRadius: 1.5,
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#4141e4',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#4141e4',
                  },
                }}
              >
                <InputLabel sx={{ color: '#4141e4' }}>Status</InputLabel>
                <Select
                  value={enabledFilter}
                  onChange={(e) => setEnabledFilter(e.target.value)}
                  label="Status"
                  sx={{ color: '#4141e4' }}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="enabled">Enabled</MenuItem>
                  <MenuItem value="disabled">Disabled</MenuItem>
                </Select>
              </FormControl>
            </Box>

            {/* Results count with divider */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Chip
                label={`${filteredUsers?.length || 0} users`}
                sx={{ bgcolor: '#4141e4', color: 'white' }}
                size="small"
                variant="filled"
              />
              <Typography variant="body2" sx={{ color: '#4141e4' }}>
                out of {allUsers?.data?.totalItems || 0} total
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* Table Section */}
        <Paper
          elevation={0}
          sx={{
            ...styles.container,
            borderRadius: 2,
            overflow: 'hidden',
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                    ...styles.tableCell,
                    backgroundColor: 'background.default',
                  }}
                >
                  Name
                </TableCell>
                <TableCell
                  sx={{
                    ...styles.tableCell,
                    backgroundColor: 'background.default',
                  }}
                >
                  Email
                </TableCell>
                <TableCell
                  sx={{
                    ...styles.tableCell,
                    backgroundColor: 'background.default',
                  }}
                >
                  Role
                </TableCell>
                <TableCell
                  sx={{
                    ...styles.tableCell,
                    backgroundColor: 'background.default',
                  }}
                >
                  Tier
                </TableCell>
                <TableCell
                  sx={{
                    ...styles.tableCell,
                    backgroundColor: 'background.default',
                  }}
                >
                  Credits
                </TableCell>
                <TableCell
                  sx={{
                    ...styles.tableCell,
                    backgroundColor: 'background.default',
                  }}
                >
                  Access
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredUsers?.map((user: any) => (
                <TableRow
                  key={user.id}
                  sx={{ '&:hover': { backgroundColor: 'action.hover' } }}
                >
                  <TableCell sx={styles.nameCell}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar
                        sx={{ width: 32, height: 32, bgcolor: '#979799' }}
                      >
                        {user.name.charAt(0)}
                      </Avatar>
                      {user.name}
                    </Box>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <FormControl size="small" fullWidth>
                      <Select
                        value={user.role.replace('ROLE_', '')}
                        onChange={(e) =>
                          handleRoleChange(user.id, e.target.value)
                        }
                        size="small"
                        sx={{
                          ...styles.roleChip(user.role),
                          minWidth: 100,
                          height: 24,
                          '.MuiSelect-select': {
                            padding: '2px 8px',
                          },
                        }}
                      >
                        {AVAILABLE_ROLES.map((role) => (
                          <MenuItem key={role} value={role}>
                            {role}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={user.tier !== 'free' ? 'Free' : 'Premium'}
                      size="small"
                      sx={{
                        bgcolor:
                          user.tier !== 'premium' ? '#4141e4' : 'transparent',
                        color: user.tier !== 'premium' ? 'white' : '#4141e4',
                        borderColor: '#4141e4',
                      }}
                      variant={user.tier !== 'premium' ? 'filled' : 'outlined'}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography component="span" sx={styles.placeholderText}>
                      {user.credits || 100}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={user.enabled}
                      onChange={() => handleAccessToggle(user.id, user.enabled)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: '#4141e4',
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track':
                          {
                            backgroundColor: '#4141e4',
                          },
                      }}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Paper>

        {/* Pagination Section */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Pagination
            count={allUsers?.data?.totalPages || 1}
            page={page + 1} // Pagination component is 1-based
            onChange={(event, value) => setPage(value - 1)} // Convert back to 0-based
            color="primary"
          />
        </Box>
      </Box>
    </Container>
  );
};

export default AdminPage;
