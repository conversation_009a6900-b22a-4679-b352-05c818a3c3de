
.search {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-weight: 400;
    line-height: 24px;
    opacity: 1;
    width: 70%;
    /* margin-left: 15px; */
}

.searchWrapper {
    position: relative;
    flex: 1; 
}

.searchInput {
    height: 40px;
    padding: 10px 14px 10px 40px;
    border-radius: 8px;
    border: 1px solid #D1DFEC;
    font-size: 16px;
    width: 100%; 
    box-shadow: 2px 2px 4px rgba(16, 24, 40, 0.1);
    box-sizing: border-box;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.searchInput::placeholder{
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-weight: 400;
    font-style: italic;
}

.searchIcon {
    position: absolute;
    left: 14px; /* Space between the icon and the input edge */
    top: 50%;
    transform: translateY(-50%); /* Center the icon vertically */
    color: #999;
    width: 19px;
    height: 19px;
    
}
