import React, { ReactNode } from 'react';
import Logo from '../../../assets/images/nuequiplogo.png';
import styles from './AuthLayout.module.css';

interface AuthLayoutProps {
  title: string;
  subtitle: string;
  children: ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({
  title,
  subtitle,
  children,
}) => {
  return (
    <div className={styles.container}>
      <img
        src={Logo}
        alt="Logo"
        style={{ width: '200px', marginTop: '64px', marginLeft: '64px' }}
      />
      <div className={styles.headingContainer}>
        <h2 className={styles.heading}>{title}</h2>
        <p className={styles.subHeading}>{subtitle}</p>
      </div>
      <div className={styles.formContainer}>{children}</div>
    </div>
  );
};

export default AuthLayout;
