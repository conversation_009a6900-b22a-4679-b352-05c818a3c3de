// import React from 'react';
// import { Bar } from 'react-chartjs-2';
// import {
//   Chart as ChartJS,
//   CategoryScale,
//   LinearScale,
//   BarElement,
//   Title,
//   Tooltip,
//   Legend,
// } from 'chart.js';

// ChartJS.register(
//   CategoryScale,
//   LinearScale,
//   BarElement,
//   Title,
//   Tooltip,
//   Legend
// );

// interface ChartComponentProps {
//   data: {
//     labels: string[];
//     datasets: {
//       label: string;
//       data: number[];
//       backgroundColor: string[];
//     }[];
//   };
// }

// const ChartComponent: React.FC<ChartComponentProps> = ({ data }) => {
//   return <Bar data={data} options={{ maintainAspectRatio: false }} />;
// };

// export default ChartComponent;
