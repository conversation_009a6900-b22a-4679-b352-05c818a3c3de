.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 8px;
  width: 100%;
  max-width: 400px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.titleWrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.warningIcon {
  color: #ff4d4f;
  font-size: 24px;
}

.header h2 {
  margin: 0;
  font-size: 18px;
  color: #1f1f1f;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: #666;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.closeButton:hover {
  background-color: #f5f5f5;
  color: #333;
}

.content {
  margin-bottom: 24px;
}

.content p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.actions button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancelButton {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  color: #666;
}

.cancelButton:hover {
  background: #e8e8e8;
  border-color: #c0c0c0;
}

.confirmButton {
  background: #ff4d4f;
  border: 1px solid #ff4d4f;
  color: white;
}

.confirmButton:hover {
  background: #ff7875;
  border-color: #ff7875;
}
