.peopleSection {
  margin-bottom: 2rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.participantsHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.required {
  color: #e53e3e;
  margin-left: 2px;
  font-weight: bold;
}

.errorText {
  font-size: 0.875rem;
  color: #e53e3e;
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.searchContainer {
  position: relative;
  margin-bottom: 1.5rem;
}

.searchInput {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  margin-bottom: 0.5rem;
}

.standardInput {
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  font-size: 1rem;
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  transition: all 0.2s;
  width: 100%;
}

.standardInput:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

.standardInput::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

.searchResults {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  right: 0;
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  max-height: 300px;
  overflow-y: auto;
  z-index: 10;
}

.searchResult {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--color-border);
  color: var(--color-text-primary);
}

.searchResult:last-child {
  border-bottom: none;
}

.searchResult:hover {
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.teamTable {
  background: var(--color-background-secondary);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-border);
}

.teamTable table {
  width: 100%;
  border-collapse: collapse;
}

.teamTable th {
  background: rgba(var(--color-border-rgb), 0.1);
  padding: 1rem 1.25rem;
  text-align: left;
  font-weight: 600;
  color: var(--color-text-secondary);
  font-size: 0.9rem;
}

.teamTable td {
  padding: 1rem 1.25rem;
  border-top: 1px solid var(--color-border);
  color: var(--color-text-primary);
}

.teamTable td:last-child {
  width: 60px;
  text-align: center;
}

.userCell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--color-text-primary);
}

.userImg {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--color-background-secondary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.helperText {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  margin-top: 1rem;
  opacity: 0.8;
  text-align: center;
}
