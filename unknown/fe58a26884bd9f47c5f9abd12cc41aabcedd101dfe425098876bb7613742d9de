import { useState } from 'react';
import { AI_BASE_URL } from '../services/config';
import { createFormData } from '../utils/formDataHelper';
import { getSimplifiedFileExtension } from '../utils/getFileExtention';

interface UseChatStreamProps {
  userId: string;
  projectId: string;
  fileName: string;
  fileType: string;
  projectTitle: string;
  onStreamStart: () => number;
  onStreamMessage: (streamMessageId: number, content: string) => void;
  onFinalResponse: (
    response: any,
    streamMessageId: number,
    content: string
  ) => void;
  onError: (streamMessageId: number) => void;
}

export const useChatStream = ({
  userId,
  projectId,
  fileName,
  fileType,
  projectTitle,
  onStreamStart,
  onStreamMessage,
  onFinalResponse,
  onError,
}: UseChatStreamProps) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStreamingMessageId, setCurrentStreamingMessageId] = useState<
    string | number | null
  >(null);
  const [loadingFinalResponse, setLoadingFinalResponse] = useState(false);
  const [codeInput, setCodeInput] = useState(false);

  const handleStreamMessage = (
    jsonData: any,
    streamMessageId: number,
    streamContent: string
  ) => {
    switch (jsonData.type) {
      case 'complete':
        setLoadingFinalResponse(true);
        break;
      case 'code_input':
        setCodeInput(true);
        break;
      case 'message':
        onStreamMessage(streamMessageId, streamContent);
        break;
      case 'final_response':
        onFinalResponse(jsonData.content, streamMessageId, streamContent);
        break;
    }
  };

  const sendMessage = async (content: string) => {
    if (!content || isProcessing) return;
    setIsProcessing(true);

    const streamMessageId = onStreamStart();
    setCurrentStreamingMessageId(streamMessageId);

    const currentResponseType = localStorage.getItem('responseType') || 'brief';
    const storedPageRange = localStorage.getItem('pageRangeChanges');
    const currentPageRange =
      storedPageRange !== null && storedPageRange !== 'undefined'
        ? storedPageRange
        : '';
    const dontGenerateChart =
      localStorage.getItem('dontGenerateChart') === 'true';

    const fileExtension = getSimplifiedFileExtension(fileType);
    const formData = createFormData({
      userId,
      projectId,
      pageNumber: currentPageRange,
      fileName: fileName || '',
      fileType: fileExtension || '',
      title: projectTitle || '',
      visualization: dontGenerateChart,
      prompt: content,
      response_type: currentResponseType,
    });

    try {
      const response = await fetch(`${AI_BASE_URL}/process/`, {
        method: 'POST',
        body: formData,
      });

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No reader available');

      let streamContent = '';
      let decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonData = JSON.parse(line.slice(6));
              setCodeInput(false);
              setLoadingFinalResponse(false);

              handleStreamMessage(jsonData, streamMessageId, streamContent);

              if (jsonData.type === 'message') {
                streamContent += jsonData.content;
              }
            } catch (e) {
              console.error('Error parsing JSON:', e, line);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error processing your request:', error);
      onError(streamMessageId);
    } finally {
      setIsProcessing(false);
      setCurrentStreamingMessageId(null);
    }
  };

  return {
    isProcessing,
    currentStreamingMessageId,
    loadingFinalResponse,
    codeInput,
    sendMessage,
  };
};
