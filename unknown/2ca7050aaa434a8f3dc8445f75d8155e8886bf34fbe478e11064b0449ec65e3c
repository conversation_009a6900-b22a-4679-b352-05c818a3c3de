.chipContainer,
.darkChipContainer {
  min-width: fit-content;
  max-width: 800px;
  /* padding: 4px; */
  border-radius: 8px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: visible;
  box-shadow:
    rgba(67, 71, 85, 0.27) 0px 0px 0.25em,
    rgba(90, 125, 188, 0.05) 0px 0.25em 1em;
}

.labelContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}

.darkChipContainer {
  opacity: 0.5;
  padding: 2px;
  height: fit-content;
  border: none !important;
}

.darkChipContainer > .labelContainer {
  font-size: 12px;
}

.darkChipContainer:hover {
  opacity: 1;
}

.darkChipContainer:focus {
  opacity: 1;
}

.darkChipContainer:active {
  opacity: 1;
}

.hintChipContainer {
  padding: 2px;
  border: none !important;
  background-color: transparent !important;
}

.hintChipContainer > .labelContainer {
  font-family: Lato;
  font-size: 12px;
  font-style: italic;
  color: gray;
}

/* markdownStyles.css */

.markdown {
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
}

.markdown h1,
.markdown h2,
.markdown h3 {
  color: #333;
}

.markdown ul,
.markdown ol {
  padding-left: 20px;
}

.markdown li {
  margin-bottom: 0.5em;
}

.markdown blockquote {
  border-left: 4px solid #ccc;
  padding-left: 1em;
  color: #555;
  font-style: italic;
}

.suggestedQuestionsRow {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* Adjust spacing between chips */
}

.exportButton:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.buttonGroup {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

.hidden {
  opacity: 0;
  pointer-events: none;
}

.visible {
  opacity: 1;
  pointer-events: auto;
}
.exportButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
}

.exportButton:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.buttonGroupContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
  width: 100%;
}

.buttonGroup {
  display: flex;
  align-items: center;
  gap: 4px;
}

.exportButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.exportButton:hover {
  color: #174e86;
}

/* Add these additional styles if needed */
.labelContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.chipContainer {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  position: relative;
}

.messageControls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.selectionControls {
  position: absolute;
  background: white;
  z-index: 1000;
  cursor: pointer;
  white-space: nowrap;
}
