POST
http://localhost:8082/api/v1/sketchbook/save

Payload: 
{
	"ai_chart_ids": ["23b2844a-0909-44d7-b974-45be4f05eb43"],
	"ai_image_ids" :  ["file-b0W1vnB9jo4ZP8cx4O0LZtDm"],
	"project_id" :  "6730ac2b998707fcdf706f0d",
	"user_id" :  "66ffd00a9198b631b28292c2",
    "sketchbook_name": "Dummy Sketchbook 24"
}

Response:
{
    "timestamp": "12-Nov-2024 12:14:39 PM",
    "path": "/api/v1/sketchbook/save",
    "status": 201,
    "success": true,
    "message": "Success",
    "error": "",
    "data": {
        "id": "6731a424e273335735553187"
    }
}

PUT
http://localhost:8082/api/v1/sketchbook/update/6731a424e273335735553187
Payload:
{
    "ai_chart_ids": ["23b2844a-0909-44d7-b974-45be4f05eb43"],
	"ai_image_ids" :  ["file-b0W1vnB9jo4ZP8cx4O0LZtDm"],
    "sketchbook_name": "Dummy Sketchbook 29",
     "flowNodes": [
          {
            "id": "node-1",
            "type": "start",
            "position": { "x": 100, "y": 100 },
            "data": {
              "label": "Start",
              "nodeId": "node-1"
            },
            "draggable": true
          },
          {
            "id": "node-2",
            "type": "process",
            "position": { "x": 250, "y": 100 },
            "data": {
              "label": "Process Step",
              "nodeId": "node-2"
            },
            "draggable": true
          },
          {
            "id": "node-3",
            "type": "organization",
            "position": { "x": 400, "y": 100 },
            "data": {
              "name": "John Doe",
              "role": "Manager",
              "department": "Sales",
              "nodeId": "node-3"
            },
            "draggable": true
          }
        ],
       "flowEdges": [
          {
            "id": "edge-1",
            "source": "node-1",
            "target": "node-2",
            "type": "default"
          },
          {
            "id": "edge-2",
            "source": "node-2",
            "target": "node-3",
            "type": "default"
          }
     ],
	"activePage":"12",    	
    "pageSize": {
      "value": "a4",
      "label": "A4",
      "width": 559,
      "height": 793,
      "orientation": "portrait"
    },
    "pages": [
      {
        "id": "01",
        "name": "Page 01",
        "pageEnabled": true,
		"custom_chart_ids": [
                 {  "id": "67322ac8bf274e26bcbb2e43", "type": "bar" },
                 {  "id": "67322d029780fd3ae13206ae",  "type": "gauge" }
		 ],
        "layouts": [
          {
            "i": "chart-1",
            "x": 0,
            "y": 0,
            "w": 3,
            "h": 2
          },
          {
            "i": "chart-2",
            "x": 3,
            "y": 0,
            "w": 3,
            "h": 2
          }
        ]
      }
    ]
}

Response:
{
    "timestamp": "11-Nov-2024 13:40:56 PM",
    "path": "/api/v1/sketchbook/update/6731a424e273335735553187",
    "status": 200,
    "success": true,
    "message": "Success",
    "error": "",
    "data": {
        "id": "6731a424e273335735553187"
    }
}


GET 
http://localhost:8082/api/v1/sketchbook/get-by-userid/66ffd00a9198b631b28292c2
{
    "timestamp": "11-Nov-2024 23:31:20 PM",
    "path": "/api/v1/sketchbook/get-by-userid/66ffd00a9198b631b28292c2",
    "status": 200,
    "success": true,
    "message": "Success",
    "error": "",
    "data": [
        {
            "id": "6731a424e273335735553187",
            "project_id": "6730ac2b998707fcdf706f0d",
            "sketchbook_name": "Dummy Sketchbook 22",
            "ai_chart_ids": [
                "23b2844a-0909-44d7-b974-45be4f05eb43"
            ],
            "ai_image_ids": [
                "file-b0W1vnB9jo4ZP8cx4O0LZtDm"
            ],
            "custom_chart_ids": [
                {
                    "pageIndex": 0,
                    "custom_chart_info": [
                        {
                            "id": "67322ac8bf274e26bcbb2e43",
                            "type": "bar"
                        },
                        {
                            "id": "67322d029780fd3ae13206ae",
                            "type": "gauge"
                        },
                        {
                            "id": "673240a92d72363fc8ffac2c",
                            "type": "gauge"
                        }
                    ]
                }			
            ]
        }
    ]
}


GET
http://localhost:8082/api/v1/sketchbook/get-by-id/6731a424e273335735553187
{
    "timestamp": "12-Nov-2024 13:06:38 PM",
    "path": "/api/v1/sketchbook/get-by-id/6731a424e273335735553187",
    "status": 200,
    "success": true,
    "message": "Success",
    "error": "",
    "data": {
        "id": "6731a424e273335735553187",
        "ai_chart_ids": [
            "23b2844a-0909-44d7-b974-45be4f05eb43"
        ],
        "ai_image_ids": [
            "file-b0W1vnB9jo4ZP8cx4O0LZtDm"
        ],
        "ai_charts": [
            {
                "chart_name": "Unit Prices by Region",
                "chart_id": "23b2844a-0909-44d7-b974-45be4f05eb43",
                "type_of_chart": "Bar Chart",
                "x_labels": [
                    "North",
                    "South",
                    "East",
                    "West"
                ],
                "y_labels": [
                    "Price (in $)"
                ],
                "values": [
                    150,
                    200,
                    180,
                    220
                ]
            }
        ],
        "ai_images": [
            {
                "imageId": "file-b0W1vnB9jo4ZP8cx4O0LZtDm",
                "image": "data:image/png;base64,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"
            }
        ],
        "project_id": "6730ac2b998707fcdf706f0d",
        "project_title": "TEST11",
        "sketchbook_name": "Dummy Sketchbook 29",
        "user_id": "66ffd00a9198b631b28292c2",
        "flowNodes": [
            {
                "id": "node-1",
                "type": "start",
                "position": {
                    "x": 100,
                    "y": 100
                },
                "data": {
                    "label": "Start",
                    "nodeId": "node-1",
                    "name": null,
                    "role": null,
                    "department": null
                },
                "draggable": true
            },
            {
                "id": "node-2",
                "type": "process",
                "position": {
                    "x": 250,
                    "y": 100
                },
                "data": {
                    "label": "Process Step",
                    "nodeId": "node-2",
                    "name": null,
                    "role": null,
                    "department": null
                },
                "draggable": true
            },
            {
                "id": "node-3",
                "type": "organization",
                "position": {
                    "x": 400,
                    "y": 100
                },
                "data": {
                    "label": null,
                    "nodeId": "node-3",
                    "name": "John Doe",
                    "role": "Manager",
                    "department": "Sales"
                },
                "draggable": true
            }
        ],
        "flowEdges": [
            {
                "id": "edge-1",
                "source": "node-1",
                "target": "node-2",
                "type": "default"
            },
            {
                "id": "edge-2",
                "source": "node-2",
                "target": "node-3",
                "type": "default"
            }
        ],
        "activePage": "12",
        "pageSize": {
            "value": "a4",
            "label": "A4",
            "width": 559,
            "height": 793,
            "orientation": "portrait"
        },
        "pages": [
            {
                "id": "01",
                "name": "Page 01",
                "pageEnabled": true,
                "custom_chart_ids": [
                    {
                        "id": "67322ac8bf274e26bcbb2e43",
                        "type": "bar"
                    },
                    {
                        "id": "67322d029780fd3ae13206ae",
                        "type": "gauge"
                    }
                ],
                "customCharts": {
                    "barCharts": [
                        {
                            "id": "67322ac8bf274e26bcbb2e43",
                            "type": "bar",
                            "sketchbookId": "6731a424e273335735553187",
                            "pageIndex": 0,
                            "data": {
                                "labels": [
                                    "January",
                                    "February",
                                    "March"
                                ],
                                "datasets": [
                                    {
                                        "label": "Sales Data",
                                        "data": [
                                            65,
                                            59,
                                            80
                                        ],
                                        "backgroundColor": "rgba(75, 192, 192, 0.2)",
                                        "borderColor": "rgba(75, 192, 192, 1)",
                                        "borderWidth": 1
                                    }
                                ]
                            },
                            "options": {
                                "responsive": true,
                                "maintainAspectRatio": false,
                                "scales": {
                                    "y": {
                                        "beginAtZero": true
                                    }
                                }
                            },
                            "width": 400,
                            "height": 300
                        }
                    ],
                    "lineCharts": [],
                    "ganttCharts": [],
                    "gaugeCharts": [
                        {
                            "id": "67322d029780fd3ae13206ae",
                            "type": "gauge",
                            "sketchbookId": "6731a424e273335735553187",
                            "pageIndex": 0,
                            "data": {
                                "percent": 0.76
                            },
                            "options": {
                                "id": "gauge-1",
                                "nrOfLevels": 20,
                                "colors": [
                                    "#FF5F6D",
                                    "#FFC371"
                                ],
                                "arcWidth": 0.3,
                                "arcPadding": 0.02,
                                "cornerRadius": 3,
                                "needleColor": "#464A4F",
                                "needleBaseColor": "#464A4F",
                                "textColor": "#000000",
                                "animate": true,
                                "animDelay": 500,
                                "animateDuration": 3000,
                                "marginInPercent": 0.05,
                                "formatTextValue": "value => `${Math.round(value * 100)}%`"
                            },
                            "width": 300,
                            "height": 300
                        }
                    ],
                    "textareaCharts": []
                },
                "layouts": [
                    {
                        "i": "chart-1",
                        "x": 0,
                        "y": 0,
                        "w": 3,
                        "h": 2
                    },
                    {
                        "i": "chart-2",
                        "x": 3,
                        "y": 0,
                        "w": 3,
                        "h": 2
                    }
                ]
            }
        ]
    }
}

POST 
http://localhost:8082/api/v1/custom-charts/save/bubble
Payload: 

{
  "type": "bubble", 
  "pageIndex":0,
  "sketchbookId": "6731a424e273335735553187",
  "title": "Sample Chart Title",
  "labels": ["Label 1", "Label 2", "Label 3"],
  "datasets": [
    {
      "label": "Dataset 1",
      "data": [10, 20, 30],
      "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56"],
      "borderColor": "#FF6384",
      "borderWidth": 1,
      "fill": false
    }
  ],
  "options": {
    "responsive": true,
    "scales": {
      "xAxes": [
        {
          "type": "category", 
          "display": true,
          "scaleLabel": {
            "display": true,
            "labelString": "X Axis Label"
          }
        }
      ],
      "yAxes": [
        {
          "type": "linear",
          "display": true,
          "scaleLabel": {
            "display": true,
            "labelString": "Y Axis Label"
          },
          "ticks": {
            "beginAtZero": true
          }
        }
      ]
    },
    "legend": {
      "display": true,
      "position": "top"
    },
    "title": {
      "display": true,
      "text": "Chart Title"
    },
    "layout": {
      "padding": {
        "left": 10,
        "right": 10,
        "top": 10,
        "bottom": 10
      }
    }
  }
}

Response:
{
    "timestamp": "13-Nov-2024 13:40:38 PM",
    "path": "/api/v1/custom-charts/save/bubble",
    "status": 201,
    "success": true,
    "message": "Success",
    "error": "",
    "data": {
        "id": "67345ec3f50ca82738dbe109"
    }
}

EXAMPLE #2:

http://localhost:8082/api/v1/custom-charts/save/bubble
Payload: 

{
  "type": "bubble", 
  "pageIndex":0,
  "sketchbookId": "6731a424e273335735553187",
  "title": "Sample Chart Title",
  "labels": ["Label 1", "Label 2", "Label 3"],
  "datasets": [
    {
      "label": "Dataset 1",
      "data": [10, 20, 30],
      "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56"],
      "borderColor": "#FF6384",
      "borderWidth": 1,
      "fill": false
    }
  ],
  "options": {
    "responsive": true,
    "scales": {
      "xAxes": [
        {
          "type": "category", 
          "display": true,
          "scaleLabel": {
            "display": true,
            "labelString": "X Axis Label"
          }
        }
      ],
      "yAxes": [
        {
          "type": "linear",
          "display": true,
          "scaleLabel": {
            "display": true,
            "labelString": "Y Axis Label"
          },
          "ticks": {
            "beginAtZero": true
          }
        }
      ]
    },
    "legend": {
      "display": true,
      "position": "top"
    },
    "title": {
      "display": true,
      "text": "Chart Title"
    },
    "layout": {
      "padding": {
        "left": 10,
        "right": 10,
        "top": 10,
        "bottom": 10
      }
    }
  }
}

Response:
{
    "timestamp": "13-Nov-2024 13:40:38 PM",
    "path": "/api/v1/custom-charts/save/bubble",
    "status": 201,
    "success": true,
    "message": "Success",
    "error": "",
    "data": {
        "id": "67345efef50ca82738dbe10a"
    }
}

Example #3 
http://localhost:8082/api/v1/custom-charts/save/timeline
Payload:
        {
  "type": "timeline",
  "pageIndex":0,
   "sketchbookId": "6731a424e273335735553187",
  "title": "Project Timeline or Sprint Burndown",
  "labels": ["2024-01-01", "2024-02-01", "2024-03-01"], 
  "datasets": [
    {
      "label": "Timeline Events",
      "data": [
        { "x": "2024-01-01", "y": "Task A", "duration": 10 }, 
        { "x": "2024-02-01", "y": "Task B", "duration": 20 },
        { "x": "2024-03-01", "y": "Task C", "duration": 15 }
      ],
      "backgroundColor": "#36A2EB",
      "borderColor": "#36A2EB",
      "fill": false 
    },
    {
      "label": "Remaining Tasks",
      "data": [
        { "x": "2024-01-01", "y": 50 },
        { "x": "2024-01-02", "y": 40 },
        { "x": "2024-01-03", "y": 30 },
        { "x": "2024-01-04", "y": 20 },
        { "x": "2024-01-05", "y": 10 },
        { "x": "2024-01-06", "y": 0 }
      ],
      "backgroundColor": "rgba(255,99,132,0.2)",
      "borderColor": "rgba(255,99,132,1)",
      "fill": true 
    }
  ],
  "options": {
    "responsive": true,
    "scales": {
      "xAxes": [
        {
          "type": "time",
          "time": {
            "unit": "day"
          },
          "scaleLabel": {
            "display": true,
            "labelString": "Date"
          }
        }
      ],
      "yAxes": [
        {
          "type": "linear",
          "display": true,
          "scaleLabel": {
            "display": true,
            "labelString": "Value"
          },
          "ticks": {
            "beginAtZero": true,
            "reverse": true
          }
        }
      ]
    },
    "title": {
      "display": true,
      "text": "Timeline and Burndown Chart"
    },
    "legend": {
      "display": true,
      "position": "top"
    }
  }
}

Response:
{
    "timestamp": "13-Nov-2024 13:44:33 PM",
    "path": "/api/v1/custom-charts/save/timeline",
    "status": 201,
    "success": true,
    "message": "Success",
    "error": "",
    "data": {
        "id": "67345fe9f50ca82738dbe10b"
    }
}

Saving a chart, updates custom_chart_ids array in specified pageIndex of the specified sketchbook by adding its id in that array.


PUT
http://localhost:8082/api/v1/custom-charts/update/gauge/67322d029780fd3ae13206ae
Payload:
{
		    "sketchbookId": "6731a424e273335735553187",
			"pageIndex": 0,
            "type": "gauge",
            "data": {
              "percent": 0.78
            },
            "options": {
              "id": "gauge-1",
              "nrOfLevels": 20,
              "colors": ["#FF5F6D", "#FFC371"],
              "arcWidth": 0.3,
              "arcPadding": 0.02,
              "cornerRadius": 3,
              "needleColor": "#464A4F",
              "needleBaseColor": "#464A4F",
              "textColor": "#000000",
              "animate": true,
              "animDelay": 500,
              "animateDuration": 3000,
              "marginInPercent": 0.05,
              "formatTextValue": "value => `${Math.round(value * 100)}%`"
            },
            "width": 300,
            "height": 300
}
Response:
{
    "timestamp": "11-Nov-2024 21:52:03 PM",
    "path": "/api/v1/custom-charts/update/gauge/67322d029780fd3ae13206ae",
    "status": 200,
    "success": true,
    "message": "Success",
    "error": "",
    "data": {
        "id": "67322d029780fd3ae13206ae"
    }
}


Example #2
http://localhost:8082/api/v1/custom-charts/update/timeline/67345fe9f50ca82738dbe10b
Payload:
{
  "pageIndex":0,
  "title": "Project Timeline or Sprint Burndown UPDATED",
  "labels": ["2024-01-01", "2024-02-01", "2024-03-04"], 
  "datasets": [
    {
      "label": "Timeline Events UPDATED",
      "data": [
        { "x": "2024-01-01", "y": "Task A", "duration": 900 }, 
        { "x": "2024-02-01", "y": "Task B", "duration": 20 },
        { "x": "2027-03-11", "y": "Task F", "duration": 15 }
      ],
      "backgroundColor": "#36A2EE",
      "borderColor": "#36A2EE",
      "fill": true 
    },
    {
      "label": "Remaining Tasks UPDATED",
      "data": [
        { "x": "2024-01-01", "y": 777 },
        { "x": "2024-01-02", "y": 40 },
        { "x": "2024-01-03", "y": 30 },
        { "x": "2024-01-04", "y": 20 },
        { "x": "2024-01-05", "y": 10 },
        { "x": "2024-01-06", "y": 900 }
      ],
      "backgroundColor": "rgba(255,99,132,0.6)",
      "borderColor": "rgba(255,99,132,2)",
      "fill": false
    }
  ],
  "options": {
    "responsive": true,
    "scales": {
      "xAxes": [
        {
          "type": "time",
          "time": {
            "unit": "day"
          },
          "scaleLabel": {
            "display": true,
            "labelString": "Date"
          }
        }
      ],
      "yAxes": [
        {
          "type": "linear",
          "display": true,
          "scaleLabel": {
            "display": true,
            "labelString": "Value"
          },
          "ticks": {
            "beginAtZero": true,
            "reverse": true
          }
        }
      ]
    },
    "title": {
      "display": true,
      "text": "Timeline and Burndown Chart"
    },
    "legend": {
      "display": true,
      "position": "bottom"
    }
  }
}

Response:
{
    "timestamp": "13-Nov-2024 13:51:02 PM",
    "path": "/api/v1/custom-charts/update/timeline/67345fe9f50ca82738dbe10b",
    "status": 200,
    "success": true,
    "message": "Success",
    "error": "",
    "data": {
        "id": "67345fe9f50ca82738dbe10b"
    }
}



GET
http://localhost:8082/api/v1/custom-charts/get-by-id/gauge/67322d029780fd3ae13206ae
{
    "timestamp": "11-Nov-2024 22:02:59 PM",
    "path": "/api/v1/custom-charts/get-by-id/gauge/67322d029780fd3ae13206ae",
    "status": 200,
    "success": true,
    "message": "Success",
    "error": "",
    "data": {
        "id": "67322d029780fd3ae13206ae",
        "type": "gauge",
        "sketchbookId": "6731a424e273335735553187",
        "pageIndex": 0,
        "data": {
            "percent": 0.76
        },
        "options": {
            "id": "gauge-1",
            "nrOfLevels": 20,
            "colors": [
                "#FF5F6D",
                "#FFC371"
            ],
            "arcWidth": 0.3,
            "arcPadding": 0.02,
            "cornerRadius": 3,
            "needleColor": "#464A4F",
            "needleBaseColor": "#464A4F",
            "textColor": "#000000",
            "animate": true,
            "animDelay": 500,
            "animateDuration": 3000,
            "marginInPercent": 0.05,
            "formatTextValue": "value => `${Math.round(value * 100)}%`"
        },
        "width": 300,
        "height": 300
    }
}


Example #2
http://localhost:8082/api/v1/custom-charts/get-by-id/timeline/67345fe9f50ca82738dbe10b
{
    "timestamp": "13-Nov-2024 13:54:06 PM",
    "path": "/api/v1/custom-charts/get-by-id/timeline/67345fe9f50ca82738dbe10b",
    "status": 200,
    "success": true,
    "message": "Success",
    "error": "",
    "data": {
        "id": "67345fe9f50ca82738dbe10b",
        "type": "timeline",
        "sketchbookId": "6731a424e273335735553187",
        "pageIndex": 0,
        "title": "Project Timeline or Sprint Burndown UPDATED",
        "labels": [
            "2024-01-01",
            "2024-02-01",
            "2024-03-04"
        ],
        "datasets": [
            {
                "label": "Timeline Events UPDATED",
                "data": [
                    {
                        "x": "2024-01-01",
                        "y": "Task A",
                        "duration": 900
                    },
                    {
                        "x": "2024-02-01",
                        "y": "Task B",
                        "duration": 20
                    },
                    {
                        "x": "2027-03-11",
                        "y": "Task F",
                        "duration": 15
                    }
                ],
                "backgroundColor": "#36A2EE",
                "borderColor": "#36A2EE",
                "fill": true
            },
            {
                "label": "Remaining Tasks UPDATED",
                "data": [
                    {
                        "x": "2024-01-01",
                        "y": "777",
                        "duration": 0
                    },
                    {
                        "x": "2024-01-02",
                        "y": "40",
                        "duration": 0
                    },
                    {
                        "x": "2024-01-03",
                        "y": "30",
                        "duration": 0
                    },
                    {
                        "x": "2024-01-04",
                        "y": "20",
                        "duration": 0
                    },
                    {
                        "x": "2024-01-05",
                        "y": "10",
                        "duration": 0
                    },
                    {
                        "x": "2024-01-06",
                        "y": "900",
                        "duration": 0
                    }
                ],
                "backgroundColor": "rgba(255,99,132,0.6)",
                "borderColor": "rgba(255,99,132,2)",
                "fill": false
            }
        ],
        "options": {
            "responsive": true,
            "scales": {
                "xAxes": [
                    {
                        "type": "time",
                        "time": {
                            "unit": "day"
                        },
                        "scaleLabel": {
                            "display": true,
                            "labelString": "Date"
                        }
                    }
                ],
                "yAxes": [
                    {
                        "type": "linear",
                        "display": true,
                        "scaleLabel": {
                            "display": true,
                            "labelString": "Value"
                        },
                        "ticks": {
                            "beginAtZero": true,
                            "reverse": true
                        }
                    }
                ],
                "xaxes": [
                    {
                        "type": "time",
                        "time": {
                            "unit": "day"
                        },
                        "scaleLabel": {
                            "display": true,
                            "labelString": "Date"
                        }
                    }
                ],
                "yaxes": [
                    {
                        "type": "linear",
                        "display": true,
                        "scaleLabel": {
                            "display": true,
                            "labelString": "Value"
                        },
                        "ticks": {
                            "beginAtZero": true,
                            "reverse": true
                        }
                    }
                ]
            },
            "title": {
                "display": true,
                "text": "Timeline and Burndown Chart"
            },
            "legend": {
                "display": true,
                "position": "bottom"
            }
        }
    }
}

All chart types (15):
bar
line
area
pie
doughunut
radar
polararea
scatter
bubble
horizantalbar
timeline
burndown
gauge
gantt
textarea