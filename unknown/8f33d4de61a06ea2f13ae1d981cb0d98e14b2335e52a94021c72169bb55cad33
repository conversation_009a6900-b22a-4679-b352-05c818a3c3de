.chatInputContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--color-background-card);
  padding: 8px;
  border-radius: 24px;
  box-shadow: none;
  transition:
    all 0.3s ease,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
  margin: 0;
  max-width: 1200px;
  width: 80%;
  border: 1px solid var(--color-border);
}

.chatInputContainer:focus-within {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.attachment {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 42px;
  height: 42px;
  border-radius: 50%;
  cursor: pointer;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease;
  color: var(--color-text-secondary);
  margin-left: 4px;
}

.attachment:hover {
  background: var(--color-background-tertiary);
  color: var(--color-primary);
  transform: scale(1.05);
}

/* More specific input styles */
.chatInputContainer .inputStyles {
  flex: 1 !important;
  border: none !important;
  outline: none !important;
  font-size: 15px !important;
  line-height: 1.5 !important;
  padding: 8px 12px !important;
  background: transparent !important;
  color: var(--color-text-primary) !important;
  min-height: 24px !important;
  max-height: 120px !important;
  resize: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  transition: color 0.3s ease !important;
}

.chatInputContainer .inputStyles:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.chatInputContainer .inputStyles:focus-visible {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

.chatInputContainer .inputStyles::placeholder {
  color: var(--color-text-secondary) !important;
  font-weight: 400 !important;
  opacity: 0.7 !important;
  transition: color 0.3s ease !important;
}

.sendIconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 42px;
  height: 42px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #fff;
  background: var(--primary-color);
  margin-right: 4px;
}

.sendIconContainer:hover {
  transform: scale(1.05);
  background: var(--primary-light);
}

.sendIconContainer:active {
  transform: scale(0.95);
}

/* Drag & Drop Styles */
.dragActive {
  background: var(--color-background-tertiary);
  border: 2px dashed var(--color-primary);
  box-shadow: 0 0 0 4px rgba(var(--color-primary-rgb), 0.1);
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

/* Disabled State */
.chatInputContainer.disabled {
  opacity: 0.75;
  cursor: not-allowed;
  background: var(--color-background-tertiary);
  border: 2px solid var(--color-border);
}

/* Selected File Styles */
.selectedFile {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  background: var(--color-background-tertiary);
  border-radius: 20px;
  font-size: 13px;
  color: var(--color-text-primary);
  margin: 0 4px;
  max-width: 200px;
  height: 28px;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.selectedFile span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.selectedFile svg {
  cursor: pointer;
  font-size: 16px;
  transition:
    all 0.2s ease,
    color 0.3s ease;
  color: var(--color-text-secondary);
  min-width: 16px;
}

.selectedFile svg:hover {
  transform: scale(1.1);
  color: var(--color-error-main);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .chatInputContainer {
    margin: 4px;
    padding: 6px;
  }

  .attachment,
  .sendIconContainer {
    min-width: 38px;
    height: 38px;
  }

  .selectedFile {
    max-width: 150px;
    height: 26px;
    font-size: 12px;
  }
}
