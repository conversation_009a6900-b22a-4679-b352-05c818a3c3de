.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.inputGroup {
  margin-bottom: 15px;
}

.inputGroup label {
  display: block;
  margin-bottom: 5px;
}

.inputGroup input {
  width: 100%;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.buttonGroup button {
  padding: 5px 15px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.buttonGroup button:first-child {
  background-color: #4caf50;
  color: white;
}

.buttonGroup button:last-child {
  background-color: #f44336;
  color: white;
}
