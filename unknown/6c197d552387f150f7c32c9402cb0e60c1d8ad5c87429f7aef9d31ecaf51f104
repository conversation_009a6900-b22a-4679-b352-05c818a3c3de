import styles from './Loader.module.css';

interface LoaderTypes {
  type?: 'withtext';
  loading?: any;
}

const Loader = ({ type, loading }: LoaderTypes) => {
  console.log(loading, 'kakkakakkaka');
  return (
    <div className={styles.loader_container}>
      {type == 'withtext' && (
        <p className={styles.message}>
          Sit back while we analyzing your document...
        </p>
      )}
      <div className={styles.progressBar}>
        <div
          className={styles.progressBarInner}
          style={{ width: `${loading}%` }}
        ></div>
      </div>
    </div>
  );
};

export default Loader;
