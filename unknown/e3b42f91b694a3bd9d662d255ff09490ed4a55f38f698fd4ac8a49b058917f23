import React, { useState } from 'react';
import Activities, { ActivityItem } from './Activities'; 
import styles from './ActivitiesButton.module.css';
import ActivityIcon from '../../../assets/images/Activities.png';

const ActivitiesButton: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [activityData, setActivityData] = useState<ActivityItem[]>([
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON>",
      action: "Assigned Tasks",
      comment: "All Good",
      tasks: [
        { id: 1, text: "Task 1 added here", completed: false },
        { id: 2, text: "Task 2 added here", completed: false },
        { id: 3, text: "Task 3 added here", completed: false },
      ]
    },
    {
      id: 2,
      name: "<PERSON><PERSON> Ah",
      action: "Approved Document"
    },
    {
      id: 3,
      name: "<PERSON>asi<PERSON>",
      action: "Approved Document",
      comment: "All Good"
    },
    {
      id: 4,
      name: "<PERSON>h<PERSON> <PERSON><PERSON>",
      action: "Created Document",
      comment: "I have checked this document, please cross-check the revenue for the months March to September in the year 2023"
    }
  ]);

  const handleOpen = () => {
    setIsVisible(true);
  };

  const handleClose = () => {
    setIsVisible(false);
  };

  const handleTaskCompletionToggle = (activityId: number, taskId: number) => {
    setActivityData(prevData =>
      prevData.map(activity =>
        activity.id === activityId
          ? {
              ...activity,
              tasks: activity.tasks?.map(task =>
                task.id === taskId
                  ? { ...task, completed: !task.completed }
                  : task
              ),
            }
          : activity
      )
    );
  };

  const handleCompleteAllToggle = (activityId: number, completed: boolean) => {
    setActivityData(prevData =>
      prevData.map(activity =>
        activity.id === activityId
          ? {
              ...activity,
              tasks: activity.tasks?.map(task => ({
                ...task,
                completed,
              })),
            }
          : activity
      )
    );
  };

  return (
    <div>
      {!isVisible && (
        <button onClick={handleOpen} className={styles.activitiesButton}>
          <img src={ActivityIcon} alt="Activities" />
          Activities
        </button>
      )}
      {isVisible && (
        <Activities
          activities={activityData}
          onClose={handleClose}
          onToggleTaskCompletion={handleTaskCompletionToggle}
          onToggleCompleteAll={handleCompleteAllToggle} // Pass the complete all handler
        />
      )}
    </div>
  );
};

export default ActivitiesButton;
