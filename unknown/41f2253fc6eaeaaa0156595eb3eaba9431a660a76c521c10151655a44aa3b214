import React from 'react';
import styles from './DropdownButton.module.css';

interface ButtonProps {
  label?: string;
  onClick?: () => void;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  disabled?: boolean;
  style?: React.CSSProperties;
  buttonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>;
  isActive?: boolean;
}

const DropdownButton: React.FC<ButtonProps> = ({
  label,
  onClick,
  leftIcon,
  rightIcon,
  disabled = false,
  style,
  buttonProps,
  isActive = false,
}) => {
  return (
    <button
      className={`${styles.button} ${isActive ? styles.active : ''}`}
      onClick={onClick}
      disabled={disabled}
      style={style}
      {...buttonProps}
    >
      {leftIcon && <span className={styles.icon}>{leftIcon}</span>}
      <span>{label}</span>
      {rightIcon && <span className={styles.icon}>{rightIcon}</span>}
    </button>
  );
};

export default DropdownButton;
