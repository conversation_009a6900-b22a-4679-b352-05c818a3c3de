import { MdDashboard } from 'react-icons/md';
import { IoCheckmarkDoneCircle } from 'react-icons/io5';
import { BiTime } from 'react-icons/bi';
import { RiCloseCircleLine } from 'react-icons/ri';
import { FilterOption } from '../../types/workflow/index';
import styles from './WorkflowFilter.module.css';

export const filterOptions: FilterOption[] = [
  { id: 'all-projects', label: 'All Projects', icon: MdDashboard },
  { id: 'completed', label: 'Completed', icon: IoCheckmarkDoneCircle },
  { id: 'in-progress', label: 'In Progress', icon: BiTime },
  { id: 'rejected', label: 'Rejected', icon: RiCloseCircleLine },
];

interface WorkflowFilterProps {
  activeButton: string;
  workflowCount: (id: string) => number;
  onFilterChange: (id: string) => void;
}

const WorkflowFilter: React.FC<WorkflowFilterProps> = ({
  activeButton,
  workflowCount,
  onFilterChange,
}) => {
  return (
    <div className={styles.filterContainer}>
      {filterOptions.map((option) => (
        <button
          key={option.id}
          className={`${styles.filterButton} ${activeButton === option.id ? styles.active : ''}`}
          onClick={() => onFilterChange(option.id)}
        >
          <span className={styles.filterIcon}>
            <option.icon />
          </span>
          <span className={styles.filterLabel}>{option.label}</span>
          <span className={styles.filterCount}>{workflowCount(option.id)}</span>
        </button>
      ))}
    </div>
  );
};

export default WorkflowFilter;
