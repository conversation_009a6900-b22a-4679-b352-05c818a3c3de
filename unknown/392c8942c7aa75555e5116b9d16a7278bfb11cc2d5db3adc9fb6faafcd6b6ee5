.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.headingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 32px;
}

.heading {
  font-size: 32px;
  font-weight: 600;
  color: #174e86;
  margin: 0;
}

.subHeading {
  font-size: 16px;
  color: #666666;
  margin: 8px 0 0 0;
}

.formContainer {
  max-width: 400px;
  margin: 32px auto;
  padding: 32px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.linkContainer {
  text-align: center;
  color: #666666;
  margin: 16px 0;
}

.link {
  color: #174e86;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}
