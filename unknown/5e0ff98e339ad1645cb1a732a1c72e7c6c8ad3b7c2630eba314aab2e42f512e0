import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { BASE_URL } from '../services/config';
import defaultProfile from '../assets/images/svg/defaultProfile.svg';

export const useProfileImage = (user: any) => {
  const [profileImage, setProfileImage] = useState<string>('');
  const selectedImage = useSelector(
    (state: any) => state.profile.selectedImage
  );
  const { userDetails } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    const userProfileImage = user?.profileImgThumbnail
      ? `${user.profileImgThumbnail}?t=${new Date().getTime()}`
      : selectedImage || userDetails?.profileImgThumbnail || defaultProfile;

    setProfileImage(userProfileImage);
  }, [user, selectedImage, userDetails]);

  const getFullImageUrl = (imagePath: string | null) => {
    if (!imagePath) return defaultProfile;
    return imagePath.startsWith('http') ? imagePath : `${BASE_URL}${imagePath}`;
  };

  const getChatThumbnail = (user: any) => {
    if (!user) return defaultProfile;
    return user.profileImgThumbnail
      ? getFullImageUrl(user.profileImgThumbnail)
      : defaultProfile;
  };

  return {
    profileImage,
    setProfileImage,
    getFullImageUrl,
    getChatThumbnail,
    defaultProfile,
  };
};
