import { Participant } from '../../types/workflow/index';
import styles from './WorkflowMessages.module.css';

interface WorkflowMessagesProps {
  participants: Participant[];
}

const WorkflowMessages: React.FC<WorkflowMessagesProps> = ({
  participants,
}) => {
  return (
    <div className={styles.participantMessages}>
      {participants.map(
        (participant) =>
          participant.status && (
            <div key={participant.userId} className={styles.message}>
              <strong>{participant.name}:</strong>
              {participant.approval_message && (
                <div>✓ {participant.approval_message}</div>
              )}
              {participant.rejection_message && (
                <div>✕ {participant.rejection_message}</div>
              )}
              {participant.conditional_approved_message && (
                <div>⚠ {participant.conditional_approved_message}</div>
              )}
            </div>
          )
      )}
    </div>
  );
};

export default WorkflowMessages;
