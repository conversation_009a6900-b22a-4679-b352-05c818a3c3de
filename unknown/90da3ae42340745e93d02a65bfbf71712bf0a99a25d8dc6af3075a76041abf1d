.noteLabel {
  font-size: 16px;
  display: block;
  padding: 20px;
  color: #0e2f51;
  font-family: Lato;
  font-weight: 600;
  line-height: 22px;
  text-align: left;
}

.noteInput {
  width: 630px;
  height: 85px;
  padding: 10px;
  border: 1px solid #d1dfec;
  border-radius: 8px;
  font-size: 16px;
  resize: none;
}
.textarea {
  text-align: center;
  padding-bottom: 0px;
}

.modalFooter {
  margin-top: 20px;
  text-align: right;
}

.modalHeader h2 {
  font-family: Lato;
  font-size: 20px;
  font-weight: 600;
  line-height: 28px;
  padding: 10px;
  text-align: left;
  color: #292E33;
  border: 1px solid #D1DFEC
  }

.addTasksSection {
  padding-left: 10px;
}

.taskList {
  padding: 20px;
  padding-top: 0px;
}

.taskItem {
  display: flex;
  align-items: center;
}

.taskItem input[type='checkbox'] {
  width: 15px;
  height: 15px;
  margin-right: 10px;
  border-color: #292e33;
  accent-color: #1B5EA1;
  font-size: 16px;
}

.taskItem span {
  flex-grow: 1;
  font-family: Lato, sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #292e33;
  padding: 5px;
}

.removeTask {
  background: none;
  border: none;
  color: #292e33;
  cursor: pointer;
  font-size: 16px;
}

.removeTask img {
  width: 18px;
  height: 19px;
}

.newTaskInput {
  display: flex;
  margin-top: 10px;
  align-items: center;
}

.newTaskInput input {
  flex-grow: 1;
  padding: 5px 10px;
  border: 1px solid #D1DFEC;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
}

.addNewTask button {
  background: none;
  border: none;
  padding-top: 5px;
  color: #677480;
  cursor: pointer;
  font-size: 16px;
  font-weight: 400;
  text-decoration: none;
}

.addNewTask span {
  margin-left: 10px;
}

.addTaskButton {
  background-color: #0E2F51;
  color: white;
}
.cancelButton {
  background-color: #f0f0f0;
  color: #677480;
}

.addTaskButton, .cancelButton {
  margin-left: 10px;
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.addNewTask button img {
  width: 14px;
  height: 14px;
  border-radius: 3px;
}

.modalFooter img:active{
  cursor: pointer;
  color: #D1DFEC;
}

.defaultIcon {
  background-color: none; 
  
}

.clickedIcon {
  background-color: #ffffff;
  border-radius: 4px;
}
