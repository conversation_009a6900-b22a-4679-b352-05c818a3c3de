/**
 * Global dark mode transition styles
 * 
 * This file contains global transition styles to ensure smooth transitions
 * between light and dark modes across all components.
 */

/* Apply transitions to common elements */
body,
button,
input,
textarea,
select,
a,
p,
h1, h2, h3, h4, h5, h6,
div,
span,
li,
table,
tr,
td,
th {
  transition: 
    color 0.3s ease,
    background-color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

/* Ensure MUI components transition smoothly */
.MuiPaper-root,
.MuiCard-root,
.MuiAppBar-root,
.MuiDrawer-paper,
.MuiDialog-paper,
.MuiPopover-paper,
.MuiMenu-paper,
.MuiList-root,
.MuiListItem-root,
.MuiButton-root,
.MuiIconButton-root,
.MuiInputBase-root,
.MuiOutlinedInput-root,
.MuiFilledInput-root,
.MuiInputLabel-root,
.MuiFormHelperText-root,
.MuiChip-root,
.MuiAvatar-root,
.MuiTooltip-tooltip,
.MuiAlert-root,
.MuiSnackbar-root,
.MuiTableCell-root,
.MuiTableRow-root,
.MuiTableHead-root,
.MuiTableBody-root,
.MuiTableFooter-root,
.MuiTablePagination-root,
.MuiTabs-root,
.MuiTab-root,
.MuiAccordion-root,
.MuiAccordionSummary-root,
.MuiAccordionDetails-root,
.MuiSwitch-root,
.MuiCheckbox-root,
.MuiRadio-root,
.MuiSlider-root,
.MuiDivider-root,
.MuiBadge-root,
.MuiCircularProgress-root,
.MuiLinearProgress-root,
.MuiSkeleton-root {
  transition: 
    color 0.3s ease,
    background-color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease !important;
}

/* Ensure AG Grid components transition smoothly */
.ag-theme-alpine,
.ag-theme-alpine-dark,
.ag-header,
.ag-header-cell,
.ag-header-group-cell,
.ag-cell,
.ag-row,
.ag-row-odd,
.ag-row-even,
.ag-floating-top,
.ag-floating-bottom,
.ag-body-viewport,
.ag-center-cols-viewport,
.ag-center-cols-container,
.ag-header-container,
.ag-body-horizontal-scroll,
.ag-body-vertical-scroll {
  transition: 
    color 0.3s ease,
    background-color 0.3s ease,
    border-color 0.3s ease !important;
}

/* Ensure ReactFlow components transition smoothly */
.react-flow,
.react-flow__node,
.react-flow__edge,
.react-flow__edge-path,
.react-flow__handle,
.react-flow__controls,
.react-flow__background,
.react-flow__minimap,
.react-flow__attribution {
  transition: 
    color 0.3s ease,
    background-color 0.3s ease,
    border-color 0.3s ease,
    fill 0.3s ease,
    stroke 0.3s ease !important;
}

/* Ensure Chart.js components transition smoothly */
canvas {
  transition: background-color 0.3s ease !important;
}
