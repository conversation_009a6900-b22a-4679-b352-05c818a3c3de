import React, { useState } from 'react';
import styles from '../ChartPropertyController.module.css';
import { useUpdateCustomChartsMutation } from '../../../../services/sketchbookServices';
import { chartPayloadHandler } from '../utils/chartPayloadHandler';

interface ChartSettingsControlsProps {
  selectedChart: any;
  onChartUpdate: (chart: any) => void;
  sketchbookId: string;
  activePage: any;
}

export const ChartSettingsControls: React.FC<ChartSettingsControlsProps> = ({
  selectedChart,
  onChartUpdate,
  sketchbookId,
  activePage,
}) => {
  const hasAxes = [
    'bar',
    'line',
    'area',
    'radar',
    'bubble',
    'scatter',
    'horizontalBar',
  ].includes(selectedChart.type);

  const [updateCustomCharts] = useUpdateCustomChartsMutation();

  const isRadialChart = ['pie', 'doughnut', 'polarArea'].includes(
    selectedChart.type
  );

  const [updatedChart, setUpdatedChart] = useState(selectedChart);

  const handleSettingChange = (setting: string, value: string) => {
    const updatedChart = JSON.parse(JSON.stringify(selectedChart));

    if (!updatedChart.options) {
      updatedChart.options = {};
    }

    switch (setting) {
      case 'title':
        if (!updatedChart.options.plugins) {
          updatedChart.options.plugins = {};
        }
        if (!updatedChart.options.plugins.title) {
          updatedChart.options.plugins.title = {};
        }
        updatedChart.options.plugins.title.text = value;
        updatedChart.options.plugins.title.display = value.length > 0;
        break;
      case 'xAxis':
        if (!updatedChart.options.scales) {
          updatedChart.options.scales = {};
        }
        if (!updatedChart.options.scales.x) {
          updatedChart.options.scales.x = {};
        }
        updatedChart.options.scales.x.title = {
          ...updatedChart.options.scales.x.title,
          display: value.length > 0,
          text: value,
        };
        break;
      case 'yAxis':
        if (!updatedChart.options.scales) {
          updatedChart.options.scales = {};
        }
        if (!updatedChart.options.scales.y) {
          updatedChart.options.scales.y = {};
        }
        updatedChart.options.scales.y.title = {
          ...updatedChart.options.scales.y.title,
          display: value.length > 0,
          text: value,
        };
        break;
      case 'legendTitle':
        if (!updatedChart.options.plugins) {
          updatedChart.options.plugins = {};
        }
        if (!updatedChart.options.plugins.legend) {
          updatedChart.options.plugins.legend = {};
        }
        updatedChart.options.plugins.legend.title = {
          ...updatedChart.options.plugins.legend.title,
          display: value.length > 0,
          text: value,
        };
        break;
    }
    setUpdatedChart(updatedChart);
    onChartUpdate(updatedChart);
  };

  const handleSettingBlur = async (e: any) => {
    const updatedChartPayload = chartPayloadHandler(updatedChart, 'timeline', sketchbookId, activePage);
    await updateCustomCharts({
      id: selectedChart.id,
      payload: updatedChartPayload,
      chartType: 'timeline'
    }).unwrap();
  };

  const getAxisLabel = () => {
    if (selectedChart.type === 'horizontalBar') {
      return {
        x: 'Y-Axis Label:',
        y: 'X-Axis Label:',
      };
    }
    return {
      x: 'X-Axis Label:',
      y: 'Y-Axis Label:',
    };
  };

  const axisLabels = getAxisLabel();

  return (
    <>
    <div></div>
   {/*  <div className={styles.section}>
      <div className={styles.heading}>Chart Settings</div>
      <div className={styles.settingsGrid}>
        <div className={styles.settingItem}>
          <label>Chart Title:</label>
          <input
            type="text"
            value={selectedChart.options?.plugins?.title?.text || ''}
            onChange={(e) => handleSettingChange('title', e.target.value)}
            placeholder="Enter chart title"
          />
        </div>

        {isRadialChart && (
          <div className={styles.settingItem}>
            <label>Legend Title:</label>
            <input
              type="text"
              value={selectedChart.data.datasets[0].label || ''}
              onChange={(e) =>
                handleSettingChange('legendTitle', e.target.value)
              }
              placeholder="Enter legend title"
            />
          </div>
        )}

        {hasAxes && (
          <>
            <div className={styles.settingItem}>
              <label>{axisLabels.x}</label>
              <input
                type="text"
                value={selectedChart.options?.scales?.x?.title?.text || ''}
                onChange={(e) => handleSettingChange('xAxis', e.target.value)}
                placeholder="Enter axis label"
                onBlur={handleSettingBlur}
              />
            </div>
            <div className={styles.settingItem}>
              <label>{axisLabels.y}</label>
              <input
                type="text"
                value={selectedChart.options?.scales?.y?.title?.text || ''}
                onChange={(e) => handleSettingChange('yAxis', e.target.value)}
                onBlur={handleSettingBlur}
                placeholder="Enter axis label"
              />
            </div>
          </>
        )}
      </div> 
    </div>*/}
    </>
  );
};
