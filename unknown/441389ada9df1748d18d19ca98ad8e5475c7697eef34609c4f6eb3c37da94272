@import url('https://fonts.googleapis.com/css2?family=Lato:wght@400&display=swap');
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
}

body {
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

/* Loading spinner for lazy-loaded components */
.page-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: var(--color-background-primary);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  font-size: 1.5rem;
  color: var(--color-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.page-loader::after {
  content: '';
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-background-tertiary);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
  transition: border-color 0.3s ease;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
