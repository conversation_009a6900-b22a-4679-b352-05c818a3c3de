import { utils, writeFile } from 'xlsx';

export const exportToExcel = (
  formatOnly: boolean,
  selectedChart: any,
  getRowData: () => any[]
) => {
  if (!selectedChart?.data?.datasets) return;

  const rowData = getRowData();

  // Prepare the worksheet data
  const wsData = rowData.map((row: any) => {
    if (formatOnly) {
      // Export format only (headers with empty data)
      return {
        Label: '',
        ...(selectedChart.type === 'bubble'
          ? { X: '', Y: '', Radius: '' }
          : selectedChart.type === 'scatter'
            ? { X: '', Y: '' }
            : selectedChart.type === 'line' && row.x instanceof Date
              ? { Date: '', Value: '' }
              : { Value: '' }),
        BorderColor: '',
        FillColor: '',
      };
    }

    // Export with data
    const baseRow = {
      Label: row.label,
      BorderColor: row.borderColor,
      FillColor: row.backgroundColor,
    };

    if (selectedChart.type === 'bubble') {
      return {
        ...baseRow,
        X: row.x,
        Y: row.y,
        Radius: row.r,
      };
    } else if (selectedChart.type === 'scatter') {
      return {
        ...baseRow,
        X: row.x,
        Y: row.y,
      };
    } else if (selectedChart.type === 'line' && row.x instanceof Date) {
      return {
        ...baseRow,
        Date: row.x,
        Value: row.y,
      };
    } else {
      return {
        ...baseRow,
        Value: row.value,
      };
    }
  });

  // Create worksheet
  const ws = utils.json_to_sheet(formatOnly ? [wsData[0]] : wsData);

  // Create workbook
  const wb = utils.book_new();
  utils.book_append_sheet(wb, ws, 'Chart Data');

  // Generate filename
  const filename = `${selectedChart.type}_${formatOnly ? 'format' : 'data'}_${new Date().toISOString().split('T')[0]}.xlsx`;

  // Export to file
  writeFile(wb, filename);
};
