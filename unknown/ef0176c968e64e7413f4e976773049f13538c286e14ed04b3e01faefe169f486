const createGraphData = (type: GraphType): Graph => {
  const labels = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  const data = generateRandomData(12); // Generates 12 random data points

  const commonOptions = {
    responsive: true,
    maintainAspectRatio: false,
  };

  switch (type) {
    case 'Bar':
      return {
        id: `bar-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [{ data, label: 'Sales', backgroundColor: [...colors] }],
        },
        options: commonOptions,
      };
    case 'Line':
      return {
        id: `line-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [
            { data, label: 'Trend', borderColor: 'rgba(197, 170, 255)' },
          ],
        },
        options: commonOptions,
      };
    case 'Pie':
    case 'Doughnut':
      return {
        id: `${type.toLowerCase()}-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [{ data, backgroundColor: [...colors] }],
        },
        options: { ...commonOptions, plugins: { legend: { display: false } } },
      };
    case 'Gauge':
      return {
        id: `gauge-${Date.now()}`,
        type,
        data: {
          labels,
          datasets: [
            {
              data: [...gaugeData],
              circumference: 180,
              rotation: 270,
              backgroundColor: [...colors],
            },
          ],
        },
        options: {
          ...commonOptions,
          cutout: '75%',
          plugins: { legend: { display: false } },
        },
      };
    case 'Bubble':
      return {
        id: `bubble-${Date.now()}`,
        type,
        data: {
          datasets: [
            {
              data: data.map((d) => ({
                x: randomX(),
                y: randomY(),
                r: d / 10,
              })),
            },
          ],
        },
        options: commonOptions,
      };
    case 'Scatter':
      return {
        id: `scatter-${Date.now()}`,
        type,
        data: {
          datasets: [
            { data: data.map(() => ({ x: randomX(), y: randomY() })) },
          ],
        },
        options: commonOptions,
      };
    default:
      throw new Error(`Unsupported graph type: ${type}`);
  }
};
