import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './baseQuery';
import {
  GET_PROFILE_IMAGE,
  UPDATE_USER,
  UPDATE_USER_PROFILE_IMAGE,
  CHANGE_PASSWORD,
  UPDATE_USER_BY_ADMIN,
  LOGOUT,
  GET_ALL_USERS_PAGINATED,
} from './constants/userMgtServiceConstants';

export const userMgtService = createApi({
  reducerPath: 'userMgtService',
  baseQuery: baseQuery,
  endpoints: (builder) => ({
    updateUser: builder.mutation<void, { userId: string; userData: any }>({
      query: ({ userId, userData }) => ({
        url: UPDATE_USER + userId,
        method: 'PUT',
        body: userData,
        headers: {
          'Content-Type': 'application/json',
        },
      }),
    }),
    updateUserProfileImage: builder.mutation<
      void,
      { userId: string; imageData: FormData }
    >({
      query: ({ userId, imageData }) => ({
        url: UPDATE_USER_PROFILE_IMAGE + userId,
        method: 'POST',
        body: imageData,
      }),
    }),
    getProfileImage: builder.query<any, string>({
      query: (userId) => ({
        url: GET_PROFILE_IMAGE + userId,
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
    }),
    changePassword: builder.mutation<
      void,
      { userId: string; oldPassword: string; newPassword: string }
    >({
      query: ({ userId, oldPassword, newPassword }) => ({
        url: CHANGE_PASSWORD + userId,
        method: 'PUT',
        body: { oldPassword, newPassword },
      }),
    }),
    updateUserByAdmin: builder.mutation<
      void,
      { userId: string; userData: any }
    >({
      query: ({ userId, userData }) => ({
        url: UPDATE_USER_BY_ADMIN + userId,
        method: 'PUT',
        body: userData,
        headers: {
          'Content-Type': 'application/json',
        },
      }),
    }),
    logout: builder.mutation<void, void>({
      query: () => ({
        url: LOGOUT,
        method: 'POST',
      }),
    }),
    getUsersPaginated: builder.query<any, { page: number; size: number }>({
      query: ({ page, size }) => ({
        url: `${GET_ALL_USERS_PAGINATED}?page=${page}&size=${size}`,
        method: 'GET',
      }),
    }),
  }),
});

export const {
  useUpdateUserMutation,
  useUpdateUserProfileImageMutation,
  useChangePasswordMutation,
  useGetProfileImageQuery,
  useGetUsersPaginatedQuery,
  useUpdateUserByAdminMutation,
  useLogoutMutation,
} = userMgtService;
