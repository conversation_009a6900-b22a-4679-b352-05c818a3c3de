import React, { useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  Typography,
  IconButton,
  CircularProgress,
  Tooltip,
  Box,
  Menu,
  MenuItem,
  Paper,
  DialogActions,
  Button,
} from '@mui/material';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { FiZoomIn, FiZoomOut, FiMaximize2 } from 'react-icons/fi';
import { FaPrint } from 'react-icons/fa';
import {
  Close as CloseIcon,
  Print as PrintIcon,
  FileDownload as DownloadIcon,
  Email as EmailIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';
import CustomButton from '../../button/CustomButton';
import PreviewChart from './PreviewChart';
import styles from '../BreadCrumbsComponent.module.css';

interface PreviewDialogProps {
  open: boolean;
  onClose: () => void;
  pages: any[];
  charts: Record<string, any[]>;
  layouts: Record<string, any[]>;
  pageSize: {
    value: string;
    label: string;
    width: number;
    height: number;
    orientation: 'portrait' | 'landscape';
  };
  isExporting: boolean;
  pendingExport: 'PDF' | 'DOC' | null;
  onPrint: () => void;
  onExport: (type: 'email' | 'saveToDisk') => void;
  projectTitle?: string;
}

const PreviewDialog: React.FC<PreviewDialogProps> = ({
  open,
  onClose,
  pages,
  charts,
  layouts,
  pageSize,
  isExporting,
  pendingExport,
  onPrint,
  onExport,
  projectTitle,
}) => {
  const [zoomLevel, setZoomLevel] = React.useState(1);
  const [menuAnchorEl, setMenuAnchorEl] = React.useState<null | HTMLElement>(
    null
  );
  const menuOpen = Boolean(menuAnchorEl);

  const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleZoomIn = () => {
    setZoomLevel((prev) => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoomLevel((prev) => Math.max(prev - 0.1, 0.5));
  };

  const handlePrint = () => {
    if (isExporting) return;
    onPrint();
  };

  const handleExport = (type: 'email' | 'saveToDisk') => {
    if (isExporting) return;
    handleMenuClose();
    onExport(type);
  };

  // Reset zoom level when dialog opens
  React.useEffect(() => {
    if (open) {
      setZoomLevel(1);
    }
  }, [open]);

  // Calculate initial scale based on page size
  const calculateInitialScale = () => {
    // Match the exact same calculation used in DropableEditor
    const containerWidth = window.innerWidth * 0.8;
    const containerHeight = window.innerHeight * 0.77; // Adjusted to match DropableEditor 77vh

    const scaleX = containerWidth / pageSize.width;
    const scaleY = containerHeight / pageSize.height;

    // Use the smaller scale to ensure the entire page fits
    let scale = Math.min(scaleX, scaleY);

    // For very large pages, scale down a bit more initially
    if (pageSize.width >= 2000) {
      scale = Math.min(scale, 0.15);
    }

    return scale;
  };

  return (
    <Dialog
      open={open}
      onClose={isExporting ? undefined : onClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: {
          height: '90vh',
          maxHeight: '90vh',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          borderBottom: '1px solid #e0e0e0',
        }}
        className={styles.previewHeader}
      >
        <Typography variant="h6" sx={{ fontWeight: 500 }}>
          {projectTitle ? `Preview: ${projectTitle}` : 'Document Preview'}
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          {isExporting && (
            <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
              <CircularProgress size={24} sx={{ mr: 1 }} />
              <Typography variant="body2" sx={{ color: '#666' }}>
                {pendingExport
                  ? `Preparing ${pendingExport}...`
                  : 'Preparing document...'}
              </Typography>
            </Box>
          )}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              bgcolor: '#f5f5f5',
              borderRadius: 1,
              p: 0.5,
            }}
            className={styles.zoomControls}
          >
            <Tooltip title="Zoom out">
              <IconButton
                size="small"
                onClick={handleZoomOut}
                disabled={isExporting}
              >
                <FiZoomOut />
              </IconButton>
            </Tooltip>
            <Typography
              variant="body2"
              sx={{ mx: 1, fontWeight: 500, userSelect: 'none' }}
            >
              {Math.round(zoomLevel * 100)}%
            </Typography>
            <Tooltip title="Zoom in">
              <IconButton
                size="small"
                onClick={handleZoomIn}
                disabled={isExporting}
              >
                <FiZoomIn />
              </IconButton>
            </Tooltip>
          </Box>
          <Tooltip title="Close preview">
            <IconButton
              edge="end"
              onClick={onClose}
              disabled={isExporting}
              size="small"
            >
              <CloseIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Keep the menu definition for export options */}
      <Menu
        anchorEl={menuAnchorEl}
        open={menuOpen}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={() => handleExport('saveToDisk')}>
          <DownloadIcon fontSize="small" sx={{ mr: 1 }} />
          Download as PDF
        </MenuItem>
        <MenuItem onClick={() => handleExport('email')}>
          <EmailIcon fontSize="small" sx={{ mr: 1 }} />
          Email as PDF
        </MenuItem>
      </Menu>

      <DialogContent
        sx={{
          p: 0,
          overflow: 'auto',
          flex: 1,
          bgcolor: '#f5f5f5',
        }}
        className={styles.previewDialogContent}
      >
        <TransformWrapper
          initialScale={calculateInitialScale()}
          minScale={0.1}
          maxScale={2}
          centerOnInit={true}
          wheel={{
            step: 0.1,
            activationKeys: ['Control'],
          }}
          panning={{
            disabled: false,
            velocityDisabled: true,
            activationKeys: ['Control'],
          }}
        >
          {({ zoomIn, zoomOut, resetTransform }) => (
            <>
              <div className={styles.zoomControls}>
                <Tooltip title="Zoom Out (Ctrl + Scroll Down)" placement="top">
                  <IconButton
                    onClick={() => zoomOut()}
                    style={{
                      backgroundColor: '#f0f4f8',
                      color: '#174E86',
                    }}
                  >
                    <FiZoomOut />
                  </IconButton>
                </Tooltip>
                <Typography
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '0 12px',
                    color: '#174E86',
                    fontWeight: 500,
                  }}
                >
                  Zoom
                </Typography>
                <Tooltip title="Zoom In (Ctrl + Scroll Up)" placement="top">
                  <IconButton
                    onClick={() => zoomIn()}
                    style={{
                      backgroundColor: '#f0f4f8',
                      color: '#174E86',
                    }}
                  >
                    <FiZoomIn />
                  </IconButton>
                </Tooltip>
                <div
                  style={{
                    width: '1px',
                    backgroundColor: '#e0e0e0',
                    margin: '0 8px',
                  }}
                />
                <Tooltip title="Reset Zoom" placement="top">
                  <IconButton
                    onClick={() => resetTransform()}
                    style={{
                      backgroundColor: '#f0f4f8',
                      color: '#174E86',
                    }}
                  >
                    <FiMaximize2 />
                  </IconButton>
                </Tooltip>
              </div>

              <TransformComponent
                wrapperStyle={{
                  width: '100%',
                  height: 'calc(90vh - 100px)',
                  overflow: 'auto',
                }}
                contentStyle={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}
              >
                <div className={styles.previewContainer}>
                  {pages.map((page: any, index: number) => {
                    // Grid dimensions based on orientation
                    const gridCols =
                      pageSize.orientation === 'portrait' ? 12 : 20;
                    const gridRows =
                      pageSize.orientation === 'portrait' ? 20 : 12;

                    // Calculate row height exactly like in DropableEditor
                    const rowHeight = Math.ceil(pageSize.height / gridRows) - 6;
                    const colWidth = pageSize.width / gridCols;

                    // Find the maximum bottom position of all charts to ensure proper page height
                    // Default to page height if no charts
                    let maxBottom = pageSize.height;

                    if (
                      charts[page.id] &&
                      layouts[page.id] &&
                      layouts[page.id].length > 0
                    ) {
                      layouts[page.id].forEach((layout: any) => {
                        // Calculate positions and dimensions exactly as in DropableEditor
                        const top = layout.y * rowHeight;
                        const height = layout.h * rowHeight;
                        // Calculate the bottom position of this chart
                        const bottom = top + height + 10; // Add margin to match editor

                        // Update maxBottom if this chart extends lower
                        if (bottom > maxBottom) {
                          maxBottom = bottom;
                        }
                      });
                    }

                    // Ensure the page expands to fit all charts
                    const adjustedHeight = Math.max(
                      pageSize.height,
                      maxBottom + 40
                    );

                    return (
                      <div key={page.id} className={styles.previewPage}>
                        <div className={styles.pageHeader}>
                          <span className={styles.pageNumber}>
                            Page {index + 1}
                          </span>
                          <h3 className={styles.pageName}>{page.name}</h3>
                        </div>
                        <div
                          className={styles.pageContent}
                          style={{
                            width: `${pageSize.width}px`,
                            minHeight: `${pageSize.height}px`,
                            height: 'auto', // Allow height to grow
                            backgroundColor: 'white',
                            border: '1px solid #ddd',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                            margin: '0 auto 30px auto',
                            position: 'relative',
                            transformOrigin: 'top center',
                            overflow: 'visible', // Ensure content isn't clipped
                          }}
                        >
                          <div
                            className={styles.gridContainer}
                            style={{
                              display: 'grid',
                              gridTemplateColumns: `repeat(${gridCols}, 1fr)`,
                              gridAutoRows: `${rowHeight}px`,
                              width: '100%',
                              height: '100%',
                              position: 'relative',
                              gap: '0px',
                            }}
                          >
                            {charts[page.id]?.map((chart: any) => {
                              const layout = layouts[page.id]?.find(
                                (l: any) => l.i === chart.id
                              );
                              if (!layout) return null;

                              // Calculate column width and row height exactly as in DropableEditor
                              const rowHeight =
                                Math.ceil(pageSize.height / gridRows) - 6;
                              const colWidth = pageSize.width / gridCols;

                              return (
                                <div
                                  key={chart.id}
                                  className={styles.chartContainer}
                                  style={{
                                    gridColumn: `${layout.x + 1} / span ${layout.w}`,
                                    gridRow: `${layout.y + 1} / span ${layout.h}`,
                                    border: '1px solid #eaeaea',
                                    borderRadius: '6px',
                                    backgroundColor: '#ffffff',
                                    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
                                    padding: '4px',
                                    overflow: 'hidden',
                                    // Use z-index for proper stacking without absolute positioning
                                    zIndex: Math.floor(layout.y * 10) + 1,
                                  }}
                                >
                                  <div
                                    className={styles.chartPreviewWrapper}
                                    style={{
                                      width: '100%',
                                      height: '100%',
                                      overflow: 'hidden',
                                    }}
                                  >
                                    <div
                                      className={styles.chartInnerWrapper}
                                      style={{
                                        width: '100%',
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                      }}
                                    >
                                      <PreviewChart
                                        chart={chart}
                                        width={layout.w * colWidth - 10}
                                        height={layout.h * rowHeight - 10}
                                      />
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </TransformComponent>
            </>
          )}
        </TransformWrapper>
      </DialogContent>
      {isExporting && (
        <Paper
          elevation={3}
          sx={{
            position: 'absolute',
            bottom: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            p: 2,
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            minWidth: '250px',
          }}
        >
          <CircularProgress size={24} />
          <Box>
            <Typography variant="body2" fontWeight={500}>
              {pendingExport
                ? `Preparing ${pendingExport}...`
                : 'Preparing for print...'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Please wait, this may take a moment
            </Typography>
          </Box>
        </Paper>
      )}
      <DialogActions
        sx={{
          px: 3,
          py: 1.5,
          borderTop: '1px solid #e0e0e0',
        }}
      >
        <Button
          onClick={onClose}
          disabled={isExporting}
          variant="outlined"
          size="medium"
        >
          Close
        </Button>
        <Button
          onClick={handlePrint}
          disabled={isExporting}
          variant="contained"
          startIcon={<PrintIcon />}
          size="medium"
        >
          Print
        </Button>
        <Button
          disabled={isExporting}
          variant="contained"
          color="primary"
          startIcon={<DownloadIcon />}
          size="medium"
          aria-label="Export PDF"
          aria-haspopup="true"
          endIcon={<MoreVertIcon />}
          onClick={handleMenuClick}
        >
          Export PDF
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PreviewDialog;
