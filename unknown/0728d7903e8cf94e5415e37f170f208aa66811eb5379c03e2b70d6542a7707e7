.button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary,
.secondary {
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
}

.primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

/* .secondary {
  background-color: var(--color-primary-dark);
  color: var(--color-secondary-contrast);
} */

.secondary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.header {
  background-color: transparent;
  color: var(--color-text-primary);
  padding: var(--spacing-2);
}

.header:hover:not(:disabled) {
  background-color: var(--color-grey-100);
}

.button:disabled {
  background-color: var(--color-grey-300);
  color: var(--color-grey-500);
  cursor: not-allowed;
}

.active {
  background-color: var(--color-primary-dark);
  color: var(--color-primary-contrast);
}

.icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* .icon svg {
  transition: stroke 0.5s;
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.header:hover .icon svg,
.header.active .icon svg {
  stroke: blue;
  fill: blue;
} */
