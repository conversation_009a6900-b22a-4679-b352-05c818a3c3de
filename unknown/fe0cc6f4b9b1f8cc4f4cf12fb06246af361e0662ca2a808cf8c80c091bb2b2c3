.filterContainer {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  width: 100%;
}

.filterButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  background: var(--color-background-tertiary);
  cursor: pointer;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease;
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-right: 8px;
}

.filterButton:hover {
  background: var(--color-background-hover);
  transform: translateY(-1px);
}

.filterButton.active {
  background: var(--color-primary);
  color: var(--color-primary-contrast);
}

.filterButton.active .filterIcon {
  color: var(--color-primary-contrast);
}

.filterIcon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  font-size: 18px;
}

.filterLabel {
  font-weight: 500;
}

.filterCount {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-left: 4px;
}

.filterButton.active .filterCount {
  background: rgba(255, 255, 255, 0.2);
}
