import React, { useState } from 'react';
import styles from './ConditionalApprove.module.css';
import CustomButton from '../../../common/button/CustomButton';
import ConditionalApproveIcon from '../../../../assets/images/svg/conditionalApprove.svg';
import AddTaskIcon from '../../../../assets/images/svg/AddTask.svg';
import DeleteIcon from '../../../../assets/images/svg/Delete.svg';

const ConditionalApprove: React.FC = () => {
  const [tasks, setTasks] = useState<string[]>([
    'Please check the document again and add bar charts instead of Pie charts',
    'Make the text bold at important places rather than subheadings',
    'The document is not aligned properly align it properly',
  ]);
  const [showNewTaskInput, setShowNewTaskInput] = useState(false);
  const [newTaskText, setNewTaskText] = useState('');
  const [buttonType, setButtonType] = useState<'primary' | 'secondary' | 'header'>('secondary');
  const [isClicked, setIsClicked] = useState(false);
  const handleClick = () => {
    setButtonType('primary');
    setIsClicked(true);  // Set clicked state to true
    console.log('Conditional Approve Button Clicked');
  };
  const addTask = () => {
    if (newTaskText.trim()) {
      setTasks([...tasks, newTaskText.trim()]);
      setNewTaskText('');
      setShowNewTaskInput(false);
    }
  };

  const removeTask = (index: number) => {
    setTasks(tasks.filter((_, i) => i !== index));
  };

  return (
    <div className={styles.modal}>
      <div className={styles.modalContent}>
        <div className={styles.modalHeader}>
          <h2>Conditional Approve</h2>
        </div>
        <label className={styles.noteLabel}>Add Note</label>
        <div className={styles.textarea}>
          <textarea
            className={styles.noteInput}
            placeholder="Enter your note here..."
          />
        </div>
        <label className={styles.noteLabel}>Add Tasks</label>
        <div className={styles.addTasksSection}>
          <div className={styles.taskList}>
            {tasks.map((task, index) => (
              <div key={index} className={styles.taskItem}>
                <input type="checkbox" />
                <span>{task}</span>
                <button
                  onClick={() => removeTask(index)}
                  className={styles.removeTask}
                >
                  {' '}
                  <img src={DeleteIcon} alt="Delete" />{' '}
                </button>
              </div>
            ))}
            {showNewTaskInput ? (
              <div className={styles.newTaskInput}>
                <input
                  type="text"
                  value={newTaskText}
                  onChange={(e) => setNewTaskText(e.target.value)}
                  placeholder="Enter new task here..."
                />
                <button onClick={addTask} className={styles.addTaskButton}>
                  Add
                </button>
                <button
                  onClick={() => setShowNewTaskInput(false)}
                  className={styles.cancelButton}
                >
                  Cancel
                </button>
              </div>
            ) : (
              <div className={styles.addNewTask}>
                <button onClick={() => setShowNewTaskInput(true)}>
                  {' '}
                  <img src={AddTaskIcon} alt="AddNewTask" />
                  <span>+ Add New Task</span>{' '}
                </button>
              </div>
            )}
          </div>
        </div>

        <div className={styles.modalFooter}>
          <CustomButton
            type={buttonType}
            // type='secondary'
            label="Conditional Approve"
            style={{
              width: '180px',
              marginLeft: '10px',
            }}
            leftIcon={
              <img src={ConditionalApproveIcon}
               alt="Conditional Approve" 
               className={isClicked ? styles.clickedIcon : styles.defaultIcon}
               />
            }
            onClick={handleClick}
          />
        </div>
      </div>
    </div>
  );
};

export default ConditionalApprove;
