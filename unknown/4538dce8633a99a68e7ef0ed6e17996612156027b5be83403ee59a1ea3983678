import React, { useState } from 'react';
import styles from './Reject.module.css';
import CustomButton from '../../../common/button/CustomButton';
import RejectIcon from '../../../../assets/images/svg/Reject.svg';

const Reject: React.FC = () => {


  const [buttonType, setButtonType] = useState<'primary' | 'secondary' | 'header'>('secondary');
  const [isClicked, setIsClicked] = useState(false);
  const handleClick = () => {
    setButtonType('primary');
    setIsClicked(true);  // Set clicked state to true
    console.log('Reject Approve Button Clicked');
  };


  return (
    <div className={styles.modal}>
      <div className={styles.modalContent}>
        <div className={styles.modalHeader}>
          <h2>Reject</h2>
        </div>
        <label className={styles.noteLabel}>Add Comment</label>
        <div className={styles.textarea}>
          <textarea
            className={styles.noteInput}
            placeholder="Enter your comment here..."
          />
        </div>

        <div className={styles.modalFooter}>
          <CustomButton
            type={buttonType}
            label="Reject"
            style={
              {
                 width: '180px',
                 marginLeft: '10px'
              }
            }
            leftIcon={<img src={RejectIcon} alt="Reject"
            className={isClicked ? styles.clickedIcon : styles.defaultIcon}
            />}
            onClick={handleClick}
          />
        </div>
      </div>
    </div>
  );
};

export default Reject;
