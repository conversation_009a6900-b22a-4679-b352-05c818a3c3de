export function formatRanges(arr: any) {
  if (arr.length === 0) return '';

  const ranges = [];
  let start: any = arr[0];
  let end: any = arr[0];

  for (let i = 1; i < arr.length; i++) {
    if (arr[i] === end + 1) {
      // Continue the range
      end = arr[i];
    } else {
      // Add the previous range and start a new one
      if (start === end) {
        ranges.push(`${start}`);
      } else {
        ranges.push(`${start}-${end}`);
      }
      start = arr[i];
      end = arr[i];
    }
  }

  // Push the final range
  if (start === end) {
    ranges.push(`${start}`);
  } else {
    ranges.push(`${start}-${end}`);
  }

  return ranges.join(', ');
}
