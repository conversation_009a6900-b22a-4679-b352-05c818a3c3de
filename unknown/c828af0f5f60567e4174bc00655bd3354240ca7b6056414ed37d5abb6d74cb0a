/* EditorPreview.module.css */

.previewHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.previewTitle {
  font-weight: 500;
  color: #333;
}

.headerControls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.exportingIndicator {
  display: flex;
  align-items: center;
  padding: 0 8px;
  color: #666;
}

.zoomControlsHeader {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 4px 8px;
}

.zoomButton {
  color: #174e86 !important;
}

.zoomLevel {
  margin: 0 8px !important;
  font-weight: 500 !important;
  user-select: none;
  font-size: 14px !important;
  color: #174e86;
}

.previewContent {
  padding: 0 !important;
  background-color: #f5f5f5;
  overflow: auto !important;
  flex: 1;
}

.previewContainer {
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.zoomContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.1s ease-out;
  margin-bottom: 40px;
}

.pageWrapper {
  margin-bottom: 40px;
  position: relative;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border: 1px solid #ddd;
  border-bottom: none;
}

.pageNumber {
  font-size: 12px;
  color: #666;
}

.pageName {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.page {
  position: relative;
  background-color: white;
  border: 1px solid #ddd;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  box-sizing: border-box;
  margin: 0 auto;
  display: block;
  /* Ensure content has enough space and proper positioning */
  overflow: visible;
  /* Ensure page has proper sizing */
  min-height: 100%;
}

.gridContainer {
  /* Position is now absolute with explicit coordinates in inline styles */
  box-sizing: border-box;
  /* Let inline styles handle grid configuration */
  display: grid;
  /* Ensure chart overflow is visible */
  overflow: visible !important;
  /* Ensure grid is above page background */
  z-index: 1;
}

.chartContainer {
  position: relative;
  background-color: #ffffff;
  border: 1px solid #eaeaea;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  padding: 4px;
  box-sizing: border-box;
  /* Smaller margins for tighter layout */
  margin: 2px;
  min-width: 0;
  min-height: 0;
  /* Remove transform that could cause rendering issues */
  transform: none !important;
  /* Ensure proper stacking context behavior */
  isolation: isolate;
  z-index: auto !important; /* Let the inline style control z-index */
}

.chartContent {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  /* Ensure proper sizing */
  max-width: 100%;
  max-height: 100%;
  position: relative;
  /* Ensure chart is displayed above container background */
  z-index: 1;
  /* Improve rendering quality */
  will-change: transform;
  transform: translateZ(0);
}

.imageContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.textContainer {
  font-family: Arial, sans-serif;
  color: #333;
  line-height: 1.5;
  font-size: 14px;
}

.chartPlaceholder {
  border: 1px dashed #ccc;
  border-radius: 4px;
}

.exportingOverlay {
  position: absolute !important;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 16px !important;
  display: flex !important;
  align-items: center;
  gap: 16px;
  min-width: 250px;
  z-index: 1000;
}

.dialogActions {
  padding: 16px 24px !important;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.complianceToggle {
  margin-right: auto !important;
}

.actionButtons {
  display: flex;
  gap: 8px;
}

.compliancePage {
  margin-top: 40px;
  position: relative;
}

.compliancePageHeader {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border: 1px solid #ddd;
  border-bottom: none;
}

.emptyPreview {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 300px;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 8px;
  color: #666;
  font-size: 16px;
  font-weight: 500;
}

/* Print optimizations */
@media print {
  .previewContainer {
    padding: 0;
    background-color: white;
  }

  .pageWrapper {
    margin-bottom: 0;
    page-break-after: always;
  }

  .pageWrapper:last-child {
    page-break-after: avoid;
  }

  .pageHeader {
    display: none;
  }

  .page {
    box-shadow: none;
    border: none;
    padding: 0;
  }

  .chartContainer {
    border: none;
    box-shadow: none;
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .chartContent {
    page-break-inside: avoid;
    break-inside: avoid;
  }
}
