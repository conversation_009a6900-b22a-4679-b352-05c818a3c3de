/* BreadcrumbsComponent.module.css */
.breadcrumbsContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 28px;
  background-color: var(--color-background-secondary);
  z-index: 1000 !important;
  padding: 0 16px;
  box-sizing: border-box;
  overflow: visible;
  transition: background-color 0.3s ease;
}

.breadcrumbs {
  display: flex;
  align-items: center;
  height: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
}

.separator {
  margin: 0 6px;
  height: 12px;
}

.link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #677480;
  font-size: 12px !important;
  font-weight: 500;
  transition: color 0.2s ease;
  height: 100%;
  padding: 0 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.link:hover {
  color: #174e86;
}

.titleContainer {
  display: flex;
  align-items: center;
  gap: 4px;
  max-width: 250px;
  overflow: hidden;
}

.editableTitle {
  font-size: 12px;
  font-weight: 500;
  color: #677480;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.editableTitle:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.editableTitle[contenteditable='true'] {
  background-color: var(--color-background-secondary);
  border: 1px solid var(--color-border);
  outline: none;
  min-width: 100px;
}

.editButton {
  color: #677480 !important;
  padding: 2px !important;
  font-size: 12px !important;
}

.buttonsContainer {
  display: flex;
  gap: 8px;
  align-items: center;
  position: relative;
  height: 100%;
  flex-shrink: 0;
}

/* Preview Dialog Styles */
.previewHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  padding: 16px 24px;
}

.previewTitle {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.headerControls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.previewDialogContent {
  position: relative;
  padding: 24px;
  background-color: #f8f9fa;
  height: calc(95vh - 80px);
  overflow: hidden;
}

.zoomControls {
  position: fixed;
  left: 50%;
  bottom: 24px;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  gap: 8px;
  background-color: white;
  padding: 8px 16px;
  border-radius: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1200;
}

.previewContainer {
  width: 100%;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background-color: #f5f5f5;
}

.previewPage {
  width: 100%;
  background: white;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.pageHeader {
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pageContent {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  padding: 30px;
  overflow: hidden;
}

.chartContainer {
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  padding: 4px;
  box-sizing: border-box;
  overflow: hidden;
  /* Ensure proper positioning and stacking */
  transform-origin: top left;
  backface-visibility: hidden;
  will-change: transform, opacity;
}

.gridContainer {
  display: grid;
  width: 100%;
  height: 100%;
  position: relative;
}

.chartPreviewWrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.chartInnerWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Menu Styles */
.menu {
  margin-top: 8px;
  background-color: var(--color-background-dropdown);
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease;
}

.menuItem {
  font-size: var(--font-size-sm);
  line-height: 19.5px;
  text-align: center;
  font-family: var(--font-family-primary);
  padding: 10px 20px;
  color: var(--color-primary);
  transition:
    background-color 0.2s ease,
    color 0.3s ease;
}

.menuItem:hover {
  background-color: var(--color-background-tertiary);
}

/* Color Picker Styles */
.colorPickerContainer {
  position: absolute;
  z-index: 9999;
  top: 100%;
  margin-top: 8px;
  right: 0;
  background-color: var(--color-background-dropdown);
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 8px;
  border: 1px solid var(--color-border);
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

/* Scrollbar Styles */
.previewContainer::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.previewContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.previewContainer::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.previewContainer::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Print optimizations */
@media print {
  .previewContainer {
    overflow: visible;
    background-color: transparent;
    gap: 0;
    padding: 0;
  }

  .previewPage {
    page-break-after: always;
    page-break-inside: avoid;
    box-shadow: none;
    margin-bottom: 0;
  }

  .previewPage:last-child {
    page-break-after: auto;
  }

  .pageContent {
    overflow: visible;
  }

  .gridContainer {
    display: grid;
    width: 100%;
    height: 100%;
    overflow: visible !important;
  }

  .chartContainer {
    background-color: #ffffff !important;
    border: 1px solid #eaeaea !important;
    box-shadow: none !important;
    overflow: visible !important;
    break-inside: avoid !important;
    page-break-inside: avoid !important;
  }

  .chartPreviewWrapper,
  .chartInnerWrapper {
    overflow: visible !important;
    page-break-inside: avoid !important;
  }
}

/* High quality rendering for canvas elements */
canvas {
  image-rendering: high-quality;
  image-rendering: -webkit-optimize-contrast;
}

/* Handle page headers and footers */
.pageFooter {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 10px;
  color: #666;
}

/* Additional print-optimized elements */
.pageNumber {
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 10px;
  color: #888;
}

/* Add a custom class for print-only elements */
.printOnly {
  display: none;
}

@media print {
  .printOnly {
    display: block;
  }
}

/* Responsive Styles */
@media (max-width: 992px) {
  .breadcrumbsContainer {
    padding: 0 12px;
  }

  .buttonsContainer {
    gap: 6px;
  }
}

@media (max-width: 768px) {
  .breadcrumbs {
    max-width: 60%;
  }

  .link {
    max-width: 150px;
  }

  .separator {
    margin: 0 4px;
  }

  .buttonsContainer {
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .breadcrumbs {
    max-width: 50%;
  }

  .link {
    max-width: 100px;
  }

  .separator {
    margin: 0 2px;
    height: 10px;
  }

  .buttonsContainer {
    gap: 2px;
  }
}
