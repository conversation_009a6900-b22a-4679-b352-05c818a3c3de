import React, { useState } from 'react';
import styles from './DropdownInput.module.css'; // Assuming you're using CSS modules

interface DropdownOption {
  value: string;
  label: string;
}

interface DropdownInputProps {
  label?: string;
  options: DropdownOption[];
  value?: string;
  style?: React.CSSProperties;
  onChange?: (value: string) => void;
  error?: string;
}

const DropdownInput: React.FC<DropdownInputProps> = ({ label, options, value, onChange, error }) => {
  const [selectedValue, setSelectedValue] = useState<string>(value || '');

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newValue = e.target.value;
    setSelectedValue(newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  return (
    <div className={styles.inputContainer}>
      {label && <label className={styles.inputLabel}>{label}</label>}
      <select
        value={selectedValue}
        onChange={handleChange}
        className={styles.input}
      >
        <option value="" disabled>
          Select
        </option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && <span className={styles.error}>{error}</span>}
    </div>
  );
};

export default DropdownInput;
