.promptsContainer {
  width: 100%;
  max-width: 800px;
  margin: 0 auto 24px;
}

.promptsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  width: 100%;
  animation: fadeIn 0.3s ease-out;
  will-change: opacity, transform;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.promptCard {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: white;
  border: 1px solid #eee;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
  min-height: 110px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  will-change: transform, box-shadow;
  animation: cardFadeIn 0.3s ease-out forwards;
  opacity: 0;
}

.promptCard:nth-child(1) {
  animation-delay: 0.05s;
}
.promptCard:nth-child(2) {
  animation-delay: 0.1s;
}
.promptCard:nth-child(3) {
  animation-delay: 0.15s;
}
.promptCard:nth-child(4) {
  animation-delay: 0.2s;
}

@keyframes cardFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.promptCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  border-color: var(--primary-color);
}

.promptIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: min(5vw, 44px);
  height: min(5vw, 44px);
  border-radius: min(1vw, 10px);
  background: #f8fbff;
  color: var(--primary-color);
  transition: all 0.2s ease;
  flex-shrink: 0;
  border: 1px solid #eee;
}

.promptCard:hover .promptIcon {
  background: #f0f7ff;
  color: var(--primary-color);
  transform: scale(1.08);
  border-color: var(--primary-color);
}

.promptContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: min(1vh, 8px);
}

.promptContent h4 {
  margin: 0;
  font-size: clamp(15px, 1.5vw, 17px);
  color: #1a1a1a;
  font-weight: 600;
  line-height: 1.3;
}

.promptContent p {
  margin: 0;
  font-size: clamp(13px, 1.25vw, 14px);
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.noPromptsMessage {
  text-align: center;
  padding: 32px;
  font-size: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  margin: 20px 0;
  border: 1px dashed #e0e0e0;
  color: #666;
}

@media (max-width: 1024px) {
  .promptsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: min(1.5vw, 16px);
  }

  .promptCard {
    min-height: min(12vh, 100px);
  }
}

@media (max-width: 768px) {
  .promptsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .promptCard {
    min-height: auto;
    padding: 16px;
  }

  .promptIcon {
    width: min(8vw, 40px);
    height: min(8vw, 40px);
  }
}
