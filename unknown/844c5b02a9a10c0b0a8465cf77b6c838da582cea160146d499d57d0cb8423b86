import styles from './SketchbookCard.module.css';
import fallBack from '../../../assets/images/logoIcon.png';
interface SketchbookCardProps {
  title: string;
  timeAgo: string;
  onClick: () => void;
  viewMode: 'grid' | 'list';
  isPrivate?: boolean;
  creator?: string;
  index?: number;
  thumbnails?: any;
}

const SketchbookCard = ({
  title,
  timeAgo,
  onClick,
  viewMode = 'grid',
  index = 0,
  thumbnails,
}: SketchbookCardProps) => {
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = fallBack;
  };

  return (
    <div className={`${styles.card} ${styles[viewMode]}`} onClick={onClick}>
      <div className={styles.thumbnailSection}>
        <img
          src={thumbnails}
          alt="Sketchbook preview"
          width={300}
          height={150}
          onError={handleImageError}
        />
      </div>
      <div className={styles.content}>
        <h3 className={styles.title}>{title || 'Untitled'}</h3>
        <div className={styles.metadata}>
          {/* {isPrivate && (
            <div className={styles.privacy}>
              <FaLock  size={16}/> Private
            </div>
          )} */}
          {/* <div className={styles.creator}>
            Created by {creator}
          </div> */}
          <div className={styles.timeAgo}>Created : {timeAgo}</div>
        </div>
      </div>
    </div>
  );
};

export default SketchbookCard;
