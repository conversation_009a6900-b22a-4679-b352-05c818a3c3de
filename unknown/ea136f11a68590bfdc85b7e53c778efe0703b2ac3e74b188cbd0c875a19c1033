.sidebar {
  width: 280px;
  height: calc(100vh - 60px);
  background: var(--color-background-secondary);
  padding: var(--spacing-2);
  position: fixed;
  top: 60px;
  left: 0;
  opacity: 1;
  overflow-y: auto;
  transition:
    width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    background-color 0.3s ease,
    border-color 0.3s ease;
  border-right: 1px solid var(--color-border);
  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none;
  scrollbar-width: none;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  z-index: 100;
  margin-top: -10px;
}

.sidebarContent {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
}

.sidebar.collapsed {
  width: 60px;
  padding: var(--spacing-2);
}

.sidebarSection {
  margin-bottom: var(--spacing-2);
  padding: 0 var(--spacing-2);
}

.sectionHeader {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2) var(--spacing-3);
  width: 100%;
  color: var(--color-text-secondary);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  opacity: 0.7;
  margin-bottom: var(--spacing-1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  pointer-events: none;
  user-select: none;
}

.sectionItems {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.sectionHeader:hover {
  background: transparent;
  color: var(--color-text-secondary);
}

.sectionHeader .label {
  flex: 1;
  text-align: left;
}

.sectionHeader .icon {
  opacity: 0.7;
}

.sectionHeader:hover .icon {
  opacity: 0.7;
  transform: none;
  color: var(--color-text-secondary);
}

.expandIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--color-text-secondary);
}

.sectionHeader:hover .expandIcon {
  color: var(--color-primary-main);
}

.sectionContent {
  padding-left: var(--spacing-4);
}

.sidebarList {
  list-style-type: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.sidebarItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-decoration: none;
  white-space: nowrap;
}

.collapsed .sidebarItem {
  padding: var(--spacing-2);
  justify-content: center;
}

.sidebarItem:hover {
  background: var(--color-grey-100);
  color: var(--color-primary-main);
  transform: translateX(4px);
}

.sidebarItem.active {
  background: linear-gradient(
    90deg,
    var(--color-primary-main) 0%,
    var(--color-primary-light) 100%
  );
  color: var(--color-primary-dark);
  font-weight: var(--font-weight-semibold);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sidebarItem:hover .icon,
.sectionHeader:hover .icon {
  opacity: 1;
  transform: scale(1.1);
}

.sidebarItem.active .icon {
  opacity: 1;
  transform: scale(1.1);
  stroke-width: 2;
}

.sidebarItem.active:hover {
  background: linear-gradient(
    90deg,
    var(--color-primary-main) 0%,
    var(--color-primary-light) 100%
  );
  color: var(--color-text-primary);
  transform: translateX(4px);
}

.socialLinks {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-5);
  padding: var(--spacing-5);
  margin-top: auto;
  border-top: 1px solid var(--color-grey-200);
  position: relative;
  z-index: 1;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  background: transparent;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.socialLink:hover {
  transform: translateY(-2px);
}

.collapsed .socialLinks {
  flex-direction: column;
  padding: var(--spacing-4);
  gap: var(--spacing-4);
}

.label {
  vertical-align: middle;
  flex-grow: 1;
  opacity: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--font-size-sm);
}

.collapsed .label {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebarHr {
  border: none;
  height: 1px;
  background-color: var(--color-grey-200);
  margin: var(--spacing-4) 0;
  opacity: 0.6;
}

.collapsed .sidebarHr {
  margin: var(--spacing-3) auto;
  width: 30px;
}

.compliance {
  padding: var(--spacing-4) var(--spacing-2);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.complianceHeader {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
  color: var(--color-text-primary);
}

.complianceHeader h3 {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

.complianceControls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.selectAllButton {
  background: var(--color-grey-50);
  border: 1px solid var(--color-primary-main);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary-main);
  transition: all 0.2s;
  gap: var(--spacing-2);
}

.selectAllButton:hover {
  background: var(--color-grey-100);
}

.dialogTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-5) !important;
  background: var(--color-grey-50);
}

.closeButton {
  cursor: pointer;
  font-size: var(--font-size-2xl);
  color: var(--color-text-secondary);
  transition: color 0.2s;
}

.closeButton:hover {
  color: var(--color-text-primary);
}

.dialogContent {
  padding: var(--spacing-6) !important;
}

.resultCard {
  background: var(--color-primary-contrast);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: var(--spacing-6);
}

.resultCard:last-child {
  margin-bottom: 0;
}

.scoreSection {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
}

.scoreCircle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.scoreCircle span {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.scoreDetails {
  flex: 1;
}

.scoreDetails h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.scoreDetails p {
  margin: 0 0 4px 0;
  color: #666;
}

.scoreDetails small {
  color: #888;
}

.downloadButton {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s;
}

.downloadButton:hover {
  background: #357abd;
}

.reportsContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.reportItem {
  display: flex;
  align-items: center;
  gap: 16px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.reportItem:hover {
  transform: translateY(-2px);
}

.reportScore {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.reportScore span {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.reportInfo {
  flex: 1;
}

.reportInfo h4 {
  margin: 0 0 4px 0;
  color: #333;
}

.reportInfo p {
  margin: 0 0 4px 0;
  color: #666;
}

.reportInfo small {
  color: #888;
}

.reportItem .downloadButton {
  width: auto;
  padding: 8px;
  background: #f0f0f0;
  color: #666;
}

.reportItem .downloadButton:hover {
  background: #e0e0e0;
  color: #333;
}

.filesResults {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.fileCard {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid;
  transition: all 0.2s ease;
}

.fileCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.fileDetails h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.fileName {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.resultFooter {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.frameworkName {
  margin: 4px 0;
  font-size: 13px;
  color: #555;
}

.timestamp {
  display: block;
  margin-top: 8px;
  font-size: 12px;
  color: #777;
}

.cardDownloadButton {
  position: absolute;
  top: 12px;
  right: 12px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
}

.cardDownloadButton:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #333;
}

.fileCard {
  position: relative;
}

.reportHeader {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.reportHeader h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.reportHeader p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.buttonContent {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loader {
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-bottom-color: transparent;
  border-radius: 50%;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.generateButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.indicatorsContainer {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  justify-content: center;
}

.indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.colorBox {
  width: 20px;
  height: 20px;
  border: 1px solid;
  border-radius: 4px;
}

.stickyTopContainer {
  position: sticky;
  top: 0;
  z-index: 9500;
  background-color: var(--color-background-secondary);
  padding: var(--spacing-3) 0;
  margin-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--color-border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
}

.collapsed .stickyTopContainer {
  padding: var(--spacing-3) 0;
}

.collapseIconWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-1);
  transition: all 0.2s ease;
}

.collapseIconWrapper:hover {
  transform: translateY(-1px);
}

/* Tooltip styles removed in favor of IconButton's built-in tooltip */

.collapsedSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-3);
}

.iconButtonWrapper {
  display: flex;
  justify-content: center;
  margin: 4px 0;
  position: relative;
  z-index: 1;
  transition: all 0.2s ease;
}

.iconButtonWrapper:hover {
  transform: translateY(-1px);
}

.iconButtonWrapper .sidebarItem {
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.iconButtonWrapper .sidebarItem:hover {
  background: transparent;
  transform: none;
}

.complianceSelect {
  width: 100%;
  margin-bottom: 16px;
}

/* Ensure dropdown portal follows theme */
:global(.select__menu-portal) {
  z-index: 9999;
}

[data-theme='dark'] :global(.select__menu) {
  background-color: var(--color-background-paper);
  color: var(--color-text-primary);
}

[data-theme='dark'] :global(.select__option) {
  color: var(--color-text-primary);
}
