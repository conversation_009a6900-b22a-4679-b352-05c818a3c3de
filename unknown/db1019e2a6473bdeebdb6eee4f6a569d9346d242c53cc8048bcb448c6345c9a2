// import React, { useState, useCallback, useRef } from 'react';
// import { Card } from '@/components/ui/card';
// import { Move } from 'lucide-react';

// const ChartCanvas = ({ availableCharts }) => {
//   const [charts, setCharts] = useState([]);
//   const canvasRef = useRef(null);
//   const [draggedChart, setDraggedChart] = useState(null);
//   const [resizing, setResizing] = useState(null);

//   const addChart = useCallback(
//     (chartType, position) => {
//       const chart = availableCharts.find((c) => c.type === chartType);
//       if (!chart) return;

//       setCharts((prevCharts) => [
//         ...prevCharts,
//         {
//           id: `chart-${prevCharts.length + 1}`,
//           type: chartType,
//           label: chart.label,
//           component: chart.component,
//           position,
//           size: { width: 300, height: 200 },
//         },
//       ]);
//     },
//     [availableCharts]
//   );

//   const onDrop = useCallback(
//     (e) => {
//       e.preventDefault();
//       const chartType = e.dataTransfer.getData('application/chartType');
//       const canvasRect = canvasRef.current.getBoundingClientRect();
//       const position = {
//         x: e.clientX - canvasRect.left,
//         y: e.clientY - canvasRect.top,
//       };
//       addChart(chartType, position);
//     },
//     [addChart]
//   );

//   const onDragOver = useCallback((e) => {
//     e.preventDefault();
//     e.dataTransfer.dropEffect = 'move';
//   }, []);

//   const startDragging = useCallback((e, chartId) => {
//     e.stopPropagation();
//     setDraggedChart({
//       id: chartId,
//       initialX: e.clientX,
//       initialY: e.clientY,
//     });
//   }, []);

//   const onMouseMove = useCallback(
//     (e) => {
//       if (draggedChart) {
//         setCharts((prevCharts) =>
//           prevCharts.map((chart) => {
//             if (chart.id === draggedChart.id) {
//               return {
//                 ...chart,
//                 position: {
//                   x: chart.position.x + (e.clientX - draggedChart.initialX),
//                   y: chart.position.y + (e.clientY - draggedChart.initialY),
//                 },
//               };
//             }
//             return chart;
//           })
//         );
//         setDraggedChart((prev) => ({
//           ...prev,
//           initialX: e.clientX,
//           initialY: e.clientY,
//         }));
//       } else if (resizing) {
//         setCharts((prevCharts) =>
//           prevCharts.map((chart) => {
//             if (chart.id === resizing.id) {
//               const newWidth = Math.max(
//                 200,
//                 resizing.initialWidth + (e.clientX - resizing.startX)
//               );
//               const newHeight = Math.max(
//                 150,
//                 resizing.initialHeight + (e.clientY - resizing.startY)
//               );
//               return {
//                 ...chart,
//                 size: { width: newWidth, height: newHeight },
//               };
//             }
//             return chart;
//           })
//         );
//       }
//     },
//     [draggedChart, resizing]
//   );

//   const onMouseUp = useCallback(() => {
//     setDraggedChart(null);
//     setResizing(null);
//   }, []);

//   const startResizing = useCallback((e, chartId, currentSize) => {
//     e.stopPropagation();
//     setResizing({
//       id: chartId,
//       startX: e.clientX,
//       startY: e.clientY,
//       initialWidth: currentSize.width,
//       initialHeight: currentSize.height,
//     });
//   }, []);

//   return (
//     <div
//       ref={canvasRef}
//       className="relative w-full h-screen bg-gray-50 overflow-hidden"
//       onDrop={onDrop}
//       onDragOver={onDragOver}
//       onMouseMove={onMouseMove}
//       onMouseUp={onMouseUp}
//       onMouseLeave={onMouseUp}
//     >
//       {charts.map((chart) => (
//         <Card
//           key={chart.id}
//           className="absolute cursor-move"
//           style={{
//             left: chart.position.x,
//             top: chart.position.y,
//             width: chart.size.width,
//             height: chart.size.height,
//           }}
//         >
//           <div
//             className="p-4 h-full relative"
//             onMouseDown={(e) => startDragging(e, chart.id)}
//           >
//             <div className="flex items-center gap-2 mb-2">
//               <Move size={16} />
//               <span className="text-sm font-medium">{chart.label}</span>
//             </div>
//             <div className="w-full h-[calc(100%-2rem)]">{chart.component}</div>
//             <div
//               className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize"
//               onMouseDown={(e) => startResizing(e, chart.id, chart.size)}
//             >
//               <div className="w-2 h-2 bg-gray-400 rounded-full absolute bottom-1 right-1" />
//             </div>
//           </div>
//         </Card>
//       ))}
//     </div>
//   );
// };

// export default ChartCanvas;
