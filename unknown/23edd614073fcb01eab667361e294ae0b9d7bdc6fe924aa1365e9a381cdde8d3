import 'chartjs-adapter-date-fns';

export const transformChartData = (tables: any) => {
  return tables.map((table: any) => {
    let chartData;
    console.log(table.chart_id, 'chart id');

    switch (table.type_of_chart) {
      case 'Timeline Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'bar',
          data: {
            labels: table.x_labels, // Assumes these are date strings or date objects
            datasets: [
              {
                label: table.chart_name,
                data: table.values,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1,
              },
            ],
          },
          options: {
            responsive: true,
            scales: {
              x: {
                type: 'time', // Specify time for the x-axis
                time: {
                  unit: 'month', // Customize this as needed
                  displayFormats: {
                    month: 'MMM yyyy',
                  },
                },
                title: {
                  display: true,
                  text: 'Timeline',
                },
              },
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: 'Value',
                },
              },
            },
          },
        };
        break;
      case 'Bar Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'bar',
          data: {
            labels: table.x_labels,
            datasets: [
              {
                label: table.chart_name,
                data: table.values,
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 1,
              },
            ],
          },
          options: {
            scales: {
              y: {
                beginAtZero: true,
              },
            },
          },
        };
        break;

      case 'Line Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'line',
          data: {
            labels: table.x_labels,
            datasets: [
              {
                label: table.chart_name,
                data: table.values,
                fill: false,
                borderColor: 'rgba(54, 162, 235, 1)',
                tension: 0.1,
              },
            ],
          },
          options: {
            scales: {
              y: {
                beginAtZero: true,
              },
            },
          },
        };
        break;

      case 'Pie Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'pie',
          data: {
            labels: table.x_labels,
            datasets: [
              {
                label: table.chart_name,
                data: table.values,
                backgroundColor: [
                  'rgba(255, 99, 132, 0.2)',
                  'rgba(54, 162, 235, 0.2)',
                  'rgba(255, 206, 86, 0.2)',
                  'rgba(75, 192, 192, 0.2)',
                  'rgba(153, 102, 255, 0.2)',
                ],
                borderColor: [
                  'rgba(255, 99, 132, 1)',
                  'rgba(54, 162, 235, 1)',
                  'rgba(255, 206, 86, 1)',
                  'rgba(75, 192, 192, 1)',
                  'rgba(153, 102, 255, 1)',
                ],
                borderWidth: 1,
              },
            ],
          },
        };
        break;

      case 'Doughnut Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'doughnut',
          data: {
            labels: table.x_labels,
            datasets: [
              {
                label: table.chart_name,
                data: table.values,
                backgroundColor: [
                  'rgba(255, 159, 64, 0.2)',
                  'rgba(255, 99, 132, 0.2)',
                  'rgba(54, 162, 235, 0.2)',
                  'rgba(255, 206, 86, 0.2)',
                  'rgba(153, 102, 255, 0.2)',
                ],
                borderColor: [
                  'rgba(255, 159, 64, 1)',
                  'rgba(255, 99, 132, 1)',
                  'rgba(54, 162, 235, 1)',
                  'rgba(255, 206, 86, 1)',
                  'rgba(153, 102, 255, 1)',
                ],
                borderWidth: 1,
              },
            ],
          },
        };
        break;

      case 'Radar Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'radar',
          data: {
            labels: table.x_labels,
            datasets: [
              {
                label: table.chart_name,
                data: table.values,
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1,
              },
            ],
          },
        };
        break;

      case 'Polar Area Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'polarArea',
          data: {
            labels: table.x_labels,
            datasets: [
              {
                label: table.chart_name,
                data: table.values,
                backgroundColor: [
                  'rgba(75, 192, 192, 0.2)',
                  'rgba(255, 99, 132, 0.2)',
                  'rgba(54, 162, 235, 0.2)',
                  'rgba(255, 206, 86, 0.2)',
                  'rgba(153, 102, 255, 0.2)',
                ],
                borderColor: [
                  'rgba(75, 192, 192, 1)',
                  'rgba(255, 99, 132, 1)',
                  'rgba(54, 162, 235, 1)',
                  'rgba(255, 206, 86, 1)',
                  'rgba(153, 102, 255, 1)',
                ],
                borderWidth: 1,
              },
            ],
          },
        };
        break;

      case 'Scatter Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'scatter',
          data: {
            datasets: [
              {
                label: table.chart_name,
                data: table.values.map((val: any, index: number) => ({
                  x: table.x_labels[index],
                  y: val,
                })),
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 1,
              },
            ],
          },
          options: {
            scales: {
              x: {
                beginAtZero: true,
              },
              y: {
                beginAtZero: true,
              },
            },
          },
        };
        break;

      case 'Bubble Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'bubble',
          data: {
            datasets: [
              {
                label: table.chart_name,
                data: table.values.map((val: any) => ({
                  x: val[0],
                  y: val[1],
                  r: val[2],
                })),
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1,
              },
            ],
          },
        };
        break;

      case 'Horizontal Bar Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'bar',
          data: {
            labels: table.x_labels,
            datasets: [
              {
                label: table.chart_name,
                data: table.values,
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1,
              },
            ],
          },
          options: {
            indexAxis: 'y',
            scales: {
              x: {
                beginAtZero: true,
              },
            },
          },
        };
        break;

      case 'Area Chart':
        chartData = {
          chartId: table.chart_id,
          type: 'line',
          data: {
            labels: table.x_labels,
            datasets: [
              {
                label: table.chart_name,
                data: table.values,
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 1,
                fill: true,
              },
            ],
          },
          options: {
            responsive: true,
          },
        };
        break;

      default:
        chartData = {
          chartId: table.chart_id,
          type: 'line',
          data: {
            labels: table.x_labels,
            datasets: [
              {
                label: table.chart_name,
                data: table.values,
                fill: false,
                borderColor: 'rgba(54, 162, 235, 1)',
                tension: 0.1,
              },
            ],
          },
          options: {
            scales: {
              y: {
                beginAtZero: true,
              },
            },
          },
        };
    }

    return {
      sender: 'bot',
      content: 'Here is the chart based on the data you provided:',
      type: 'chart',
      chartData: chartData,
      timestamp: new Date().toLocaleTimeString(),
    };
  });
};
