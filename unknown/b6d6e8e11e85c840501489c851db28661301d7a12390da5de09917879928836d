import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SettingsState {
  responseType: string;
}

const initialState: SettingsState = {
  responseType: localStorage.getItem('responseType') || 'brief',
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setResponseType: (state, action: PayloadAction<string>) => {
      state.responseType = action.payload;
      localStorage.setItem('responseType', action.payload);
    },
  },
});

export const { setResponseType } = settingsSlice.actions;
export default settingsSlice.reducer;
