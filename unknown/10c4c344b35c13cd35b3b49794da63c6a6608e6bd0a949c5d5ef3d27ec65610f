// Define role colors using CSS variables for dark mode compatibility
const ROLE_COLORS = {
  ADMIN: {
    bg: 'var(--color-error-light)16', // Using error color with transparency for admin
    text: 'var(--color-error-main)',
  },
  DEFAULT: {
    bg: 'var(--color-info-light)16', // Using info color with transparency for regular users
    text: 'var(--color-info-main)',
  },
} as const;

export const styles = {
  container: {
    width: '100%',
    backgroundColor: 'background.paper',
  },
  tableCell: {
    fontWeight: 600,
    whiteSpace: 'nowrap',
  },
  nameCell: {
    minWidth: '200px',
    whiteSpace: 'nowrap',
  },
  roleChip: (role: string) => {
    const colorScheme = role.includes('ADMIN')
      ? ROLE_COLORS.ADMIN
      : ROLE_COLORS.DEFAULT;
    return {
      backgroundColor: colorScheme.bg,
      color: colorScheme.text,
      borderRadius: '6px',
      fontSize: '0.75rem',
      padding: '4px 8px',
    };
  },
  placeholderText: {
    color: 'text.secondary',
    fontSize: '0.875rem',
  },
} as const;
