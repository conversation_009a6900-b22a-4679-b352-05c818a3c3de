import { useState } from 'react';

const useLocalStorage = (key: string, initialValue: any) => {
    const [storedValue, setStoredValue] = useState(() => {
        try {
            const item = window.localStorage.getItem(key);
            // Check if the item is valid JSON
            return item ? JSON.parse(item) : initialValue;
        } catch (error) {
            console.error('Error parsing localStorage item:', error);
            // Clear localStorage if parsing fails
            window.localStorage.removeItem(key);
            return initialValue;
        }
    });

    const setValue = (value: any) => {
        try {
            const valueToStore = value instanceof Function ? value(storedValue) : value;
            setStoredValue(valueToStore);
            // Ensure the value is stringified before storing
            window.localStorage.setItem(key, JSON.stringify(valueToStore));
        } catch (error) {
            console.error('Error setting localStorage item:', error);
        }
    };

    return [storedValue, setValue];
};

export default useLocalStorage;
