import React, { useState } from 'react';
import styles from './Approve.module.css';
import CustomButton from '../../../common/button/CustomButton';
import ApproveIcon from '../../../../assets/images/svg/Approve.svg';

const Approve: React.FC = () => {


  const [buttonType, setButtonType] = useState<'primary' | 'secondary' | 'header'>('secondary');  
  const [isClicked, setIsClicked] = useState(false);
  const handleClick = () => {
    setButtonType('primary');
    setIsClicked(true);  // Set clicked state to true
    console.log('Approve Button Clicked');
  };




  return (
    <div className={styles.modal}>
      <div className={styles.modalContent}>
        <div className={styles.modalHeader}>
          <h2>Approve</h2>
        </div>
        <label className={styles.noteLabel}>Add Comment</label>
        <div className={styles.textarea}>
          <textarea
            className={styles.noteInput}
            placeholder="Enter your comment here..."
          />
        </div>

        <div className={styles.modalFooter}>
          <CustomButton
            type={buttonType}
            label="Approve"
            style={
              {
                 width: '180px',
                 marginLeft: '10px'
              }
            }
            leftIcon={<img src={ApproveIcon} alt="Approve" className={isClicked ? styles.clickedIcon : styles.defaultIcon}/>}
            onClick={handleClick}
          />
        </div>
      </div>
    </div>
  );
};

export default Approve;
