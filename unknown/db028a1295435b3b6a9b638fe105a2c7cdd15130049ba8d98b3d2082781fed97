.activities {
    width: 400px;
    height: 100%;
    padding: 20px;
    border-radius: 8px;
    font-family: Lato, sans-serif;
    background: #FFFFFF;
    box-shadow: -13px 0px 34px 0px #0000000A;
    float: right;
  
  }  

  
  .activitiesHeader {
    display: flex;
    justify-content: space-between;
    color: #292E33;
    font-weight: 600;
    font-size: 20px;
    align-items: center;
    padding: 10px;
  }
  
  .activitiesHeader h2 {
    margin: 0;
    font-size: 18px;
  }
  
  .closeButton {
    height: 15px;
    width: 15px;
    background: none;
    border: none;
    cursor: pointer;
  }
  
  .closeButton img {
    height: 100%;
    width: 100%;
  }
  
  .activityItem {
    padding: 10px;
  }
  
  .activityUser {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    color: #677480;
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-weight: 400;
  }
  
  .userIcon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    border: 1px solid #D1DFEC
  }
  
  .userName {
    font-weight: bold;
    margin-right: 5px;
    font-size: 16px;
    font-weight: 600;
    color: #292E33;
  }
  
  .userAction {
    color: #677480;
    font-size: 14px;
  }

  .activityComment {
    border-bottom: 1px solid #D1DFEC;
  
  }
  
  .activityComment, .activityTasks {
    margin-left: 34px;
    font-size: 16px;
    font-weight: 400;
    color: #57616B;
    background: #00000005;
    padding: 10px;
    border-radius: 8px;
  }

  

  .activityTasks .assigned {
    display: flex;
    text-wrap: nowrap;
    justify-content: space-between;
  }

  
  .activityComment p:first-child, .activityTasks p:first-child {
    color: #292E33;
    font-weight: 600;
    margin-bottom: 5px;
  }
  
  .taskItem {
    font-size: 16px;  
    color: #292E33;
    display: flex;
    align-items: center;
    text-wrap: nowrap;
    margin-top: 5px;
  }
  
  .activityTasks input[type="checkbox"] {
    margin-right: 5px;
    accent-color: #1B5EA1;
  }
  
  .completed {
    text-decoration: line-through;
    color: #57616B;
  }
  
  .completeAll {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }
