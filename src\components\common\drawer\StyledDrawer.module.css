.drawerContainer {
  position: relative;
}

.drawer {
  position: relative;
}

.resizableContainer {
  position: relative;
  height: 100%;
}

.resizableBox {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background-primary, #ffffff);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: width 0.1s ease-out;
}

.drawerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--color-background-secondary, #f5f5f5);
  border-bottom: 1px solid var(--color-border, #e0e0e0);
}

.drawerTitle {
  font-weight: 500;
  font-size: 14px;
  color: var(--color-text-primary, #333);
}

.closeButton {
  color: var(--color-text-secondary, #666) !important;
  padding: 4px !important;
}

.closeButton:hover {
  background-color: var(--color-background-tertiary, #e0e0e0) !important;
}

.drawerContent {
  height: calc(100% - 40px);
  overflow-y: auto;
  /* padding: 16px; */
  background-color: var(--color-background-primary, #ffffff);
}

.noSelect {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  cursor: col-resize !important;
}

.noSelect * {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* Custom resize handle styles */
.resizeHandle {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1400; /* Higher than other elements */
  background-color: var(--color-background-secondary, #f5f5f5);
  border: 1px solid var(--color-border, #e0e0e0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 24px;
  height: 80px; /* Increased height for easier grabbing */
  border-radius: 4px;
  cursor: col-resize;
  transition: all 0.2s ease;
  touch-action: none; /* Prevent touch actions on mobile */
}

/* Custom tooltip for resize handle */
.resizeHandle[data-tooltip]::after {
  content: attr(data-tooltip);
  position: absolute;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1500;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  transition-delay: 0.5s;
}

.resizeHandle[data-tooltip]:hover::after {
  opacity: 1;
}

/* Position the tooltip based on the handle position */
.resizeHandleE[data-tooltip]:hover::after {
  right: -5px;
  top: 50%;
  transform: translateX(100%) translateY(-50%);
}

.resizeHandleW[data-tooltip]:hover::after {
  left: -5px;
  top: 50%;
  transform: translateX(-100%) translateY(-50%);
}

.resizeHandle:hover {
  background-color: var(--color-background-tertiary, #e0e0e0);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.resizeHandleInner {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.dragIcon {
  color: var(--color-text-secondary, #666);
  font-size: 18px;
  transform: rotate(90deg);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

/* Position for left drawer resize handle */
.resizeHandleE {
  top: 50%;
  right: -12px;
  transform: translateY(-50%);
}

/* Position for right drawer resize handle */
.resizeHandleW {
  top: 50%;
  left: -12px;
  transform: translateY(-50%);
}

/* Touch-optimized styles */
.touchOptimized {
  width: 32px !important;
  height: 100px !important;
  border-radius: 8px;
  background-color: var(--color-background-secondary, #f5f5f5);
  border: 2px solid var(--color-border, #e0e0e0);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

.touchOptimized:hover,
.touchOptimized:active {
  background-color: var(--color-background-tertiary, #e0e0e0);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-50%) scale(1.05);
}

.touchOptimized .dragIcon {
  font-size: 24px;
  color: var(--color-text-primary, #333);
  animation: none;
}

/* Tablet-specific optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .resizeHandle {
    width: 28px;
    height: 90px;
    border-radius: 6px;
  }

  .touchOptimized {
    width: 36px !important;
    height: 120px !important;
  }

  .drawerButton {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 12px !important;
  }

  .closeButton {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 8px !important;
  }

  .drawerHeader {
    padding: 12px 16px;
    min-height: 56px;
  }

  .drawerTitle {
    font-size: 16px;
    font-weight: 600;
  }
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
  .resizeHandle {
    display: none; /* Hide resize handle on mobile */
  }

  .drawerButton {
    min-height: 44px !important;
    min-width: 44px !important;
  }

  .closeButton {
    min-height: 44px !important;
    min-width: 44px !important;
  }
}

.drawerButton {
  position: fixed !important;
  z-index: 1300 !important;
  background-color: var(--color-background-secondary) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transition:
    all 0.3s ease,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  top: 91px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  min-width: unset !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text-primary) !important;
}

.drawerButton[data-position='left'] {
  left: 50px !important;
}

.drawerButton[data-position='left'].open {
  left: 235px !important;
  margin-left: -14px !important;
}

.drawerButton[data-position='right'] {
  right: 10px !important;
}

.drawerButton[data-position='right'].open {
  right: 235px !important;
  margin-right: -14px !important;
}

.drawerButton:hover {
  background-color: var(--color-background-tertiary) !important;
  transform: scale(1.1) !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
  color: var(--color-primary) !important;
}

@media (max-width: 1200px) {
  .drawerButton[data-position='left'].open {
    left: 200px !important;
  }

  .drawerButton[data-position='right'].open {
    right: 200px !important;
  }
}

@media (max-width: 992px) {
  .drawerButton[data-position='left'].open {
    left: 180px !important;
  }

  .drawerButton[data-position='right'].open {
    right: 180px !important;
  }
}

@media (max-width: 768px) {
  .drawerButton {
    top: 80px !important;
    width: 24px !important;
    height: 24px !important;
    z-index: 1300 !important;
  }

  .drawerButton[data-position='left'] {
    left: 50px !important;
    margin-left: 0 !important;
  }

  .drawerButton[data-position='right'] {
    right: 10px !important;
    margin-right: 0 !important;
  }

  .drawerButton[data-position='left'].open {
    left: 240px !important;
    margin-left: -14px !important;
  }

  .drawerButton[data-position='right'].open {
    right: 240px !important;
    margin-right: -14px !important;
  }
}

@media (max-width: 480px) {
  .drawerButton {
    top: 75px !important;
    width: 22px !important;
    height: 22px !important;
  }

  .drawerButton[data-position='left'].open {
    left: 240px !important;
    margin-left: -13px !important;
  }

  .drawerButton[data-position='right'].open {
    right: 240px !important;
    margin-right: -13px !important;
  }
}
