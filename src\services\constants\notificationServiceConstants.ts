// Notification API Endpoints
export const NOTIFICATION_ENDPOINTS = {
  // Create notification
  CREATE_NOTIFICATION: '/api/v1/notifications/create',
  
  // Get all user notifications
  GET_ALL_NOTIFICATIONS: '/api/v1/notifications/get-all',
  
  // Mark notification as read
  MARK_AS_READ: '/api/v1/notifications',
} as const;

// Notification Status Constants
export const NOTIFICATION_STATUS = {
  READ: 'read',
  UNREAD: 'unread',
} as const;

// Notification Types
export const NOTIFICATION_TYPES = {
  ASSIGNMENT: 'assignment',
  STATUS_CHANGE: 'status_change',
  WORKFLOW_CREATED: 'workflow_created',
} as const;

// Priority Levels
export const NOTIFICATION_PRIORITIES = {
  LOW: 'Low',
  MEDIUM: 'Medium',
  HIGH: 'High',
} as const;
