.loading-spinner-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-spinner-container.full-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(249, 250, 251, 0.8);
  z-index: 9999;
}

.loading-spinner {
  border-radius: 50%;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-top-color: var(--color-primary);
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
}

.loading-spinner.medium {
  width: 40px;
  height: 40px;
}

.loading-spinner.large {
  width: 60px;
  height: 60px;
}

.loading-message {
  margin-top: 16px;
  font-size: 1rem;
  color: var(--color-primary);
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
