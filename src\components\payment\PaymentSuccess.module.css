.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  background: var(--color-background-light);
}

.successCard {
  max-width: 600px;
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--color-success-border);
  background: var(--color-background);
}

.errorCard {
  max-width: 500px;
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: var(--color-background);
}

.content {
  padding: 3rem 2rem;
  text-align: center;
}

.iconContainer {
  margin-bottom: 1.5rem;
}

.successIcon {
  font-size: 4rem;
  color: var(--color-success);
  animation: successPulse 0.6s ease-out;
}

.title {
  color: var(--color-text-primary);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: var(--color-text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.divider {
  margin: 2rem 0;
  background-color: var(--color-border-light);
}

.details {
  text-align: left;
  background: var(--color-background-light);
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
}

.detailsTitle {
  color: var(--color-text-primary);
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.receiptIcon {
  font-size: 1.25rem;
  color: var(--color-primary);
}

.detailRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--color-border-light);
}

.detailRow:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label {
  color: var(--color-text-secondary);
  font-weight: 500;
}

.value {
  color: var(--color-text-primary);
  font-weight: 600;
  text-align: right;
}

.statusChip {
  font-weight: 600;
}

.nextSteps {
  text-align: left;
  margin-bottom: 1.5rem;
}

.nextStepsTitle {
  color: var(--color-text-primary);
  font-weight: 600;
  margin-bottom: 1rem;
}

.stepsList {
  color: var(--color-text-secondary);
  line-height: 1.8;
  padding-left: 1.5rem;
}

.stepsList li {
  margin-bottom: 0.5rem;
}

.actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  justify-content: center;
}

.primaryButton {
  background: var(--color-primary);
  color: white;
  padding: 0.75rem 2rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.primaryButton:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
}

.secondaryButton {
  border-color: var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.75rem 2rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.secondaryButton:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: rgba(var(--color-primary-rgb), 0.05);
}

.supportInfo {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  margin-top: 1rem;
}

.supportLink {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
}

.supportLink:hover {
  text-decoration: underline;
}

/* Animations */
@keyframes successPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.successCard {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode support */
[data-theme="dark"] .container {
  background: var(--color-background-dark);
}

[data-theme="dark"] .successCard,
[data-theme="dark"] .errorCard {
  background: var(--color-background-dark);
  border-color: var(--color-border-dark);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .details {
  background: rgba(var(--color-primary-rgb), 0.05);
}

[data-theme="dark"] .divider {
  background-color: var(--color-border-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .content {
    padding: 2rem 1.5rem;
  }

  .actions {
    flex-direction: column;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
  }

  .title {
    font-size: 1.75rem;
  }

  .details {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }

  .content {
    padding: 1.5rem 1rem;
  }

  .successIcon {
    font-size: 3rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .detailRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .value {
    text-align: left;
  }
}
