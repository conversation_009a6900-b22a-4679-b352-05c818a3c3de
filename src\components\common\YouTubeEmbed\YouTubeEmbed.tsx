import React from 'react';
import styles from './YouTubeEmbed.module.css';

interface YouTubeEmbedProps {
  videoId: string;
  title?: string;
  width?: string | number;
  height?: string | number;
  autoplay?: boolean;
  allowFullScreen?: boolean;
  className?: string;
}

/**
 * A component for embedding YouTube videos
 * 
 * @param videoId - The YouTube video ID (the part after v= in the URL)
 * @param title - The title attribute for the iframe (for accessibility)
 * @param width - The width of the video player
 * @param height - The height of the video player
 * @param autoplay - Whether to autoplay the video
 * @param allowFullScreen - Whether to allow fullscreen mode
 * @param className - Additional CSS class name
 */
const YouTubeEmbed: React.FC<YouTubeEmbedProps> = ({
  videoId,
  title = 'YouTube video player',
  width = '100%',
  height = '480',
  autoplay = false,
  allowFullScreen = true,
  className = '',
}) => {
  // Construct the YouTube embed URL with parameters
  const embedUrl = `https://www.youtube.com/embed/${videoId}?rel=0${autoplay ? '&autoplay=1' : ''}`;

  return (
    <div className={`${styles.videoResponsive} ${className}`}>
      <iframe
        width={width}
        height={height}
        src={embedUrl}
        title={title}
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen={allowFullScreen}
      />
    </div>
  );
};

export default YouTubeEmbed;
