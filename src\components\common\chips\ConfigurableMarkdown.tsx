import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeKatex from 'rehype-katex';
import remarkMath from 'remark-math';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { solarizedlight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import 'katex/dist/katex.min.css';
import './markdownStyles.css';
import {
  preprocessMarkdownContent,
  detectLanguage,
} from '../../../utils/markdownUtils';
import {
  MarkdownConfig,
  DEFAULT_MARKDOWN_CONFIG,
  mergeMarkdownConfig,
  // getSyntaxHighlightingTheme, // For future use
} from '../../../utils/markdownConfig';

interface ConfigurableMarkdownProps {
  content: string;
  config?: Partial<MarkdownConfig>;
  className?: string;
  onError?: (error: Error) => void;
}

/**
 * A highly configurable markdown renderer with LaTeX support
 */
const ConfigurableMarkdown: React.FC<ConfigurableMarkdownProps> = ({
  content,
  config: userConfig = {},
  className = '',
  onError,
}) => {
  // Merge user configuration with defaults
  const config = mergeMarkdownConfig(userConfig, DEFAULT_MARKDOWN_CONFIG);

  // Ensure content is a string and handle edge cases
  if (!content || typeof content !== 'string') {
    return <div className={`markdown-wrapper ${className}`}></div>;
  }

  try {
    // Use the comprehensive preprocessing pipeline
    const processedContent = preprocessMarkdownContent(content, {
      baseUrl: config.baseUrl,
      enableGreekConversion: config.enableGreekConversion,
      enableCurrencyProtection: config.enableCurrencyProtection,
      enableLatexPreprocessing: config.enableLatexPreprocessing,
    });

    // Configure plugins based on options
    const remarkPlugins: any[] = [remarkGfm];

    if (config.enableLatexPreprocessing) {
      remarkPlugins.push([
        remarkMath,
        { singleDollarTextMath: config.singleDollarTextMath },
      ]);
    }

    const rehypePlugins: any[] = [];
    if (config.enableLatexPreprocessing) {
      rehypePlugins.push(rehypeKatex);
    }

    // Get syntax highlighting theme (for future use)
    // const syntaxTheme = getSyntaxHighlightingTheme(config.theme);

    return (
      <div
        className={`markdown-wrapper ${className}`}
        data-theme={config.theme}
      >
        <ReactMarkdown
          className="markdown"
          remarkPlugins={remarkPlugins}
          rehypePlugins={rehypePlugins}
          components={{
            // Enhanced code block rendering with language detection
            code({ className, children, ...props }: any) {
              if (!config.enableSyntaxHighlighting) {
                return (
                  <code className={className} {...props}>
                    {children}
                  </code>
                );
              }

              const codeString = String(children).replace(/\n$/, '');
              const match = /language-(\w+)/.exec(className || '');
              const language = match ? match[1] : detectLanguage(codeString);
              const isInline = !className || !match;
              const shouldShowLineNumbers =
                config.showLineNumbers &&
                codeString.split('\n').length >= config.lineNumberThreshold;

              return !isInline ? (
                <SyntaxHighlighter
                  style={solarizedlight as any}
                  language={language}
                  PreTag="div"
                  showLineNumbers={shouldShowLineNumbers}
                  wrapLines={true}
                >
                  {codeString}
                </SyntaxHighlighter>
              ) : (
                <code className={className} {...props}>
                  {children}
                </code>
              );
            },

            // Enhanced table rendering
            table({ children, ...props }) {
              if (!config.enableTableStyling) {
                return <table {...props}>{children}</table>;
              }

              return (
                <div className="table-wrapper">
                  <table
                    className={`markdown-table ${config.enableTableHover ? 'hover-enabled' : ''}`}
                    {...props}
                  >
                    {children}
                  </table>
                </div>
              );
            },

            // Enhanced checkbox rendering
            input({ type, checked, ...props }) {
              if (type === 'checkbox') {
                return (
                  <input
                    type="checkbox"
                    checked={checked}
                    readOnly
                    className="markdown-checkbox"
                    {...props}
                  />
                );
              }
              return <input type={type} {...props} />;
            },

            // Enhanced link rendering with external link handling
            a({ href, children, ...props }) {
              const isExternal =
                href?.startsWith('http') || href?.startsWith('//');
              const shouldOpenInNewTab =
                config.openExternalLinksInNewTab && isExternal;
              const shouldShowIndicator =
                config.enableExternalLinkIndicators && isExternal;

              return (
                <a
                  href={href}
                  target={shouldOpenInNewTab ? '_blank' : undefined}
                  rel={shouldOpenInNewTab ? 'noopener noreferrer' : undefined}
                  className={`markdown-link ${shouldShowIndicator ? 'external-link' : ''}`}
                  {...props}
                >
                  {children}
                </a>
              );
            },

            // Enhanced blockquote rendering
            blockquote({ children, ...props }) {
              return (
                <blockquote className="markdown-blockquote" {...props}>
                  {children}
                </blockquote>
              );
            },

            // Enhanced heading rendering with anchor links
            h1({ children, ...props }) {
              const id = String(children)
                .toLowerCase()
                .replace(/\s+/g, '-')
                .replace(/[^\w-]/g, '');
              return (
                <h1 id={id} className="markdown-heading" {...props}>
                  {children}
                </h1>
              );
            },
            h2({ children, ...props }) {
              const id = String(children)
                .toLowerCase()
                .replace(/\s+/g, '-')
                .replace(/[^\w-]/g, '');
              return (
                <h2 id={id} className="markdown-heading" {...props}>
                  {children}
                </h2>
              );
            },
            h3({ children, ...props }) {
              const id = String(children)
                .toLowerCase()
                .replace(/\s+/g, '-')
                .replace(/[^\w-]/g, '');
              return (
                <h3 id={id} className="markdown-heading" {...props}>
                  {children}
                </h3>
              );
            },
          }}
        >
          {processedContent}
        </ReactMarkdown>
      </div>
    );
  } catch (error) {
    console.error('ConfigurableMarkdown Error:', error);
    console.error('Content that caused error:', content);

    // Call error handler if provided
    if (onError) {
      onError(error as Error);
    }

    // Enhanced fallback rendering
    if (config.enableFallbackRendering) {
      return (
        <div className={`markdown-wrapper ${className}`}>
          <div
            style={{
              color: '#d32f2f',
              fontSize: '12px',
              marginBottom: '8px',
              padding: '8px',
              backgroundColor: '#ffebee',
              border: '1px solid #ffcdd2',
              borderRadius: '4px',
            }}
          >
            ⚠️ Rendering error - displaying as plain text
            {(config.showErrorDetails as any) && error && (
              <details style={{ marginTop: '4px', fontSize: '11px' }}>
                <summary>Error details</summary>
                <pre style={{ margin: '4px 0', fontSize: '10px' }}>
                  {String((error as Error).message)}
                </pre>
              </details>
            )}
          </div>
          <div
            style={{
              whiteSpace: 'pre-wrap',
              fontFamily: 'monospace',
              padding: '8px',
              backgroundColor: '#f5f5f5',
              border: '1px solid #ddd',
              borderRadius: '4px',
            }}
          >
            {content}
          </div>
        </div>
      );
    }

    // Minimal fallback
    return (
      <div className={`markdown-wrapper ${className}`}>
        <div style={{ color: 'red', fontSize: '12px', marginBottom: '8px' }}>
          Rendering failed
        </div>
        <pre style={{ whiteSpace: 'pre-wrap' }}>{content}</pre>
      </div>
    );
  }
};

export default ConfigurableMarkdown;

// Export configuration types and utilities for convenience
export type { MarkdownConfig };
export {
  DEFAULT_MARKDOWN_CONFIG,
  MARKDOWN_PRESETS,
  mergeMarkdownConfig,
  getMarkdownPreset,
} from '../../../utils/markdownConfig';
