import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { Layout } from 'react-grid-layout';
import styles from './EditorControls.module.css';
import { Tooltip } from '@mui/material';

// Icons
import { FiZoomIn, FiZoomOut, FiMaximize2 } from 'react-icons/fi';
import { CgArrangeBack } from 'react-icons/cg';
import {
  MdContentCopy,
  MdOutlineGridOn,
  MdOutlineGridOff,
  MdAlignHorizontalCenter,
  MdAlignVerticalCenter,
  MdMoreVert,
  MdSettings,
  MdUnfoldMore,
  MdUnfoldLess,
  MdMenu,
  MdClose,
  MdBugReport,
  MdHelp,
  MdDashboard,
  MdInsertChart,
} from 'react-icons/md';
import { IoIosUndo, IoIosRedo } from 'react-icons/io';
import { FaRegKeyboard } from 'react-icons/fa';
import { BsDistributeHorizontal, BsDistributeVertical } from 'react-icons/bs';
import { VscLayoutPanel } from 'react-icons/vsc';
import { TbArrowAutofitContent } from 'react-icons/tb';
import { HiOutlineDocumentText } from 'react-icons/hi';

// Import caret icons
import caretDown from '../../../../assets/images/sketchController/CaretDown.png';
import caretUp from '../../../../assets/images/sketchController/CaretUp.png';

interface EditorControlsProps {
  // State
  showGridLines: boolean;
  setShowGridLines: (value: boolean | ((prev: boolean) => boolean)) => void;
  gridDensity: number;
  setGridDensity: (value: number | ((prev: number) => number)) => void;
  selectedChart: string | null;
  undoStack: Layout[][];
  redoStack: Layout[][];

  // Actions
  zoomIn?: () => void;
  zoomOut?: () => void;
  resetTransform?: () => void;
  handleUndo: () => void;
  handleRedo: () => void;
  handleDuplicateChart: (chartId: string) => void;
  alignCharts: (
    direction: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom'
  ) => void;
  distributeCharts: (direction: 'horizontal' | 'vertical') => void;
  autoArrangeLayout: () => void;
  onPrint?: () => void;

  // Properties
  gridCols: number;
  gridRows: number;
  layoutLength: number;

  // Optional props for extended functionality
  handleRemoveChart?: (chartId: string, chart: any) => void;
  charts?: any[];

  // New props
  preventCollision: boolean;
  setPreventCollision: (value: boolean) => void;
  compactType: 'vertical' | 'horizontal' | null;
  setCompactType: (value: 'vertical' | 'horizontal' | null) => void;
}

const EditorControls: React.FC<EditorControlsProps> = ({
  showGridLines,
  setShowGridLines,
  gridDensity,
  setGridDensity,
  selectedChart,
  undoStack,
  redoStack,
  zoomIn,
  zoomOut,
  resetTransform,
  handleUndo,
  handleRedo,
  handleDuplicateChart,
  alignCharts,
  distributeCharts,
  autoArrangeLayout,
  gridCols,
  gridRows,
  layoutLength,
  handleRemoveChart,
  charts = [],
  onPrint,
  preventCollision,
  setPreventCollision,
  compactType,
  setCompactType,
}) => {
  const [debugMessage, setDebugMessage] = useState<string>('');
  const [showDebugPanel, setShowDebugPanel] = useState<boolean>(false);
  const [keyboardShortcutsModalOpen, setKeyboardShortcutsModalOpen] =
    useState<boolean>(false);
  const [showAdvancedControls, setShowAdvancedControls] =
    useState<boolean>(false);
  const [showGridSettings, setShowGridSettings] = useState<boolean>(false);
  const [isToolbarCollapsed, setIsToolbarCollapsed] = useState<boolean>(true);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showHelpPanel, setShowHelpPanel] = useState<boolean>(false);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle keyboard shortcuts if not editing text fields
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement
      ) {
        return;
      }

      // Ctrl+Z for Undo
      if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        handleUndo();
      }

      // Ctrl+Y or Ctrl+Shift+Z for Redo
      if (
        (e.ctrlKey && e.key === 'y') ||
        (e.ctrlKey && e.shiftKey && e.key === 'z')
      ) {
        e.preventDefault();
        handleRedo();
      }

      // Ctrl+D for Duplicate selected chart
      if (e.ctrlKey && e.key === 'd' && selectedChart) {
        e.preventDefault();
        handleDuplicateChart(selectedChart);
      }

      // Delete for removing selected chart
      if (
        e.key === 'Delete' &&
        e.ctrlKey &&
        selectedChart &&
        handleRemoveChart
      ) {
        const chart = charts.find((c: any) => c.id === selectedChart);
        if (chart) {
          handleRemoveChart(selectedChart, chart);
        }
      }

      // Increase grid density with Ctrl+Plus
      if (e.ctrlKey && (e.key === '+' || e.key === '=')) {
        e.preventDefault();
        setGridDensity((prev: number) => Math.min(prev + 0.25, 2.5));
      }

      // Decrease grid density with Ctrl+Minus
      if (e.ctrlKey && e.key === '-') {
        e.preventDefault();
        setGridDensity((prev: number) => Math.max(prev - 0.25, 0.5));
      }

      // Toggle grid lines with G
      if (e.key === 'g' && !e.ctrlKey && !e.altKey && !e.shiftKey) {
        setShowGridLines((prev: boolean) => !prev);
      }

      // Add print keyboard shortcut
      if (e.ctrlKey && e.key === 'p' && onPrint) {
        e.preventDefault();
        onPrint();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [
    handleUndo,
    handleRedo,
    selectedChart,
    charts,
    handleRemoveChart,
    handleDuplicateChart,
    setGridDensity,
    setShowGridLines,
    onPrint,
  ]);

  // Function to update debug info and show a visual indicator
  const updateDebugInfo = (action: string) => {
    setDebugMessage(`${action} - ${new Date().toLocaleTimeString()}`);

    // // Show toast notification for feedback
    // toast.success(action, {
    //   position: 'bottom-center',
    //   duration: 2000,
    // });

    // Automatically clear debug message after 3 seconds
    // setTimeout(() => {
    //   setDebugMessage('');
    // }, 3000);
  };

  // Enhanced button click handler
  const handleButtonClick = (action: string, callback: Function) => {
    // updateDebugInfo(`Action: ${action}`);

    try {
      // Execute the actual callback function
      callback();
    } catch (error) {
      setDebugMessage(`Error in ${action}: ${error}`);
      toast.error(`Failed to ${action}`);
      console.error(`Error in ${action}:`, error);
    }
  };

  // Function to handle grid visibility toggle with debug feedback
  const toggleGridVisibility = () => {
    const newState = !showGridLines;
    setShowGridLines(newState);
    // updateDebugInfo(`Grid lines ${newState ? 'visible' : 'hidden'}`);
  };

  // Toggle debug panel
  const toggleDebugPanel = () => {
    setShowDebugPanel(!showDebugPanel);
  };

  // Toggle advanced controls view
  const toggleAdvancedControls = () => {
    setShowAdvancedControls(!showAdvancedControls);
  };

  // Toggle grid settings
  const toggleGridSettings = () => {
    setShowGridSettings(!showGridSettings);
  };

  // Toggle toolbar collapse state
  const toggleToolbar = () => {
    setIsToolbarCollapsed(!isToolbarCollapsed);
  };

  // Toggle help panel
  const toggleHelpPanel = () => {
    setShowHelpPanel(!showHelpPanel);
  };

  // Keyboard shortcuts info modal
  const KeyboardShortcutsModal = () => (
    <div className={styles['keyboard-shortcuts-modal']}>
      <h3>Page & Chart Editor - Keyboard Shortcuts</h3>
      <p className={styles['modal-description']}>
        These shortcuts work specifically when editing pages and charts in the
        sketchbook canvas.
      </p>
      <div className={styles['shortcuts-grid']}>
        <div>
          <h4>📄 Page Layout Controls</h4>
          <ul>
            <li>
              <strong>Ctrl+Z</strong>: Undo page layout change
            </li>
            <li>
              <strong>Ctrl+Y</strong> or <strong>Ctrl+Shift+Z</strong>: Redo
              page layout change
            </li>
            <li>
              <strong>G</strong>: Toggle page grid lines
            </li>
            <li>
              <strong>Ctrl++</strong>: Increase page grid density
            </li>
            <li>
              <strong>Ctrl+-</strong>: Decrease page grid density
            </li>
          </ul>
        </div>
        <div>
          <h4>📊 Chart Controls</h4>
          <ul>
            <li>
              <strong>Ctrl+D</strong>: Duplicate selected chart
            </li>
            <li>
              <strong>Ctrl+Delete</strong>: Remove selected chart
            </li>
            <li>
              <strong>Ctrl+Wheel</strong>: Zoom in/out on page
            </li>
            <li>
              <strong>Ctrl+Drag</strong>: Pan around page
            </li>
          </ul>
        </div>
      </div>
      <button
        onClick={() => setKeyboardShortcutsModalOpen(false)}
        className={styles['close-button']}
      >
        Close
      </button>
    </div>
  );

  // Help panel component
  const HelpPanel = () => (
    <div className={styles['help-panel']}>
      <div className={styles['help-panel-header']}>
        <div className={styles['help-title']}>
          <MdDashboard className={styles['help-icon']} />
          <h3>Page & Chart Editor</h3>
        </div>
        <div className={styles['help-panel-close']} onClick={toggleHelpPanel}>
          <MdClose size={14} />
        </div>
      </div>
      <div className={styles['help-panel-content']}>
        <div className={styles['help-section']}>
          <h4>
            <HiOutlineDocumentText /> Page Tools
          </h4>
          <p>
            Control page layout, grid settings, and overall page structure.
            These tools affect the entire canvas.
          </p>
        </div>
        <div className={styles['help-section']}>
          <h4>
            <MdInsertChart /> Chart Tools
          </h4>
          <p>
            Manipulate individual charts - duplicate, align, distribute, and
            arrange charts on your page.
          </p>
        </div>
        <div className={styles['help-section']}>
          <h4>🎯 Scope</h4>
          <p>
            These controls only work within the sketchbook canvas and do not
            affect other parts of the application.
          </p>
        </div>
      </div>
    </div>
  );

  useEffect(() => {
    // Save toolbar state to localStorage for persistence
    localStorage.setItem(
      'editorToolbarCollapsed',
      isToolbarCollapsed.toString()
    );
  }, [isToolbarCollapsed]);

  // Load saved toolbar state on component mount
  useEffect(() => {
    const savedState = localStorage.getItem('editorToolbarCollapsed');
    if (savedState !== null) {
      setIsToolbarCollapsed(savedState === 'true');
    }
  }, []);

  // Add this useEffect to handle clicking outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdown = document.querySelector(`.${styles.dropdown}`);
      if (dropdown && !dropdown.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [styles.dropdown]);

  return (
    <div className={styles['editor-controls-container']}>
      {/* Toggle button for toolbar */}
      <Tooltip title="Toggle Page & Chart Editor Controls" placement="left">
        <div
          className={`${styles['toolbar-toggle']} ${!isToolbarCollapsed ? styles['active'] : ''}`}
          onClick={toggleToolbar}
        >
          {isToolbarCollapsed ? <MdMenu /> : <MdClose />}
        </div>
      </Tooltip>

      {/* Debug message display */}
      {debugMessage && (
        <div className={styles['debug-message']}>
          <strong>Action:</strong> {debugMessage}
        </div>
      )}

      {/* Help Panel */}
      {showHelpPanel && !isToolbarCollapsed && <HelpPanel />}

      {/* Debug Panel */}
      {showDebugPanel && !isToolbarCollapsed && (
        <div className={styles['debug-panel']}>
          <div className={styles['debug-panel-header']}>
            <h3>Debug Panel</h3>
            <div
              className={styles['debug-panel-close']}
              onClick={toggleDebugPanel}
            >
              <MdClose size={14} />
            </div>
          </div>
          {/* <div className={styles['debug-panel-content']}>
            <div>
              <strong>Last Action:</strong> {debugMessage || 'None'}
            </div>
            <div>
              <strong>Grid:</strong> {showGridLines ? 'Visible' : 'Hidden'}
            </div>
            <div>
              <strong>Selected Chart:</strong> {selectedChart || 'None'}
            </div>
            <div>
              <strong>Grid Density:</strong> {gridDensity}
            </div>
            <div>
              <strong>Layout Items:</strong> {layoutLength}
            </div>
            <div>
              <button onClick={() => updateDebugInfo('Test button clicked')}>
                Test Button
              </button>
            </div>
          </div> */}
        </div>
      )}

      {/* Main Editor Controls - Vertical Toolbar Style */}
      <div
        className={`${styles['toolbar']} ${isToolbarCollapsed ? styles['collapsed'] : ''}`}
      >
        {/* Toolbar Header */}
        <div className={styles['toolbar-header']}>
          <div className={styles['toolbar-title']}>
            <MdDashboard className={styles['title-icon']} />
            <span>Page & Chart Editor</span>
          </div>
          <div
            className={`${styles['toolbar-item']} ${showHelpPanel ? styles['active'] : ''}`}
            onClick={toggleHelpPanel}
            data-tooltip="Show help about these page & chart editing tools"
          >
            <MdHelp />
          </div>
        </div>

        {/* Page Controls Group */}
        <div className={styles['toolbar-group']}>
          <div className={styles['toolbar-group-label']}>
            <HiOutlineDocumentText />
            <span>Page Tools</span>
          </div>
          <div
            className={`${styles['toolbar-item']} ${showGridLines ? styles['active'] : ''}`}
            onClick={toggleGridVisibility}
            data-tooltip={
              showGridLines ? 'Hide page grid lines' : 'Show page grid lines'
            }
          >
            {showGridLines ? <MdOutlineGridOn /> : <MdOutlineGridOff />}
          </div>

          <div
            className={styles['toolbar-item']}
            onClick={() =>
              handleButtonClick('Auto Arrange Layout', autoArrangeLayout)
            }
            data-tooltip="Auto arrange all charts on page"
          >
            <CgArrangeBack />
          </div>
        </div>

        {/* Zoom Controls Group - Only shown if zoom functions available */}
        {zoomIn && zoomOut && resetTransform && (
          <div className={styles['toolbar-group']}>
            <div className={styles['toolbar-group-label']}>
              <FiMaximize2 />
              <span>Page View</span>
            </div>
            <div
              className={styles['toolbar-item']}
              onClick={() => handleButtonClick('Zoom In', () => zoomIn())}
              data-tooltip="Zoom in on page canvas"
            >
              <FiZoomIn />
            </div>

            <div
              className={styles['toolbar-item']}
              onClick={() =>
                handleButtonClick('Reset Zoom', () => resetTransform())
              }
              data-tooltip="Reset page zoom to fit"
            >
              <FiMaximize2 />
            </div>

            <div
              className={styles['toolbar-item']}
              onClick={() => handleButtonClick('Zoom Out', () => zoomOut())}
              data-tooltip="Zoom out from page canvas"
            >
              <FiZoomOut />
            </div>
          </div>
        )}

        {/* Chart Edit Controls Group */}
        <div className={styles['toolbar-group']}>
          <div className={styles['toolbar-group-label']}>
            <MdInsertChart />
            <span>Chart Edit</span>
          </div>
          <div
            className={`${styles['toolbar-item']} ${undoStack.length === 0 ? styles['disabled'] : ''}`}
            onClick={() => undoStack.length > 0 && handleUndo()}
            data-tooltip="Undo chart layout change (Ctrl+Z)"
          >
            <IoIosUndo />
          </div>

          <div
            className={`${styles['toolbar-item']} ${redoStack.length === 0 ? styles['disabled'] : ''}`}
            onClick={() => redoStack.length > 0 && handleRedo()}
            data-tooltip="Redo chart layout change (Ctrl+Y)"
          >
            <IoIosRedo />
          </div>

          {selectedChart && (
            <div
              className={styles['toolbar-item']}
              onClick={() =>
                selectedChart && handleDuplicateChart(selectedChart)
              }
              data-tooltip="Duplicate selected chart (Ctrl+D)"
            >
              <MdContentCopy />
            </div>
          )}
        </div>

        {/* Settings and Advanced Controls */}
        <div className={styles['toolbar-group']}>
          <div className={styles['toolbar-group-label']}>
            <MdSettings />
            <span>Settings</span>
          </div>
          <div
            className={`${styles['toolbar-item']} ${showGridSettings ? styles['active'] : ''}`}
            onClick={toggleGridSettings}
            data-tooltip="Page grid settings and density"
          >
            <MdSettings />
          </div>

          <div
            className={styles['toolbar-item']}
            onClick={() => setKeyboardShortcutsModalOpen(true)}
            data-tooltip="Page & chart editing keyboard shortcuts"
          >
            <FaRegKeyboard />
          </div>

          <div
            className={`${styles['toolbar-item']} ${showAdvancedControls ? styles['active'] : ''}`}
            onClick={toggleAdvancedControls}
            data-tooltip={
              showAdvancedControls
                ? 'Hide advanced chart alignment tools'
                : 'Show advanced chart alignment tools'
            }
          >
            {showAdvancedControls ? <MdUnfoldLess /> : <MdUnfoldMore />}
          </div>
        </div>

        {/* Chart Alignment Controls */}
        {selectedChart && showAdvancedControls && (
          <div className={styles['toolbar-group']}>
            <div className={styles['toolbar-group-label']}>
              <MdAlignHorizontalCenter />
              <span>Chart Align</span>
            </div>
            <div
              className={styles['toolbar-item']}
              onClick={() => alignCharts('left')}
              data-tooltip="Align selected chart to left edge"
            >
              <MdAlignHorizontalCenter style={{ transform: 'rotate(90deg)' }} />
            </div>

            <div
              className={styles['toolbar-item']}
              onClick={() => alignCharts('center')}
              data-tooltip="Align selected chart to center"
            >
              <MdAlignHorizontalCenter />
            </div>

            <div
              className={styles['toolbar-item']}
              onClick={() => alignCharts('right')}
              data-tooltip="Align selected chart to right edge"
            >
              <MdAlignHorizontalCenter
                style={{ transform: 'rotate(-90deg)' }}
              />
            </div>
          </div>
        )}

        {/* Chart Distribution Controls */}
        {layoutLength >= 3 && showAdvancedControls && (
          <div className={styles['toolbar-group']}>
            <div className={styles['toolbar-group-label']}>
              <BsDistributeHorizontal />
              <span>Chart Distribute</span>
            </div>
            <div
              className={styles['toolbar-item']}
              onClick={() => distributeCharts('horizontal')}
              data-tooltip="Distribute charts horizontally across page"
            >
              <BsDistributeHorizontal />
            </div>

            <div
              className={styles['toolbar-item']}
              onClick={() => distributeCharts('vertical')}
              data-tooltip="Distribute charts vertically across page"
            >
              <BsDistributeVertical />
            </div>
          </div>
        )}

        {/* Page Print Controls */}
        {onPrint && (
          <div className={styles['toolbar-group']}>
            <div className={styles['toolbar-group-label']}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                fill="currentColor"
                viewBox="0 0 16 16"
              >
                <path d="M2.5 8a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1z" />
                <path d="M5 1a2 2 0 0 0-2 2v2H2a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h1v1a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-1h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-1V3a2 2 0 0 0-2-2H5zM4 3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v2H4V3zm1 5a2 2 0 0 0-2 2v1H2a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-1v-1a2 2 0 0 0-2-2H5zm7 2v3a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1z" />
              </svg>
              <span>Print</span>
            </div>
            <div
              className={styles['toolbar-item']}
              onClick={() =>
                handleButtonClick('Print Editor Content', () => onPrint())
              }
              data-tooltip="Print page with all charts (Ctrl+P)"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                viewBox="0 0 16 16"
              >
                <path d="M2.5 8a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1z" />
                <path d="M5 1a2 2 0 0 0-2 2v2H2a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h1v1a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-1h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-1V3a2 2 0 0 0-2-2H5zM4 3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v2H4V3zm1 5a2 2 0 0 0-2 2v1H2a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-1v-1a2 2 0 0 0-2-2H5zm7 2v3a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1z" />
              </svg>
            </div>
          </div>
        )}

        {/* Page Layout Settings */}
        <div className={styles['toolbar-group']}>
          <div className={styles['toolbar-group-label']}>
            <VscLayoutPanel />
            <span>Layout</span>
          </div>
          <div
            data-tooltip="Prevent charts from overlapping on page"
            className={`${styles['toolbar-item']} ${preventCollision ? styles['active'] : ''}`}
            onClick={() => setPreventCollision(!preventCollision)}
          >
            <TbArrowAutofitContent />
          </div>
          <div
            className={`${styles['dropdown']} ${isDropdownOpen ? styles['open'] : ''}`}
          >
            <div
              data-tooltip="How charts are automatically arranged on page"
              className={styles['dropdown-toggle']}
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            >
              <VscLayoutPanel />
              <span className={styles['compact-type-label']}>
                {compactType ? compactType.charAt(0).toUpperCase() : 'N'}
              </span>
            </div>
            <div className={styles['dropdown-menu']}>
              <button
                onClick={() => {
                  setCompactType('vertical');
                  setIsDropdownOpen(false);
                }}
                className={compactType === 'vertical' ? styles['active'] : ''}
              >
                Vertical
              </button>
              <button
                onClick={() => {
                  setCompactType('horizontal');
                  setIsDropdownOpen(false);
                }}
                className={compactType === 'horizontal' ? styles['active'] : ''}
              >
                Horizontal
              </button>
              <button
                onClick={() => {
                  setCompactType(null);
                  setIsDropdownOpen(false);
                }}
                className={compactType === null ? styles['active'] : ''}
              >
                None
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Grid Settings Panel - Only shown when toggle is enabled */}
      {showGridSettings && !isToolbarCollapsed && (
        <div className={styles['grid-controls']}>
          <div className={styles['grid-info']}>
            <span>
              Grid: {gridCols}×{gridRows}
            </span>
          </div>
          <div className={styles['density-slider']}>
            <span>Density:</span>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={gridDensity}
              onChange={(e) => setGridDensity(parseFloat(e.target.value))}
            />
          </div>
        </div>
      )}

      {/* Compact Emergency Controls - only show when toolbar is collapsed */}
      {isToolbarCollapsed && (
        <div className={styles['emergency-controls']}>
          <Tooltip title="Toggle Page Grid Lines" placement="right">
            <div
              className={styles['emergency-button']}
              onClick={toggleGridVisibility}
            >
              {showGridLines ? (
                <MdOutlineGridOff size={16} />
              ) : (
                <MdOutlineGridOn size={16} />
              )}
            </div>
          </Tooltip>

          <Tooltip title="Auto Arrange Charts on Page" placement="right">
            <div
              className={styles['emergency-button']}
              onClick={() =>
                handleButtonClick('Auto Arrange', autoArrangeLayout)
              }
            >
              <CgArrangeBack size={16} />
            </div>
          </Tooltip>
        </div>
      )}

      {keyboardShortcutsModalOpen && <KeyboardShortcutsModal />}
    </div>
  );
};

export default EditorControls;
