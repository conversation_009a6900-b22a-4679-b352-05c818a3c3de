.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1.5rem;
  background-color: var(--color-background-secondary);
  border-bottom: 1px solid var(--color-border);
  height: 60px;
  position: sticky;
  top: 0;
  z-index: var(--z-header);
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.left {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.left:hover {
  opacity: 0.9;
}

.logo {
  height: 24px;
  margin: 0;
}

.right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.userContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.creditsSection {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.themeToggle {
  display: flex;
  align-items: center;
  margin-left: 0.5rem;
}

.networkStatus {
  display: flex;
  align-items: center;
  margin-left: 0.5rem;
}

.tourManager {
  display: flex;
  align-items: center;
  margin-left: 0.5rem;
}

.getUnlimitedBtn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--color-purple-50);
  border: 1px solid var(--color-purple-200);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--color-purple-600);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.getUnlimitedBtn:hover {
  background-color: var(--color-purple-100);
  border-color: var(--color-purple-300);
}

.starIcon {
  color: var(--color-purple-500);
  font-size: 0.875rem;
}

.credits {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-orange-500);
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  background-color: var(--color-orange-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-orange-200);
}

.coinIcon {
  font-size: 0.875rem;
}

.user {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.userImage {
  height: 32px;
  width: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.userImage:hover {
  transform: scale(1.05);
  border: 2px solid var(--color-purple-400);
  box-shadow: var(--shadow-md);
}

.userName {
  font-family: var(--font-sans);
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--color-gray-700);
}

.dropdownMenu {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  background-color: white;
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-lg);
  padding: 0.5rem;
  min-width: 200px;
  z-index: var(--z-dropdown);
  animation: dropdownFade 0.2s ease;
}

@keyframes dropdownFade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notificationContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.notificationIcon {
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  color: var(--color-gray-600);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notificationIcon:hover {
  background-color: var(--color-gray-100);
  color: var(--color-purple-600);
  transform: scale(1.05);
}

.notificationIcon:active {
  transform: scale(0.95);
}

.notificationBadge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background-color: red;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  width: 0.9rem;
  height: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}
