import { 
  <PERSON><PERSON>, 
  <PERSON>alogTitle, 
  <PERSON>alogContent, 
  <PERSON>alogA<PERSON>, 
  Button, 
  TextField,
  Box,
  Chip,
  Paper
} from '@mui/material';
import { useState, KeyboardEvent } from 'react';

interface EmailDialogProps {
  open: boolean;
  onClose: () => void;
  onSend: (emailData: {
    sendToEmails: string[];
    ccToEmails: string[];
    subject: string;
    content: string;
    hasAttachment: boolean;
    fileNames: string[];
  }) => void;
  fileName: string;
}

export const EmailDialog = ({ open, onClose, onSend, fileName }: EmailDialogProps) => {
  const [emailData, setEmailData] = useState({
    hasAttachment: true,
    sendToEmails: [] as string[],
    ccToEmails: [] as string[],
    subject: '',
    content: '',
    fileNames: [fileName]
  });

  const [toInput, setToInput] = useState('');
  const [ccInput, setCcInput] = useState('');

  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleKeyDown = (
    e: KeyboardEvent<HTMLDivElement>,
    type: 'to' | 'cc'
  ) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const input = type === 'to' ? toInput.trim() : ccInput.trim();
      
      if (input && isValidEmail(input)) {
        setEmailData(prev => ({
          ...prev,
          [type === 'to' ? 'sendToEmails' : 'ccToEmails']: [
            ...prev[type === 'to' ? 'sendToEmails' : 'ccToEmails'],
            input
          ]
        }));
        type === 'to' ? setToInput('') : setCcInput('');
      }
    }
  };

  const handleDelete = (email: string, type: 'to' | 'cc') => {
    setEmailData(prev => ({
      ...prev,
      [type === 'to' ? 'sendToEmails' : 'ccToEmails']: 
        prev[type === 'to' ? 'sendToEmails' : 'ccToEmails'].filter(e => e !== email)
    }));
  };

  const handleSend = () => {
    // Add any remaining input if valid
    if (toInput && isValidEmail(toInput) && !emailData.sendToEmails.includes(toInput)) {
      emailData.sendToEmails.push(toInput);
    }
    if (ccInput && isValidEmail(ccInput) && !emailData.ccToEmails.includes(ccInput)) {
      emailData.ccToEmails.push(ccInput);
    }
    
    onSend({
      ...emailData,
      hasAttachment: true,
      fileNames: [fileName]
    });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Send Email</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
          <Box>
            <TextField
              label="To"
              value={toInput}
              onChange={(e) => setToInput(e.target.value)}
              onKeyDown={(e) => handleKeyDown(e, 'to')}
              placeholder="Enter recipient email and press Enter"
              fullWidth
              helperText="Press Enter or comma to add multiple emails"
            />
            {emailData.sendToEmails.length > 0 && (
              <Paper 
                sx={{ 
                  display: 'flex', 
                  flexWrap: 'wrap', 
                  gap: 0.5, 
                  mt: 1, 
                  p: 0.5 
                }}
              >
                {emailData.sendToEmails.map((email) => (
                  <Chip
                    key={email}
                    label={email}
                    onDelete={() => handleDelete(email, 'to')}
                    size="small"
                  />
                ))}
              </Paper>
            )}
          </Box>
          
          <Box>
            <TextField
              label="CC"
              value={ccInput}
              onChange={(e) => setCcInput(e.target.value)}
              onKeyDown={(e) => handleKeyDown(e, 'cc')}
              placeholder="Enter CC email and press Enter"
              fullWidth
              helperText="Press Enter or comma to add multiple emails"
            />
            {emailData.ccToEmails.length > 0 && (
              <Paper 
                sx={{ 
                  display: 'flex', 
                  flexWrap: 'wrap', 
                  gap: 0.5, 
                  mt: 1, 
                  p: 0.5 
                }}
              >
                {emailData.ccToEmails.map((email) => (
                  <Chip
                    key={email}
                    label={email}
                    onDelete={() => handleDelete(email, 'cc')}
                    size="small"
                  />
                ))}
              </Paper>
            )}
          </Box>

          <TextField
            label="Subject"
            value={emailData.subject}
            onChange={(e) => setEmailData(prev => ({
              ...prev,
              subject: e.target.value
            }))}
            fullWidth
          />

          <TextField
            label="Content"
            value={emailData.content}
            onChange={(e) => setEmailData(prev => ({
              ...prev,
              content: e.target.value
            }))}
            multiline
            rows={4}
            fullWidth
          />

          <Box sx={{ mt: 1 }}>
            <strong>Attachment:</strong> {fileName}
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={handleSend} 
          variant="contained" 
          color="primary"
          disabled={emailData.sendToEmails.length === 0 || !emailData.subject}
        >
          Send Email
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 