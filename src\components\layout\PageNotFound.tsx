import { Box, Button, Typography, Container } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

type PAGE_TYPE = {
  type?: 'LAYOUT' | 'NO_LAYOUT';
};

const PageNotFound = ({ type }: PAGE_TYPE) => {
  const navigate = useNavigate();

  const handleBackClick = () => {
    type == 'NO_LAYOUT' ? navigate('/login') : navigate('/');
  };

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          minHeight: '80vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Box
          sx={{
            position: 'relative',
            textAlign: 'center',
            padding: 3,
            borderRadius: 4,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          }}
        >
          <ErrorOutlineIcon
            sx={{
              fontSize: 60,
              color: '#ffabb5',
              marginBottom: 1,
              animation: 'pulse 2s infinite',
            }}
          />
          <Typography
            variant="h1"
            sx={{
              fontSize: { xs: '3rem', md: '4rem' },
              fontWeight: 800,
              background: 'linear-gradient(45deg, #ffabb5, #c6abff)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              marginBottom: 1,
            }}
          >
            404
          </Typography>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: '#2C3E50',
              marginBottom: 2,
            }}
          >
            Looks like you're lost!
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: '#7F8C8D',
              marginBottom: 3,
              maxWidth: '400px',
              margin: '0 auto 1.5rem',
            }}
          >
            The page you're looking for doesn't exist. Let's get you back home.
          </Typography>
          <Button
            variant="contained"
            size="medium"
            onClick={handleBackClick}
            sx={{
              padding: '8px 24px',
              borderRadius: '25px',
              background: 'linear-gradient(45deg, #ffabb5, #c6abff)',
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 600,
              boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
              '&:hover': {
                background: 'linear-gradient(45deg, #ffabb5, #c6abff)',
                transform: 'translateY(-2px)',
                transition: 'all 0.3s ease',
              },
            }}
          >
            Take Me Home
          </Button>
        </Box>

        {/* Reduced number of background circles */}
        {[...Array(3)].map((_, index) => (
          <Box
            key={index}
            sx={{
              position: 'absolute',
              width: { xs: '80px', md: '120px' },
              height: { xs: '80px', md: '120px' },
              borderRadius: '50%',
              background: 'linear-gradient(45deg, #ffabb5, #c6abff)',
              filter: 'blur(40px)',
              animation: `float ${3 + index}s infinite ease-in-out`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              zIndex: -1,
            }}
          />
        ))}

        <style>
          {`
            @keyframes float {
              0%, 100% { transform: translateY(0) rotate(0deg); }
              50% { transform: translateY(-20px) rotate(10deg); }
            }
            @keyframes pulse {
              0% { transform: scale(1); }
              50% { transform: scale(1.1); }
              100% { transform: scale(1); }
            }
          `}
        </style>
      </Box>
    </Container>
  );
};

export default PageNotFound;
