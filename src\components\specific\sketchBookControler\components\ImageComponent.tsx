import React from 'react';

interface ImageComponentProps {
  data: {
    src: string;
    alt: string;
  };
}

const ImageComponent: React.FC<ImageComponentProps> = ({ data }) => {
  return (
    <img
      src={data.src}
      alt={data.alt}
      style={{
        width: '100%',
        height: '100%',
        objectFit: 'contain',
        display: 'block',
      }}
    />
  );
};

export default ImageComponent;
