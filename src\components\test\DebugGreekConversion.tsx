import React, { useState } from 'react';
import MarkdownRenderer from '../common/chips/Markdown';
import { convertGreekLetters, preprocessMarkdownContent } from '../../utils/markdownUtils';

const DebugGreekConversion: React.FC = () => {
  const [inputText, setInputText] = useState(`"summary": "Thank you for uploading your document. What would you like to do next ?",

Table of Greek Symbols
Here is a table of common Greek symbols used in mathematics and science, along with their names and LaTeX representations:

| Symbol | Name | LaTeX Code |
|--------|------|------------|
| Alpha | Alpha | \\alpha |
| Beta | Beta | \\beta |
| Gamma | Gamma | \\gamma |
| Delta | Delta | \\delta |
| Epsilon | Epsilon | \\epsilon |
| Lambda | Lambda | \\lambda |
| Mu | Mu | \\mu |
| Sigma | Sigma | \\sigma |
| Pi | Pi | \\pi |
| Theta | Theta | \\theta |
| Phi | Phi | \\phi |
| Omega | Omega | \\omega |`);

  // Test the conversion functions step by step
  const step1_original = inputText;
  const step2_greekConverted = convertGreekLetters(inputText);
  const step3_fullProcessed = preprocessMarkdownContent(inputText, {
    enableGreekConversion: true,
    enableCurrencyProtection: true,
    enableLatexPreprocessing: true
  });

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '1200px', 
      margin: '0 auto',
      backgroundColor: '#f9f9f9',
      borderRadius: '8px'
    }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>Debug Greek Letter Conversion</h1>
      
      {/* Input Section */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd',
        marginBottom: '20px'
      }}>
        <h3>Input Text (Edit to test):</h3>
        <textarea
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          style={{
            width: '100%',
            height: '200px',
            padding: '10px',
            border: '1px solid #ccc',
            borderRadius: '4px',
            fontFamily: 'monospace',
            fontSize: '12px'
          }}
        />
      </div>

      {/* Step-by-step processing */}
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
        
        {/* Step 1: Original */}
        <div style={{ 
          backgroundColor: 'white', 
          padding: '15px', 
          borderRadius: '8px',
          border: '1px solid #ddd'
        }}>
          <h4 style={{ color: '#666', marginBottom: '10px' }}>Step 1: Original Text</h4>
          <pre style={{ 
            fontSize: '11px', 
            backgroundColor: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '200px'
          }}>
            {step1_original}
          </pre>
        </div>

        {/* Step 2: Greek Converted */}
        <div style={{ 
          backgroundColor: 'white', 
          padding: '15px', 
          borderRadius: '8px',
          border: '1px solid #ddd'
        }}>
          <h4 style={{ color: '#666', marginBottom: '10px' }}>Step 2: After Greek Conversion</h4>
          <pre style={{ 
            fontSize: '11px', 
            backgroundColor: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '200px'
          }}>
            {step2_greekConverted}
          </pre>
        </div>

        {/* Step 3: Full Processing */}
        <div style={{ 
          backgroundColor: 'white', 
          padding: '15px', 
          borderRadius: '8px',
          border: '1px solid #ddd'
        }}>
          <h4 style={{ color: '#666', marginBottom: '10px' }}>Step 3: Full Preprocessing</h4>
          <pre style={{ 
            fontSize: '11px', 
            backgroundColor: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '200px'
          }}>
            {step3_fullProcessed}
          </pre>
        </div>

        {/* Step 4: Final Rendered */}
        <div style={{ 
          backgroundColor: 'white', 
          padding: '15px', 
          borderRadius: '8px',
          border: '1px solid #ddd'
        }}>
          <h4 style={{ color: '#666', marginBottom: '10px' }}>Step 4: Final Rendered Output</h4>
          <div style={{ 
            border: '1px solid #eee', 
            padding: '10px', 
            borderRadius: '4px',
            maxHeight: '200px',
            overflow: 'auto'
          }}>
            <MarkdownRenderer content={inputText} />
          </div>
        </div>
      </div>

      {/* Analysis */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd'
      }}>
        <h3>Analysis:</h3>
        <ul style={{ fontSize: '14px', color: '#666' }}>
          <li><strong>Greek Conversion Working:</strong> {step2_greekConverted !== step1_original ? '✅ Yes' : '❌ No'}</li>
          <li><strong>LaTeX Symbols Found:</strong> {step2_greekConverted.includes('$\\') ? '✅ Yes' : '❌ No'}</li>
          <li><strong>Table Structure:</strong> {step1_original.includes('|') ? '✅ Detected' : '❌ Not detected'}</li>
          <li><strong>Processing Changes:</strong> {step3_fullProcessed !== step1_original ? '✅ Yes' : '❌ No'}</li>
        </ul>
        
        <h4 style={{ marginTop: '15px' }}>Expected vs Actual:</h4>
        <p style={{ fontSize: '12px', color: '#666' }}>
          Expected: Greek letter names like "Alpha" should be converted to "$\\alpha$"<br/>
          Check the Step 2 output above to see if this conversion is happening.
        </p>
      </div>
    </div>
  );
};

export default DebugGreekConversion;
