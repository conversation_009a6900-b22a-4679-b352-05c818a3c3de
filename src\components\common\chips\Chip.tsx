import React, { useState, useRef } from 'react';
import style from './Chip.module.css';
import MarkdownRenderer from './Markdown';
import { FaCopy, FaShare } from 'react-icons/fa';
import Checkbox from '@mui/material/Checkbox';
import { toast } from 'react-hot-toast';
import CircularLoader from '../../../assets/loader/CircularLoader';

type ChipType = {
  loadingFinalResponse?: boolean;
  codeInput?: boolean;
  isLongResponse?: boolean;
  label: string;
  type: 'dark' | 'light' | 'hint' | 'chartImage';
  icon?: string | React.ReactNode;
  noOfPages?: number;
  handleOnClick?: () => void;
  id: number;
  isSuggestedQuestions?: boolean;
  showExport?: boolean;
  onExport?: () => void;
  sender?: any;
  onSelect?: (text: string, isSelected: boolean) => void;
  isSelected?: boolean;
  timestamp?: number;
  responseId?: any;
  onSendSelected?: (text: string) => void;
  preDefinedPrompt?: string;
};

const Chip = ({
  loadingFinalResponse,
  codeInput,
  isLongResponse,
  label,
  type,
  icon,
  handleOnClick,
  isSuggestedQuestions,
  showExport,
  sender,
  onSelect,
  isSelected = false,
  timestamp,
  responseId,
  onSendSelected,
  preDefinedPrompt,
}: ChipType) => {
  const [selectedText, setSelectedText] = useState('');
  const [selectionPosition, setSelectionPosition] = useState({
    top: 0,
    left: 0,
  });
  const containerRef = useRef<HTMLDivElement>(null);

  const handleTextSelection = () => {
    const selection = window.getSelection();
    const selectedText = selection?.toString().trim();

    if (selectedText && containerRef.current) {
      const range = selection?.getRangeAt(0);
      const rect = range?.getBoundingClientRect();
      const containerRect = containerRef.current.getBoundingClientRect();

      if (rect) {
        setSelectionPosition({
          top: rect.bottom - containerRect.top,
          left: rect.left - containerRect.left + rect.width / 2,
        });
      }
      setSelectedText(selectedText);
    } else {
      setSelectedText('');
    }
  };

  return (
    <div
      ref={containerRef}
      tabIndex={0}
      onClick={handleOnClick}
      className={`
        ${
          type === 'light'
            ? style.chipContainer
            : type === 'hint'
              ? style.hintChipContainer
              : style.darkChipContainer
        }
        ${isSuggestedQuestions ? style.suggestedQuestionsRow : ''}
      `}
      style={{
        backgroundColor:
          type === 'dark' ? 'var(--color-background-tertiary)' : 'transparent',
        cursor: type === 'dark' ? 'pointer' : 'text',
        border: isSelected ? `1px dotted var(--color-primary)` : 'none',
        position: 'relative',
      }}
      onMouseUp={handleTextSelection}
    >
      {icon && icon !== '' && typeof icon === 'string' ? (
        <img
          src={icon}
          alt="Icon"
          style={{
            width: '30px',
            height: '30px',
            marginRight: '8px',
          }}
        />
      ) : (
        type !== 'dark' && <div style={{ margin: '5px' }}>{icon}</div>
      )}
      <div className={style.labelContainer}>
        <MarkdownRenderer content={label} />
        {icon && (
          <div
            className={style.preDefinedPrompt}
            style={{ fontSize: '12px', color: 'var(--color-text-secondary)' }}
          >
            {' '}
            {preDefinedPrompt}
          </div>
        )}
        <div className={style.messageControls}>
          {showExport &&
            type !== 'dark' &&
            type !== 'hint' &&
            sender === 'bot' && (
              <div className={`${style.buttonGroupContainer}`}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div
                    style={{
                      fontSize: '12px',
                      color: 'var(--color-text-secondary)',
                    }}
                  >
                    {timestamp}
                  </div>
                  {responseId && (
                    <div className={`${style.buttonGroup}`}>
                      <Checkbox
                        disabled={!responseId}
                        checked={isSelected}
                        onChange={(e) =>
                          onSelect?.(responseId, e.target.checked)
                        }
                        onClick={(e) => e.stopPropagation()}
                        size="small"
                        sx={{
                          color: 'var(--color-text-secondary)',
                          '&.Mui-checked': {
                            color: 'var(--color-primary)',
                          },
                        }}
                      />
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          navigator.clipboard
                            .writeText(label)
                            .then(() => {
                              toast.success('Content copied to clipboard!');
                            })
                            .catch((err) => {
                              toast.error('Failed to copy text: ', err);
                            });
                        }}
                        className={style.exportButton}
                        title="Copy to clipboard"
                      >
                        <FaCopy style={{ fontSize: '16px' }} />
                      </button>
                    </div>
                  )}
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  {codeInput || loadingFinalResponse ? (
                    <>
                      <div
                        style={{
                          position: 'relative',
                          marginLeft: '10px',
                          marginRight: '30px',
                        }}
                      >
                        {codeInput
                          ? 'Just a sec, I’m working on it!'
                          : 'Loading Final Response'}
                      </div>

                      <CircularLoader size="small" />
                    </>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            )}
          {selectedText && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onSendSelected?.(selectedText);
                setSelectedText('');
              }}
              className={style.selectionControls}
              style={{
                position: 'absolute',
                top: `${selectionPosition.top + 5}px`,
                left: `${selectionPosition.left}px`,
                transform: 'translateX(-50%)',
                background: 'var(--color-background-secondary)',
                padding: '4px 8px',
                borderRadius: '4px',
                boxShadow: '0 2px 4px var(--color-shadow)',
                display: 'flex',
                gap: '8px',
                alignItems: 'center',
                border: '1px solid var(--color-border)',
                zIndex: 1000,
              }}
            >
              <div
                className={style.exportButton}
                title="Send selected text to chat input"
              >
                <FaShare
                  style={{ fontSize: '16px', color: 'var(--color-primary)' }}
                />
              </div>
              <p
                style={{
                  fontSize: '12px',
                  color: 'var(--color-primary)',
                  margin: 0,
                }}
              >
                send to chat input
              </p>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Chip;
