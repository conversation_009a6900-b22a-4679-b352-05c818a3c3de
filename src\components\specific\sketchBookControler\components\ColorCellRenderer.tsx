export const ColorCellRenderer = (props: any) => {
  const color = props.value;
  // Convert RGB to Hex if needed
  const getRGBToHex = (color: unknown) => {
    // Return default color if input is not a string
    if (typeof color !== 'string') {
      return '#000000';
    }

    if (color.startsWith('#')) {
      return color;
    } else if (color.startsWith('rgb')) {
      const rgb = color.match(/\d+/g);
      if (rgb) {
        const hex = rgb
          .map((x) => {
            const hex = parseInt(x).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
          })
          .join('');
        return '#' + hex;
      }
    }
    return '#fff000'; // Return default color for invalid inputs
  };

  const hexColor = getRGBToHex(color);

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <input
        type="color"
        value={hexColor}
        onChange={(e) => props.setValue(e.target.value)}
        className="colorPreview"
      />
      <span>{hexColor}</span>
    </div>
  );
};
