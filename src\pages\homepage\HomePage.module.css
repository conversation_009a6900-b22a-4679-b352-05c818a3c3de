.homeContainer {
  max-width: 100%;
  margin: 0;
  position: relative;
  min-height: calc(100vh - 60px);
  background: var(--color-background-primary);
  display: flex;
  flex-direction: column;
  padding: 0;
  transition: background-color 0.3s ease;
}

.projects {
  display: grid;
  gap: 24px;
  width: 100%;
  min-width: 0;
}

.projects.grid {
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.projects.list {
  grid-template-columns: 1fr;
  gap: 12px;
  width: 100%;
}

.tutorialHeading {
  margin-top: 20px;
  font-size: 25px;
  font-family: lato;
  font-weight: 600;
}

.loaderStyles {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.projectError {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
  justify-content: center;
  align-items: center;
}

.topSection {
  display: flex;
  flex-direction: column;
  gap: min(3vh, 24px);
  background: var(--color-background-tertiary);
  padding: min(4vh, 32px) min(3vw, 24px);
  /* min-height: auto; */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding-bottom: min(12vh, 100px);
  position: relative;
  transition: background-color 0.3s ease;
}

.chatInputContainer {
  /* width: 100%; */
  max-width: min(90vw, 800px);
  margin: 0 auto;
  text-align: left;
  /* padding-top: min(8vh, 64px); */
}

.headingContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.chatInputHeading {
  font-size: clamp(28px, 4vw, 36px);
  color: var(--color-text-primary);
  /* margin-bottom: min(3vh, 24px); */
  text-align: center;
  width: 100%;
  font-weight: 700;
  line-height: 1.3;
  text-align: left;
  max-width: min(90vw, 600px);
  transition: color 0.3s ease;
}

.chatinputWrapper {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: linear-gradient(
    to top,
    var(--color-background-tertiary) 80%,
    rgba(var(--color-background-primary-rgb), 0)
  );
  z-index: 100;
  display: flex;
  justify-content: center;
  transform: translateZ(0); /* Force hardware acceleration */
  will-change: transform; /* Optimize for animations */
  transition: background 0.3s ease;
}

.chatinputWrapper > div {
  width: 100%;
  max-width: 800px;
  border: 1px solid var(--color-border);
  border-radius: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  background: var(--color-background-card);
  transition:
    box-shadow 0.2s ease,
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.chatinputWrapper > div:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

/* When sidebar is collapsed */
:global(.contentCollapsed) .chatinputWrapper > div {
  margin-left: 60px; /* Collapsed sidebar width */
}

.chatInputContainer h2 {
  font-size: 30px;
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  font-weight: 600;
  /* text-align: center; */
}

.recentProjects {
  max-width: 800px;
  margin: 1rem auto;
  padding: 1.5rem;
  background-color: var(--background-secondary);
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  min-width: 0;
}

.recentProjectsHeading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.recentProjectsHeadingText {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.projectTabs {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--color-border);
}

.projectTab {
  padding: 12px 20px;
  background: transparent;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--color-text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  position: relative;
  bottom: -1px;
}

.projectTab:hover {
  color: var(--color-primary);
}

.projectTab.activeTab {
  color: var(--color-primary);
  border-bottom: 3px solid var(--color-primary);
  font-weight: 600;
}

.projectControls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.viewToggle {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--color-background-tertiary);
  transition: background-color 0.3s ease;
}

.viewButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: none;
  border: none;
  font-size: 0.875rem;
  cursor: pointer;
  transition:
    all 0.2s ease,
    color 0.3s ease,
    background-color 0.3s ease;
  color: var(--color-text-secondary);
}

.viewButton:hover {
  color: var(--color-text-primary);
}

.viewButton.active {
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
  font-weight: 500;
}

.viewAllButton {
  padding: min(1.25vw, 10px) min(2.5vw, 20px);
  background: var(--color-primary);
  color: var(--color-primary-contrast);
  border: none;
  border-radius: min(1.5vw, 12px);
  cursor: pointer;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: min(1vw, 8px);
  font-size: clamp(14px, 1.5vw, 16px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.viewAllButton:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.viewAllButton:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.loaderContainer {
  display: flex;
  justify-content: center;
  padding: 2rem 0;
}

.projectError {
  width: 100%;
  padding: 1rem;
  color: var(--error-color);
  text-align: center;
}

.emptyState {
  text-align: center;
  padding: 2rem 0;
  color: var(--text-secondary);
}

.projectCards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.projectCard {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: var(--background-primary);
  border-radius: 0.5rem;
  cursor: pointer;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
  position: relative;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.projectCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.projectInitials {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 1rem;
  flex-shrink: 0;
}

.projectInfo {
  flex: 1;
  overflow: hidden;
}

.projectInfo h4 {
  margin: 0 0 0.25rem;
  font-size: 1rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
}

.projectInfo p {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.deleteButton {
  background-color: transparent;
  color: var(--error-color);
  border: none;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  opacity: 0;
  transition:
    opacity 0.2s ease,
    background-color 0.2s ease;
  position: absolute;
  right: 0.5rem;
}

.projectCard:hover .deleteButton {
  opacity: 1;
}

.deleteButton:hover {
  background-color: rgba(255, 0, 0, 0.1);
}

.dragActive {
  position: relative;
}

.dropOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--color-background-primary-rgb), 0.98);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  z-index: 1000;
  color: var(--color-primary);
  pointer-events: none;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.dropOverlay p {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: var(--color-text-primary);
  transition: color 0.3s ease;
}

.uploadProgress {
  position: fixed;
  top: 15vh;
  right: min(4vw, 32px);
  background: var(--color-background-card);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  width: min(40vw, 320px);
  z-index: 9999;
  animation: slideIn 0.3s ease;
  transition: background-color 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.progressBar {
  width: 100%;
  height: 8px;
  background: var(--color-background-tertiary);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
  position: relative;
  transition: background-color 0.3s ease;
}

.progressFill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: var(--color-primary);
  border-radius: 4px;
  transition:
    width 0.3s ease,
    background-color 0.3s ease;
}

.uploadProgress p {
  margin: 0;
  font-size: 14px;
  color: var(--color-text-primary);
  font-weight: 500;
  text-align: center;
  transition: color 0.3s ease;
}

@media (max-width: 1400px) {
  .projects.grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1024px) {
  .homeContainer {
    gap: min(6vh, 48px);
  }

  .topSection {
    gap: min(4vh, 32px);
  }

  .projects.grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .homeContainer {
    padding: 24px;
    gap: 32px;
  }

  .topSection {
    padding: 32px;
    gap: 28px;
  }

  .chatInputHeading {
    font-size: 28px;
    line-height: 1.3;
    text-align: center;
  }

  .categoryTabs {
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .recentProjects {
    padding: 32px;
  }

  .recentProjectsHeading {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
  }

  .projectControls {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .viewToggle {
    min-width: 200px;
  }

  .viewButton {
    padding: 12px 20px;
    font-size: 16px;
    min-height: 48px;
  }

  .viewAllButton {
    padding: 12px 24px;
    font-size: 16px;
    min-height: 48px;
    min-width: 140px;
  }

  .projects.grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    padding: 24px 0;
  }

  .projects.list {
    gap: 16px;
    padding: 16px 0;
  }

  .uploadProgress {
    top: 15vh;
    right: 24px;
    left: 24px;
    width: auto;
    max-width: 400px;
    margin: 0 auto;
  }
}

/* Tablet portrait specific */
@media (min-width: 768px) and (max-width: 834px) and (orientation: portrait) {
  .recentProjectsHeading {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .projectControls {
    justify-content: center;
  }

  .projects.grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

/* Tablet landscape specific */
@media (min-width: 834px) and (max-width: 1024px) and (orientation: landscape) {
  .projects.grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }

  .topSection {
    padding: 24px 32px;
  }

  .recentProjects {
    padding: 24px 32px;
  }
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
  .homeContainer {
    padding: min(2vw, 16px);
    gap: min(4vh, 32px);
  }

  .topSection {
    padding: min(3vw, 24px);
    gap: min(3vh, 24px);
  }

  .recentProjects {
    padding: min(3vw, 24px);
  }

  .recentProjectsHeading {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .projectControls {
    width: 100%;
  }

  .viewToggle {
    width: 100%;
  }

  .viewButton {
    flex: 1;
    justify-content: center;
    min-height: 44px;
  }

  .viewAllButton {
    width: 100%;
    justify-content: center;
    min-height: 44px;
  }

  .uploadProgress {
    top: 10vh;
    right: min(2vw, 16px);
    left: min(2vw, 16px);
    width: auto;
  }
}

@media (max-width: 640px) {
  .projects.grid {
    grid-template-columns: 1fr;
  }
}

/* Add styles for card titles */
:global(.card-title) {
  color: var(--color-text-primary);
  font-size: clamp(15px, 1.75vw, 16px);
  font-weight: 600;
  margin: 0;
  line-height: 1.3;
  transition: color 0.3s ease;
}

:global(.list-card-title) {
  color: var(--color-text-primary);
  font-size: clamp(14px, 1.5vw, 15px);
  font-weight: 600;
  margin: 0;
  line-height: 1.3;
  transition: color 0.3s ease;
}

.categoryIcon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-size: 16px;
  transition: transform 0.2s ease;
  will-change: transform; /* Optimize for animations */
}

.categoryTabs {
  display: flex;
  justify-content: center;
  gap: 12px;
  /* margin: 20px 0; */
  flex-wrap: wrap;
  width: 100%;
  padding: 8px 0;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scrollbar-width: none; /* Hide scrollbar for Firefox */
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.categoryTabs::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome/Safari/Edge */
}

.categoryTab {
  padding: 10px 16px;
  border-radius: 20px;
  background: var(--color-background-tertiary);
  border: none;
  color: var(--color-text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    color 0.3s ease;
  font-size: 14px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  will-change: transform; /* Optimize for animations */
}

.categoryTab:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
}

.categoryTab:hover .categoryIcon {
  transform: scale(1.2);
}

.categoryTab.active {
  background: var(--color-primary-light) 20;
  color: var(--color-primary);
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Delete Confirmation Dialog */
.deleteConfirmOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-in-out;
}

.deleteConfirmDialog {
  background-color: var(--color-background-modal);
  border-radius: 0.75rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  transition: background-color 0.3s ease;
}

.deleteConfirmHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.deleteConfirmHeader h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.closeButton {
  background: transparent;
  border: none;
  font-size: 1.25rem;
  color: var(--text-secondary);
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.closeButton:hover {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.deleteConfirmBody {
  padding: 1.5rem;
}

.deleteConfirmBody p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

.deleteConfirmActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
}

.cancelButton,
.confirmButton {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border: none;
}

.cancelButton:hover {
  background-color: var(--background-quaternary);
}

.confirmButton {
  background-color: var(--error-color);
  color: var(--text-primary);
  border: none;
}

.confirmButton:hover {
  opacity: 0.9;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
