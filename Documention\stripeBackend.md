# Stripe Payment Integration - Backend API Documentation

This document provides comprehensive backend API specifications for the Stripe payment integration implemented in the frontend.

## 📋 **API Endpoints Overview**

The frontend payment system requires the following backend endpoints:

| Endpoint                      | Method | Purpose                          | Authentication    |
| ----------------------------- | ------ | -------------------------------- | ----------------- |
| `/api/payments/plans`         | GET    | Fetch available payment plans    | Required          |
| `/api/payments/history`       | GET    | Get user's payment history       | Required          |
| `/api/payments/methods`       | GET    | Get user's saved payment methods | Required          |
| `/api/payments/create-intent` | POST   | Create Stripe payment intent     | Required          |
| `/api/payments/confirm`       | POST   | Confirm payment completion       | Required          |
| `/api/payments/customers`     | POST   | Create/update Stripe customer    | Required          |
| `/api/payments/webhook`       | POST   | Handle Stripe webhooks           | Webhook signature |

---

## 🔐 **Authentication & Headers**

### **Standard Request Headers**

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
```

### **Authentication Flow**

- All payment endpoints require valid JWT token
- Extract user ID from JWT payload for user-specific operations
- Validate token expiration and signature

---

## 📊 **1. Get Payment Plans API**

### **Endpoint Details**

```http
GET /api/payments/plans
```

### **Request Headers**

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### **Request Body**

None (GET request)

### **Response Structure**

```typescript
interface PaymentPlan {
  id: string; // Unique plan identifier
  name: string; // Plan display name
  price: number; // Price in dollars (e.g., 19.99)
  currency: string; // Currency code (e.g., "usd")
  interval: 'month' | 'year' | 'week' | 'day';
  features: string[]; // Array of feature descriptions
  isPopular?: boolean; // Optional popular badge
  stripePriceId?: string; // Stripe Price ID for this plan
  description?: string; // Optional plan description
  maxUsers?: number; // Maximum users allowed
  maxGraphs?: number; // Maximum graphs per day/month
  supportLevel?: string; // Support level description
}

// Success Response
interface GetPlansResponse {
  success: true;
  data: PaymentPlan[];
  message: string;
}
```

### **Example Response**

```json
{
  "success": true,
  "data": [
    {
      "id": "basic",
      "name": "Basic",
      "price": 0,
      "currency": "usd",
      "interval": "month",
      "features": ["1 User", "3 Graphs/Day", "No Support"],
      "stripePriceId": null,
      "maxUsers": 1,
      "maxGraphs": 3,
      "supportLevel": "none"
    },
    {
      "id": "pro",
      "name": "Pro",
      "price": 19.99,
      "currency": "usd",
      "interval": "month",
      "features": [
        "1 User",
        "30 Graphs/Day",
        "Basic Support",
        "Advanced Features"
      ],
      "isPopular": true,
      "stripePriceId": "price_1234567890",
      "maxUsers": 1,
      "maxGraphs": 30,
      "supportLevel": "basic"
    },
    {
      "id": "business",
      "name": "Business",
      "price": 49.99,
      "currency": "usd",
      "interval": "month",
      "features": [
        "5 Users",
        "Unlimited Access",
        "24/7 Support",
        "Custom Solutions"
      ],
      "stripePriceId": "price_0987654321",
      "maxUsers": 5,
      "maxGraphs": -1,
      "supportLevel": "premium"
    }
  ],
  "message": "Plans retrieved successfully"
}
```

### **Error Responses**

```typescript
// 401 Unauthorized
{
  "success": false,
  "error": "UNAUTHORIZED",
  "message": "Invalid or expired token"
}

// 500 Internal Server Error
{
  "success": false,
  "error": "INTERNAL_ERROR",
  "message": "Failed to retrieve plans"
}
```

---

## 📜 **2. Get Payment History API**

### **Endpoint Details**

```http
GET /api/payments/history?page=1&limit=10&status=succeeded
```

### **Query Parameters**

```typescript
interface HistoryQueryParams {
  page?: number; // Page number (default: 1)
  limit?: number; // Items per page (default: 10, max: 100)
  status?: 'succeeded' | 'failed' | 'pending' | 'canceled';
  startDate?: string; // ISO date string (e.g., "2024-01-01")
  endDate?: string; // ISO date string (e.g., "2024-12-31")
}
```

### **Request Headers**

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### **Response Structure**

```typescript
interface PaymentRecord {
  id: string; // Payment intent ID from Stripe
  amount: number; // Amount in dollars
  currency: string; // Currency code
  status: 'succeeded' | 'failed' | 'pending' | 'canceled';
  planName: string; // Name of purchased plan
  planId: string; // Plan identifier
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  paymentMethodId?: string; // Stripe payment method ID
  failureReason?: string; // Reason for failure (if failed)
  receiptUrl?: string; // Stripe receipt URL
  invoiceId?: string; // Invoice identifier
  metadata?: Record<string, string>; // Additional metadata
}

interface GetHistoryResponse {
  success: true;
  data: PaymentRecord[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  message: string;
}
```

### **Example Response**

```json
{
  "success": true,
  "data": [
    {
      "id": "pi_1234567890",
      "amount": 19.99,
      "currency": "usd",
      "status": "succeeded",
      "planName": "Pro",
      "planId": "pro",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:31:00Z",
      "paymentMethodId": "pm_1234567890",
      "receiptUrl": "https://pay.stripe.com/receipts/...",
      "invoiceId": "inv_1234567890"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  },
  "message": "Payment history retrieved successfully"
}
```

---

## 💳 **3. Get Payment Methods API**

### **Endpoint Details**

```http
GET /api/payments/methods
```

### **Request Headers**

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### **Response Structure**

```typescript
interface PaymentMethod {
  id: string; // Stripe payment method ID
  type: string; // Payment method type (e.g., "card")
  card?: {
    brand: string; // Card brand (visa, mastercard, etc.)
    last4: string; // Last 4 digits
    expMonth: number; // Expiration month
    expYear: number; // Expiration year
    funding: string; // Funding type (credit, debit, etc.)
    country: string; // Card country
  };
  billingDetails?: {
    name?: string;
    email?: string;
    phone?: string;
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      postalCode?: string;
      country?: string;
    };
  };
  isDefault: boolean; // Whether this is the default method
  createdAt: string; // ISO date string
}

interface GetMethodsResponse {
  success: true;
  data: PaymentMethod[];
  message: string;
}
```

### **Example Response**

```json
{
  "success": true,
  "data": [
    {
      "id": "pm_1234567890",
      "type": "card",
      "card": {
        "brand": "visa",
        "last4": "4242",
        "expMonth": 12,
        "expYear": 2025,
        "funding": "credit",
        "country": "US"
      },
      "billingDetails": {
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "isDefault": true,
      "createdAt": "2024-01-10T08:00:00Z"
    }
  ],
  "message": "Payment methods retrieved successfully"
}
```

---

## 🎯 **4. Create Payment Intent API**

### **Endpoint Details**

```http
POST /api/payments/create-intent
```

### **Request Headers**

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### **Request Body**

```typescript
interface CreatePaymentIntentRequest {
  planId: string; // Plan to purchase
  customerEmail?: string; // Customer email (optional)
  metadata?: Record<string, string>; // Additional metadata
  paymentMethodId?: string; // Existing payment method ID
  savePaymentMethod?: boolean; // Whether to save payment method
}
```

### **Example Request**

```json
{
  "planId": "pro",
  "customerEmail": "<EMAIL>",
  "metadata": {
    "userId": "user_123",
    "source": "web_app"
  },
  "savePaymentMethod": true
}
```

### **Response Structure**

```typescript
interface CreatePaymentIntentResponse {
  success: true;
  data: {
    clientSecret: string; // Stripe client secret for frontend
    paymentIntentId: string; // Payment intent ID
    amount: number; // Amount in cents
    currency: string; // Currency code
    customerId?: string; // Stripe customer ID
  };
  message: string;
}
```

### **Example Response**

```json
{
  "success": true,
  "data": {
    "clientSecret": "pi_1234567890_secret_abcdef",
    "paymentIntentId": "pi_1234567890",
    "amount": 1999,
    "currency": "usd",
    "customerId": "cus_1234567890"
  },
  "message": "Payment intent created successfully"
}
```

### **Backend Implementation Example**

```javascript
// Node.js/Express example
app.post('/api/payments/create-intent', async (req, res) => {
  try {
    const { planId, customerEmail, metadata } = req.body;
    const userId = req.user.id; // From JWT token

    // Get plan details
    const plan = await getPlanById(planId);
    if (!plan) {
      return res.status(404).json({
        success: false,
        error: 'PLAN_NOT_FOUND',
        message: 'Plan not found',
      });
    }

    // Create or get Stripe customer
    let customer = await getStripeCustomerByUserId(userId);
    if (!customer) {
      customer = await stripe.customers.create({
        email: customerEmail || req.user.email,
        metadata: { userId },
      });
      await saveCustomerToDatabase(userId, customer.id);
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(plan.price * 100), // Convert to cents
      currency: plan.currency,
      customer: customer.id,
      metadata: {
        planId,
        userId,
        ...metadata,
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    res.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        customerId: customer.id,
      },
      message: 'Payment intent created successfully',
    });
  } catch (error) {
    console.error('Payment intent creation failed:', error);
    res.status(500).json({
      success: false,
      error: 'PAYMENT_INTENT_FAILED',
      message: 'Failed to create payment intent',
    });
  }
});
```

---

## ✅ **5. Confirm Payment API**

### **Endpoint Details**

```http
POST /api/payments/confirm
```

### **Request Body**

```typescript
interface ConfirmPaymentRequest {
  paymentIntentId: string; // Payment intent ID from Stripe
  paymentMethodId: string; // Payment method ID used
}
```

### **Response Structure**

```typescript
interface ConfirmPaymentResponse {
  success: true;
  data: {
    paymentIntent: {
      id: string;
      status: string;
      amount: number;
      currency: string;
    };
    subscription?: {
      id: string;
      status: string;
      currentPeriodEnd: string;
    };
  };
  message: string;
}
```

---

## 👤 **6. Customer Management API**

### **Create/Update Customer**

```http
POST /api/payments/customers
PUT /api/payments/customers
```

### **Request Body**

```typescript
interface CustomerRequest {
  email?: string;
  name?: string;
  phone?: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
}
```

````

---

## 🔔 **7. Webhook Endpoint**

### **Endpoint Details**
```http
POST /api/payments/webhook
````

### **Request Headers**

```http
Stripe-Signature: <stripe_signature>
Content-Type: application/json
```

### **Webhook Events to Handle**

```typescript
interface WebhookEvents {
  'payment_intent.succeeded': PaymentIntentSucceeded;
  'payment_intent.payment_failed': PaymentIntentFailed;
  'customer.subscription.created': SubscriptionCreated;
  'customer.subscription.updated': SubscriptionUpdated;
  'customer.subscription.deleted': SubscriptionDeleted;
  'invoice.payment_succeeded': InvoicePaymentSucceeded;
  'invoice.payment_failed': InvoicePaymentFailed;
}
```

### **Webhook Implementation Example**

```javascript
app.post(
  '/api/payments/webhook',
  express.raw({ type: 'application/json' }),
  async (req, res) => {
    const sig = req.headers['stripe-signature'];
    let event;

    try {
      event = stripe.webhooks.constructEvent(
        req.body,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET
      );
    } catch (err) {
      console.log(`Webhook signature verification failed.`, err.message);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        await handlePaymentSuccess(paymentIntent);
        break;

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object;
        await handlePaymentFailure(failedPayment);
        break;

      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    res.json({ received: true });
  }
);
```

---

**Note**: This backend implementation provides a complete foundation for the Stripe payment integration. Ensure all security measures are in place before deploying to production.
