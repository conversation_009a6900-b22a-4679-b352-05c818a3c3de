body {
  background-color: var(--color-background-primary);
  font-family: Lato, sans-serif;
  transition: background-color 0.3s ease;
}

.approvalWorkflow {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border);
  gap: 1rem;
}

.title {
  font-size: 1.75rem;
  margin: 0;
  color: var(--color-text-primary);
  transition: color 0.3s ease;
  font-weight: 600;
}

.sectionTitle {
  font-size: 1.25rem;
  margin-bottom: 1.25rem;
  color: var(--color-text-primary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.sectionTitle svg {
  color: var(--color-primary);
  flex-shrink: 0;
}

.card {
  background-color: var(--color-background-secondary);
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--color-border);
  transition: all 0.3s ease;
}

.formSection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  margin-bottom: 0.5rem;
}

.inputGroup label {
  font-weight: 500;
  color: var(--color-text-secondary);
  transition: color 0.3s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.inputGroup label svg {
  color: var(--color-primary);
  flex-shrink: 0;
}

.standardInput {
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  font-size: 1rem;
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  transition:
    all 0.2s,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
  width: 100%;
}

.standardInput:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

.standardInput::placeholder {
  color: #a0aec0;
  opacity: 0.7;
}

.standardInput[type='date'] {
  color: var(--color-text-primary);
  cursor: pointer;
}

.standardInput[readonly] {
  background-color: rgba(var(--color-border-rgb), 0.1);
  cursor: not-allowed;
}

.notesSection {
  margin-bottom: 2rem;
}

.noteInputContainer {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.notesList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.noteItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--color-background-secondary);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-border);
  transition: all 0.2s ease;
}

.noteItem:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--color-border);
}

.teamTable {
  background: var(--color-background-secondary);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-border);
  margin-top: 1rem;
}

.teamTable table {
  width: 100%;
  border-collapse: collapse;
}

.teamTable th {
  background: rgba(var(--color-border-rgb), 0.1);
  padding: 1rem 1.25rem;
  text-align: left;
  font-weight: 600;
  color: var(--color-text-secondary);
  font-size: 0.9rem;
}

.teamTable td {
  padding: 1rem 1.25rem;
  border-top: 1px solid var(--color-border);
  color: var(--color-text-primary);
}

.userCell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.userImg {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--color-background-secondary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.actionButtons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--color-border);
}

.iconButton {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-primary);
  background-color: var(--color-background-secondary);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 0.5rem;
  transition: all 0.2s ease;
}

.iconButton:hover {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-color: var(--color-primary);
}

.searchContainer {
  position: relative;
  margin-bottom: 1.5rem;
}

.searchInput {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  margin-bottom: 0.5rem;
  position: relative;
}

.searchResults {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  right: 0;
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  max-height: 300px;
  overflow-y: auto;
  z-index: 10;
}

.searchResult {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--color-border);
}

.searchResult:last-child {
  border-bottom: none;
}

.searchResult:hover {
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.required {
  color: #e53e3e;
  margin-left: 2px;
  font-weight: bold;
}

.errorInput {
  border: 1px solid #e53e3e !important;
  background-color: rgba(229, 62, 62, 0.05) !important;
}

.helperText {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  margin-top: 0.25rem;
  opacity: 0.8;
}

.errorText {
  font-size: 0.8rem;
  color: #e53e3e;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.participantsHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.twoColumnGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .twoColumnGrid {
    grid-template-columns: 1fr;
  }

  .formSection {
    grid-template-columns: 1fr;
  }

  .actionButtons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .actionButtons button {
    width: 100% !important;
  }
}
