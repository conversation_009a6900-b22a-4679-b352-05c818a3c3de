.notesSection {
  margin-bottom: 1rem;
}

.noteInputContainer {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.standardInput {
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  font-size: 1rem;
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  transition: all 0.2s;
  width: 100%;
}

.standardInput:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

.standardInput::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

.notesList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.noteItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--color-background-secondary);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-border);
  transition: all 0.2s ease;
  color: var(--color-text-primary);
  gap: 1rem;
}

.noteItem span {
  flex: 1;
  word-break: break-word;
}

.noteItem:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}
