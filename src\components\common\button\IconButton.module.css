.iconButton {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--color-border);
  border-radius: 50%;
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Button types */
.primary {
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
  border-color: var(--color-primary);
}

.primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
}

.secondary {
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
}

.secondary:hover:not(:disabled) {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.danger {
  background-color: var(--color-background-secondary);
  color: var(--color-error, #e53e3e);
  border-color: var(--color-error, #e53e3e);
}

.danger:hover:not(:disabled) {
  background-color: var(--color-error, #e53e3e);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
}

/* Button sizes */
.small {
  width: 30px;
  height: 30px;
  font-size: 0.75rem;
}

.medium {
  width: 38px;
  height: 38px;
  font-size: 0.875rem;
}

.large {
  width: 44px;
  height: 44px;
  font-size: 1rem;
}

.iconButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--color-grey-300);
  border-color: var(--color-grey-300);
  color: var(--color-grey-500);
}
