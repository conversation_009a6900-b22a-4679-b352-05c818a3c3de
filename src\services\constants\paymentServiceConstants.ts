// Payment API Endpoints
export const PAYMENT_ENDPOINTS = {
  // Plans Management
  GET_PLANS: '/api/v1/plans/get-all',
  GET_PLAN_DETAILS: '/api/v1/plans/get-by-id',
  CREATE_PLAN: '/api/v1/plans/save',
  UPDATE_PLAN: '/api/v1/plans/update',
  DELETE_PLAN: '/api/v1/plans/delete',

  // Payment Processing
  INITIATE_PAYMENT: '/api/v1/payments/initiate',
  GET_PAYMENT_HISTORY: '/api/v1/payments/get-all',

  // Legacy endpoints for backward compatibility
  CREATE_PAYMENT_INTENT: '/api/payments/create-intent',
  CONFIRM_PAYMENT: '/api/payments/confirm',
  GET_PAYMENT_INTENT: '/api/payments/intent',
  GET_PAYMENT_METHODS: '/api/payments/methods',
  ATTACH_PAYMENT_METHOD: '/api/payments/methods/attach',
  DE<PERSON>CH_PAYMENT_METHOD: '/api/payments/methods/detach',
  CREATE_SUBSCRIPTION: '/api/payments/subscriptions',
  GET_SUBSCRIPTIONS: '/api/payments/subscriptions',
  CANCEL_SUBSCRIPTION: '/api/payments/subscriptions/cancel',
  UPDATE_SUBSCRIPTION: '/api/payments/subscriptions/update',
  GET_PAYMENT_DETAILS: '/api/payments/details',
  CREATE_CUSTOMER: '/api/payments/customers',
  UPDATE_CUSTOMER: '/api/payments/customers/update',
  GET_CUSTOMER: '/api/payments/customers/details',
  WEBHOOK_ENDPOINT: '/api/payments/webhook',
} as const;

// Payment Status Constants
export const PAYMENT_STATUS = {
  CREATED: 'created',
  PENDING: 'pending',
  PROCESSING: 'processing',
  SUCCEEDED: 'succeeded',
  FAILED: 'failed',
  CANCELED: 'canceled',
  REQUIRES_ACTION: 'requires_action',
  REQUIRES_PAYMENT_METHOD: 'requires_payment_method',
} as const;

// Payment Method Types
export const PAYMENT_METHOD_TYPES = {
  CARD: 'card',
  BANK_ACCOUNT: 'us_bank_account',
  PAYPAL: 'paypal',
  APPLE_PAY: 'apple_pay',
  GOOGLE_PAY: 'google_pay',
} as const;

// Currency Codes
export const SUPPORTED_CURRENCIES = {
  USD: 'usd',
  EUR: 'eur',
  GBP: 'gbp',
  CAD: 'cad',
  AUD: 'aud',
} as const;

// Plan Intervals
export const PLAN_INTERVALS = {
  MONTH: 'month',
  YEAR: 'year',
  WEEK: 'week',
  DAY: 'day',
} as const;

// Error Codes
export const PAYMENT_ERROR_CODES = {
  CARD_DECLINED: 'card_declined',
  INSUFFICIENT_FUNDS: 'insufficient_funds',
  EXPIRED_CARD: 'expired_card',
  INCORRECT_CVC: 'incorrect_cvc',
  PROCESSING_ERROR: 'processing_error',
  INVALID_REQUEST: 'invalid_request',
  AUTHENTICATION_REQUIRED: 'authentication_required',
  NETWORK_ERROR: 'network_error',
} as const;

// Default Payment Plans (can be overridden by API)
export const DEFAULT_PAYMENT_PLANS = [
  {
    id: 'basic',
    name: 'Basic',
    price: 99,
    currency: 'usd',
    tokens: 444,
    description: 'Basic plan for getting started',
    maxGraphs: 20,
    isPopular: false,
    // Legacy fields for backward compatibility
    interval: 'month' as const,
    features: ['1 User', '444 Tokens', 'Basic Support', '20 Graphs'],
    stripePriceId: null,
  },
  {
    id: 'premium',
    name: 'Premium',
    price: 199,
    currency: 'usd',
    tokens: 2000,
    description: 'Premium plan with advanced features',
    maxGraphs: 20,
    isPopular: true,
    // Legacy fields for backward compatibility
    interval: 'month' as const,
    features: [
      '1 User',
      '2000 Tokens',
      'Priority Support',
      '20 Graphs',
      'Advanced Features',
    ],
    stripePriceId: 'price_premium_monthly',
  },
] as const;

// Stripe Elements Styling
export const STRIPE_ELEMENT_STYLES = {
  base: {
    fontSize: '16px',
    color: '#424770',
    '::placeholder': {
      color: '#aab7c4',
    },
  },
  invalid: {
    color: '#9e2146',
  },
} as const;

// Payment Form Validation Rules
export const PAYMENT_VALIDATION = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  POSTAL_CODE_REGEX: /^[0-9]{5}(-[0-9]{4})?$/,
  MIN_AMOUNT: 0.5, // Minimum payment amount in dollars
  MAX_AMOUNT: 999999.99, // Maximum payment amount in dollars
} as const;
