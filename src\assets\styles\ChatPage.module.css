.mainChatContainer {
  height: 100%;
  overflow: hidden;
  background-color: var(--color-background-tertiary);
  transition: background-color 0.3s ease;
}

.chatContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 79vh;
  /* padding: 16px; */
  padding-left: 16px;
  padding-bottom: 16px;
  padding-top: 5px;
  border-radius: 8px;
  overflow: hidden;
}

.chatHeading {
  margin-bottom: 16px;
  text-align: start;
  font-size: 1.2em;
  color: var(--color-text-primary);
  transition: color 0.3s ease;
}

.chatContent {
  flex-grow: 1;
  overflow-y: auto;
  padding-bottom: 80px;
  margin-bottom: 0;
}

.sender,
.receiver {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.sender {
  justify-content: flex-end;
}

.receiver {
  justify-content: flex-start;
}

.messageContainer {
  display: flex;
  align-items: start;
}

.avatar {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  margin-right: 8px;
}

.inputContainer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  /* background-color: #fff; */
  /* border-top: 1px solid #eee; */
  z-index: 10;
}

.inputInnerContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: auto;
}

.chatBox {
  border-radius: 8px;
  width: 100%;
  margin: auto;
  max-width: 800px;
  position: relative;
}

.chartContainer {
  width: 500px; /* Set fixed width */
  min-height: 300px; /* Set minimum height */
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  position: relative;
  background-color: var(--color-background-card);
  border-radius: 8px;
  transition:
    background-color 0.3s ease,
    box-shadow 0.3s ease;
  /* Remove any size transitions */
}

/* Add this new class to ensure chart canvas maintains its dimensions */
.chartContainer canvas {
  width: 100% !important;
  height: 100% !important;
  min-height: 280px !important; /* Account for padding */
}

.chartSelectionHeader {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  z-index: 2;
}

.imageChartContainer {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  position: relative;
  background-color: var(--color-background-card);
  border-radius: 8px;
  transition:
    background-color 0.3s ease,
    box-shadow 0.3s ease;
}

.chartSelectionHeader .MuiCheckbox-root {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
}

input:disabled,
button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loadingChatContainer {
  display: flex;
  height: 79vh;
  justify-content: center;
  align-items: center;
}

.buttonGroupContainer {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px;
  background-color: var(--color-background-secondary);
  border-radius: 8px;
  /* If it's inside the header */
  margin-right: 16px;
  transition: background-color 0.3s ease;
}

/* If the buttons should be arranged vertically */
.buttonGroupContainer.vertical {
  flex-direction: column;
  align-items: stretch;
}

/* For individual buttons in the group */
.buttonGroupContainer button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  background-color: var(--color-background-tertiary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.buttonGroupContainer button:hover {
  background-color: var(--color-primary-light);
  color: var(--color-primary-contrast);
}

/* ///////////// */

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
}

.tourButtonContainer {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.chatHeader {
  position: static;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-right: 10px;
  flex: 1;
}

.chatHeaderOptions {
  display: flex;
  /* background-color: #441616; */
}
.dropdownContainer {
  position: relative;
  display: inline-block;
}

.dropdownButton {
  width: fit-content;
}

.dropdownContent {
  position: absolute;
  display: flex;
  flex-direction: column;
  top: 35px;
  right: 0;
  border-radius: 0px 0px 8px 8px;
  padding: 10px;
  z-index: 1000;
  gap: 5px;
}

.dialogOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(6px);
  z-index: 1000;
}

.dialogBox {
  background-color: var(--color-background-modal);
  padding: 15px;
  border-radius: 6px;
  width: 500px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  display: flex;
  flex-direction: column;
  gap: 12px;
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.referencedPageInputStyles {
  border: none;
}
