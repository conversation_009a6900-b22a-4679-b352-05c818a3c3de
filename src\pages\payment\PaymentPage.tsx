import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Container, Typography, Button, Alert } from '@mui/material';
import { ArrowBack } from '@mui/icons-material';
import StripeProvider from '../../components/payment/StripeProvider';
import PaymentForm from '../../components/payment/PaymentForm';
import TestCardHelper from '../../components/payment/TestCardHelper';
import { usePayment } from '../../hooks/payment/usePayment';
import LoadingSpinner from '../../components/common/loading/LoadingSpinner';
import { isAuthenticated } from '../../utils/auth/userHelpers';
import styles from './PaymentPage.module.css';

const PaymentPage: React.FC = () => {
  const navigate = useNavigate();
  const {
    currentPlan,
    isLoading,
    hasError,
    errorMessage,
    clearLoading,
    clearPaymentError,
  } = usePayment();

  useEffect(() => {
    console.log('PaymentPage useEffect - State:', {
      currentPlan: currentPlan?.name,
      isLoading,
      hasError,
      isAuthenticated: isAuthenticated(),
    });

    // Check authentication first
    if (!isAuthenticated()) {
      console.log('PaymentPage: User not authenticated, redirecting to login');
      navigate('/login');
      return;
    }

    // Clear any previous payment loading state when component mounts
    // This fixes the issue where startPaymentFlow sets isLoading=true
    // but it never gets cleared when navigating to the payment page
    if (currentPlan && isLoading) {
      console.log(
        'PaymentPage: Clearing loading state for plan:',
        currentPlan.name
      );
      clearPaymentError();
      clearLoading();
    }

    // Redirect to pricing page if no plan is selected
    if (!currentPlan && !isLoading) {
      console.log('PaymentPage: No plan selected, redirecting to pricing');
      navigate('/price');
    }
  }, [currentPlan, isLoading, navigate, clearLoading, clearPaymentError]);

  const handleGoBack = () => {
    navigate('/price');
  };

  if (isLoading) {
    return (
      <Container className={styles.container}>
        <LoadingSpinner fullScreen size="large" message="Loading payment..." />
      </Container>
    );
  }

  if (hasError) {
    return (
      <Container className={styles.container}>
        <Box className={styles.errorContainer}>
          <Alert severity="error" className={styles.errorAlert}>
            <Typography variant="h6">Unable to load payment</Typography>
            <Typography variant="body2">{errorMessage}</Typography>
          </Alert>
          <Button
            variant="contained"
            onClick={handleGoBack}
            startIcon={<ArrowBack />}
            className={styles.backButton}
          >
            Back to Pricing
          </Button>
        </Box>
      </Container>
    );
  }

  if (!currentPlan) {
    return (
      <Container className={styles.container}>
        <Box className={styles.errorContainer}>
          <Alert severity="warning" className={styles.errorAlert}>
            <Typography variant="h6">No plan selected</Typography>
            <Typography variant="body2">
              Please select a plan before proceeding with payment.
            </Typography>
          </Alert>
          <Button
            variant="contained"
            onClick={handleGoBack}
            startIcon={<ArrowBack />}
            className={styles.backButton}
          >
            Select a Plan
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <div className={styles.pageContainer}>
      <Container className={styles.container}>
        {/* Header */}
        <Box className={styles.header}>
          <Button
            variant="text"
            onClick={handleGoBack}
            startIcon={<ArrowBack />}
            className={styles.backLink}
          >
            Back to Plans
          </Button>

          <Typography variant="h3" className={styles.pageTitle}>
            Secure Payment
          </Typography>

          <Typography variant="body1" className={styles.pageSubtitle}>
            Complete your subscription to {currentPlan.name} plan
          </Typography>
        </Box>

        {/* Test Card Helper (only shows in test mode) */}
        <TestCardHelper />

        {/* Payment Form */}
        <Box className={styles.formContainer}>
          <StripeProvider>
            <PaymentForm />
          </StripeProvider>
        </Box>

        {/* Security Notice */}
        <Box className={styles.securityNotice}>
          <Typography variant="body2" className={styles.securityText}>
            🔒 Your payment is secured with 256-bit SSL encryption
          </Typography>
          <Typography variant="body2" className={styles.securityText}>
            💳 We accept all major credit cards
          </Typography>
          <Typography variant="body2" className={styles.securityText}>
            🛡️ Powered by Stripe - trusted by millions worldwide
          </Typography>
        </Box>

        {/* Support Information */}
        <Box className={styles.supportInfo}>
          <Typography variant="body2" className={styles.supportText}>
            Need help? Contact our support team at{' '}
            <a href="mailto:<EMAIL>" className={styles.supportLink}>
              <EMAIL>
            </a>
          </Typography>
        </Box>
      </Container>
    </div>
  );
};

export default PaymentPage;
