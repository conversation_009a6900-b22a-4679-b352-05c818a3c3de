import Card from '../../components/common/card/Card';
import styles from './AwaitingActionsPage.module.css';
import profile from '../../assets/images/profile.png';
import { useGetWorkflowByCreatorAndCurrentUserPaginatedQuery } from '../../services/workflowServices';
import useLocalStorage from '../../hooks/useLocalStorage';
import { BASE_URL } from '../../services/config';
import { useNavigate } from 'react-router-dom';
import CircularLoader from '../../assets/loader/CircularLoader';
import { useState, useEffect, useMemo } from 'react';
import ListCard from '../../components/common/card/ListCard';
import WorkflowFilter from '../../components/workflow/WorkflowFilter';
import { Workflow } from '../../types/workflow/index';
import { DataGridTemplate } from '../../components/templates/DataGridTemplate';

type CardStatus =
  | 'Completed'
  | 'In Progress'
  | 'Rejected'
  | 'Awaiting'
  | 'Draft'
  | 'Published';
type CardRole = 'creator' | 'approver';

const AwaitingActionsPage = () => {
  const [currentUser] = useLocalStorage('user', null);
  const navigate = useNavigate();
  const [activeButton, setActiveButton] = useState<string>('all-projects');
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [allWorkflows, setAllWorkflows] = useState<Workflow[]>([]);

  const {
    data,
    isLoading,
    error: isError,
    isFetching,
  } = useGetWorkflowByCreatorAndCurrentUserPaginatedQuery(
    {
      creatorId: currentUser.id,
      page,
      size: 20,
    },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const hasNextPage = data?.data
    ? data.data.totalPages > data.data.currentPage
    : false;

  // Update workflows when new data arrives
  useEffect(() => {
    if (data?.data?.content) {
      if (page === 1) {
        setAllWorkflows(data.data.content);
      } else {
        setAllWorkflows((prev) => {
          const newWorkflows = data.data.content.filter(
            (newItem: Workflow) =>
              !prev.some((existingItem) => existingItem.id === newItem.id)
          );
          return [...prev, ...newWorkflows];
        });
      }
    }
  }, [data, page]);

  // Filter workflows based on status and search term
  const filteredWorkflows = useMemo(() => {
    let filtered = [...allWorkflows];

    // Apply status filter
    if (activeButton !== 'all-projects') {
      const statusMap = {
        completed: 'approved',
        'in-progress': 'in-progress',
        rejected: 'rejected',
      } as const;
      filtered = filtered.filter(
        (w) =>
          w.approvalStatus === statusMap[activeButton as keyof typeof statusMap]
      );
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (w) =>
          w.title?.toLowerCase().includes(term) ||
          w.participants.some((p) => p.name.toLowerCase().includes(term))
      );
    }

    return filtered;
  }, [allWorkflows, activeButton, searchTerm]);

  // Get counts for each status
  const getWorkflowCount = (filterId: string): number => {
    const statusMap = {
      completed: 'approved',
      'in-progress': 'in-progress',
      rejected: 'rejected',
      'all-projects': 'all',
    } as const;

    const status = statusMap[filterId as keyof typeof statusMap];

    if (status === 'all') {
      return allWorkflows.length;
    }

    return allWorkflows.filter((w) => w.approvalStatus === status).length;
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1);
  };

  const handleFilterChange = (newFilter: string) => {
    setActiveButton(newFilter);
    setPage(1);
  };

  const handleLoadMore = () => {
    if (!isFetching && hasNextPage) {
      setPage((prev) => prev + 1);
    }
  };

  const handleCardClick = (workflow: Workflow) => {
    navigate('/awaited-actions-view', { state: { workflowData: workflow } });
  };

  const getEmptyMessage = () => {
    if (!allWorkflows.length) return "You don't have any workflows yet.";
    if (searchTerm && !filteredWorkflows.length)
      return 'No workflows found matching your search.';

    switch (activeButton) {
      case 'completed':
        return 'No completed workflows found.';
      case 'in-progress':
        return 'No workflows in progress.';
      case 'rejected':
        return 'No rejected workflows found.';
      default:
        return 'No workflows found.';
    }
  };

  if (isLoading && page === 1) {
    return (
      <div className={styles.loaderContainer}>
        <CircularLoader text="Loading workflows..." />
      </div>
    );
  }

  if (isError) {
    return (
      <div className={styles.emptyState}>
        <h2>Error Loading Workflows</h2>
        <p>There was a problem loading your workflows. Please try again.</p>
      </div>
    );
  }

  return (
    <DataGridTemplate
      isLoading={isLoading && page === 1}
      isError={!!isError}
      isEmpty={!filteredWorkflows.length}
      viewMode={viewMode}
      onViewModeChange={setViewMode}
      onSearch={handleSearch}
      headerExtras={
        <WorkflowFilter
          activeButton={activeButton}
          workflowCount={getWorkflowCount}
          onFilterChange={handleFilterChange}
        />
      }
      loadingText="Loading workflows..."
      errorTitle="Error Loading Workflows"
      errorMessage="There was a problem loading your workflows. Please try again."
      emptyTitle={`No ${activeButton.replace('-', ' ')} Workflows`}
      emptyMessage={getEmptyMessage()}
      hasMore={hasNextPage}
      onLoadMore={handleLoadMore}
      isLoadingMore={isFetching && page > 1}
    >
      {filteredWorkflows.map((workflow) => {
        const mappedUsers = workflow.participants.map((participant) => ({
          name: participant.name,
          avatar: participant.profileImg
            ? `${BASE_URL}/api/v1/user/profile/download/${participant.profileImg}`
            : profile,
        }));

        const status: CardStatus =
          workflow.approvalStatus === 'approved'
            ? 'Completed'
            : workflow.approvalStatus === 'in-progress'
              ? 'In Progress'
              : workflow.approvalStatus === 'rejected'
                ? 'Rejected'
                : 'Awaiting';

        const role: CardRole =
          workflow.creatorId === currentUser.id ? 'creator' : 'approver';

        const commonProps = {
          showStatusAsDot: workflow.approvalStatus,
          currentUser: workflow.creatorId === currentUser.id,
          canEdit: false,
          editorImage: mappedUsers[0]?.avatar || profile,
          editorName: workflow.participants[0]?.name || 'Unknown',
          initials: workflow.title?.charAt(0) || 'W',
          status,
          role,
          timeAgo: new Date(workflow.createdOn).toLocaleDateString(),
          title: workflow.title || 'Untitled Workflow',
          users:
            mappedUsers.length > 0
              ? mappedUsers
              : [{ name: 'No participants', avatar: profile }],
          handleCardClick: () => handleCardClick(workflow),
          type: 'workflow' as const,
        } as const;

        return viewMode === 'grid' ? (
          <Card key={workflow.id} {...commonProps} />
        ) : (
          <ListCard key={workflow.id} {...commonProps} />
        );
      })}
    </DataGridTemplate>
  );
};

export default AwaitingActionsPage;
