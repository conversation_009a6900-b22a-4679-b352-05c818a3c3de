import SpeechRecognition from 'react-speech-recognition';
import IconButton from '../common/button/IconButton';
import { IoMic, IoMicOff } from 'react-icons/io5';

const VoiceInput = ({
  isListening,
  setListening,
}: {
  isListening: boolean;
  setListening: (listening: boolean) => void;
}) => {
  const handleListening = () => {
    if (isListening) {
      SpeechRecognition.stopListening(); // <-- Call this
      setListening(false);
    } else {
      SpeechRecognition.startListening({ continuous: true }); // <-- and this
      setListening(true);
    }
  };

  return (
    <div>
      <IconButton
        icon={isListening ? <IoMicOff size={18} /> : <IoMic size={18} />}
        onClick={handleListening}
      />
    </div>
  );
};

export default VoiceInput;
