# Enhanced Markdown Renderer

A comprehensive, configurable React component for rendering Markdown with advanced LaTeX support, syntax highlighting, and enhanced features.

## Features

### ✨ Core Features
- **Markdown Rendering**: Full GitHub Flavored Markdown (GFM) support
- **LaTeX Support**: Inline and display math with KaTeX
- **Syntax Highlighting**: Auto-detection and highlighting for 30+ languages
- **Table Enhancement**: Beautiful, responsive tables with hover effects
- **Task Lists**: Checkbox support with custom styling
- **Link Enhancement**: External link indicators and new tab opening

### 🔧 Advanced Processing
- **LaTeX Preprocessing**: Fixes malformed patterns like `$$$`, `\\alpha$$`
- **Greek Letter Conversion**: Auto-converts "Alpha", "Beta" → `$\alpha$`, `$\beta$`
- **Currency Protection**: Prevents `$100`, `$1,000.50` from being rendered as math
- **Relative Link Rewriting**: Converts `/docs/help` → `https://example.com/docs/help`
- **Graceful Error Handling**: Fallback rendering when LaTeX/Markdown fails

### 🎨 UI/UX Enhancements
- **Responsive Design**: Mobile-friendly layouts
- **Dark/Light Theme Support**: Automatic theme detection
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Performance**: Optimized rendering with minimal re-renders

## Quick Start

### Basic Usage

```tsx
import MarkdownRenderer from '../components/common/chips/Markdown';

function MyComponent() {
  const content = `
# Hello World

This is **bold** and this is *italic*.

Math: $E = mc^2$

Greek letters: Alpha and Beta become $\\alpha$ and $\\beta$

Price: $100 (not rendered as math)
  `;

  return <MarkdownRenderer content={content} />;
}
```

### Configurable Usage

```tsx
import ConfigurableMarkdown, { MARKDOWN_PRESETS } from '../components/common/chips/ConfigurableMarkdown';

function ScientificDocument() {
  return (
    <ConfigurableMarkdown
      content={scientificContent}
      config={MARKDOWN_PRESETS.scientific}
    />
  );
}

function BlogPost() {
  return (
    <ConfigurableMarkdown
      content={blogContent}
      config={{
        enableLatexPreprocessing: false,
        enableSyntaxHighlighting: true,
        showLineNumbers: false,
        baseUrl: 'https://myblog.com'
      }}
    />
  );
}
```

## Configuration Options

### MarkdownConfig Interface

```typescript
interface MarkdownConfig {
  // LaTeX and Math Processing
  enableLatexPreprocessing: boolean;    // Fix malformed LaTeX patterns
  enableGreekConversion: boolean;       // Convert "Alpha" → $\alpha$
  enableCurrencyProtection: boolean;    // Protect $100 from math rendering
  singleDollarTextMath: boolean;        // Enable $...$ for inline math
  
  // Content Processing
  baseUrl: string;                      // Base URL for relative links
  enableInlineHtml: boolean;            // Allow HTML in markdown
  
  // Code Highlighting
  enableSyntaxHighlighting: boolean;    // Enable syntax highlighting
  showLineNumbers: boolean;             // Show line numbers in code blocks
  lineNumberThreshold: number;          // Min lines to show numbers
  
  // Table Enhancement
  enableTableStyling: boolean;          // Enhanced table styling
  enableTableHover: boolean;            // Hover effects on table rows
  
  // Link Processing
  enableExternalLinkIndicators: boolean; // Show ↗ for external links
  openExternalLinksInNewTab: boolean;   // Open external links in new tab
  
  // UI/UX
  theme: 'light' | 'dark' | 'auto';     // Theme selection
  enableResponsiveDesign: boolean;      // Mobile-friendly layouts
  
  // Error Handling
  enableFallbackRendering: boolean;     // Show fallback on errors
  showErrorDetails: boolean;            // Show detailed error messages
}
```

### Preset Configurations

```typescript
import { MARKDOWN_PRESETS } from '../utils/markdownConfig';

// Available presets:
MARKDOWN_PRESETS.minimal      // Basic markdown only
MARKDOWN_PRESETS.scientific   // Full LaTeX + Greek letters
MARKDOWN_PRESETS.documentation // Code-focused with syntax highlighting
MARKDOWN_PRESETS.blog         // Content-focused with enhanced links
MARKDOWN_PRESETS.full         // All features enabled
```

## Examples

### LaTeX and Greek Letters

```markdown
# Mathematical Content

Greek letters: Alpha, Beta, Gamma → $\alpha$, $\beta$, $\gamma$

Inline math: $E = mc^2$ and $\sum_{i=1}^{n} x_i$

Display math:
$$\int_0^1 f(x)dx = \lim_{n \to \infty} \sum_{i=1}^{n} f(x_i) \Delta x$$

Currency: The formula costs $50 to compute (not rendered as math)
```

### Enhanced Tables

```markdown
| Feature | Status | Priority |
|---------|--------|----------|
| LaTeX Support | ✅ Complete | High |
| Table Styling | ✅ Complete | Medium |
| Syntax Highlighting | ✅ Complete | High |
```

### Task Lists

```markdown
- [x] Implement LaTeX preprocessing
- [x] Add Greek letter conversion
- [ ] Add theme switching
- [x] Enhance table styling
```

### Code Blocks with Auto-Detection

```markdown
```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)
```

```sql
SELECT users.name, COUNT(orders.id) as order_count
FROM users LEFT JOIN orders ON users.id = orders.user_id
GROUP BY users.id;
```
```

## API Reference

### MarkdownRenderer (Basic)

```typescript
interface MarkdownRendererProps {
  content: string;
  baseUrl?: string;
  theme?: 'light' | 'dark';
  enableInlineHtml?: boolean;
  enableGreekConversion?: boolean;
  enableCurrencyProtection?: boolean;
  enableLatexPreprocessing?: boolean;
  className?: string;
}
```

### ConfigurableMarkdown (Advanced)

```typescript
interface ConfigurableMarkdownProps {
  content: string;
  config?: Partial<MarkdownConfig>;
  className?: string;
  onError?: (error: Error) => void;
}
```

## Utility Functions

### Preprocessing Functions

```typescript
import { 
  preprocessLatex,
  convertGreekLetters,
  protectCurrency,
  rewriteRelativeLinks,
  detectLanguage,
  preprocessMarkdownContent
} from '../utils/markdownUtils';

// Individual functions
const fixed = preprocessLatex('\\alpha$$'); // → '$\alpha$'
const converted = convertGreekLetters('Alpha and Beta'); // → '$\alpha$ and $\beta$'
const protected = protectCurrency('Price: $100'); // → 'Price: \$100'

// Comprehensive pipeline
const processed = preprocessMarkdownContent(content, {
  baseUrl: 'https://example.com',
  enableGreekConversion: true,
  enableCurrencyProtection: true,
  enableLatexPreprocessing: true,
});
```

### Configuration Utilities

```typescript
import { 
  mergeMarkdownConfig,
  getMarkdownPreset,
  validateMarkdownConfig,
  createConfigFromEnvironment
} from '../utils/markdownConfig';

// Merge configurations
const config = mergeMarkdownConfig(userConfig, DEFAULT_MARKDOWN_CONFIG);

// Get preset
const scientificConfig = getMarkdownPreset('scientific');

// Validate configuration
const errors = validateMarkdownConfig(userConfig);
```

## Styling and Theming

The component uses CSS custom properties for theming:

```css
:root {
  --color-text-primary: #333;
  --color-text-secondary: #666;
  --color-background-primary: #fff;
  --color-background-secondary: #f5f5f5;
  --color-background-tertiary: #f0f0f0;
  --color-background-hover: #f8f9fa;
  --color-border: #ddd;
  --color-primary: #007bff;
  --color-primary-dark: #0056b3;
  --color-shadow: rgba(0, 0, 0, 0.1);
}
```

## Performance Considerations

- **Lazy Loading**: Large code blocks are rendered on-demand
- **Memoization**: Processed content is cached to prevent re-processing
- **Minimal Re-renders**: Component only re-renders when content or config changes
- **Optimized Regex**: Preprocessing uses optimized regular expressions

## Browser Support

- **Modern Browsers**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Mobile**: iOS Safari 13+, Chrome Mobile 80+
- **Dependencies**: React 16.8+, KaTeX 0.16+

## Troubleshooting

### Common Issues

1. **LaTeX not rendering**: Check KaTeX CSS is loaded
2. **Syntax highlighting not working**: Verify language detection
3. **Tables not styled**: Ensure CSS custom properties are defined
4. **Performance issues**: Use `React.memo` for large documents

### Debug Mode

```typescript
<ConfigurableMarkdown
  content={content}
  config={{ showErrorDetails: true }}
  onError={(error) => console.error('Markdown error:', error)}
/>
```
