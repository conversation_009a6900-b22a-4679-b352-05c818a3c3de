# Production Deployment Guide - Stripe Payment Integration

## Overview
This guide provides step-by-step instructions for testing the Stripe payment integration with test keys and then deploying to production with live keys.

## Current Status: ✅ Ready for Testing

### **Frontend Configuration**
- ✅ **Test Mode Active**: `DEV = true` in `src/services/config.ts`
- ✅ **Test Keys**: Will use `VITE_STRIPE_PUBLISHABLE_KEY_TEST`
- ✅ **Test Helper**: TestCardHelper component added to payment page
- ✅ **Backend URL**: Points to `http://**************:8182`

## Phase 1: Testing with Test Keys

### **Step 1: Verify Environment Variables**
Ensure your `.env` file contains:
```env
# Backend URLs
VITE_DEV_BASE_URL=http://**************:8182
VITE_PRO_BASE_URL=http://**************:8182

# Stripe Test Keys
VITE_STRIPE_PUBLISHABLE_KEY_TEST=pk_test_your_test_key_here
VITE_STRIPE_PUBLISHABLE_KEY_LIVE=pk_live_your_live_key_here
```

### **Step 2: Backend Test Configuration**
**IMPORTANT**: Coordinate with your backend team to ensure:

```javascript
// Backend should use test secret key during testing
const stripe = require('stripe')('sk_test_your_test_secret_key_here');
```

**Backend Environment Variables**:
```env
# Backend .env for testing
STRIPE_SECRET_KEY=sk_test_your_test_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_test_your_webhook_secret_here
```

### **Step 3: Test Payment Flow**

#### **3.1 Basic Flow Test**
1. **Start Application**: `npm run dev`
2. **Navigate**: Go to pricing page
3. **Select Plan**: Choose any paid plan
4. **Payment Page**: Should show TestCardHelper component (blue info box)
5. **Verify Test Mode**: Check that test card numbers are displayed

#### **3.2 Successful Payment Test**
1. **Use Test Card**: `****************`
2. **Fill Form**:
   - Card Number: ****************
   - Expiry: 12/25
   - CVC: 123
   - ZIP: 12345
   - Name: Test User
   - Email: <EMAIL>
3. **Submit Payment**
4. **Expected Result**: Payment should succeed
5. **Verify**: Check Stripe test dashboard for payment intent

#### **3.3 Declined Payment Test**
1. **Use Decline Card**: `****************`
2. **Fill Form** with decline card number
3. **Submit Payment**
4. **Expected Result**: Payment should be declined with error message
5. **Verify**: Error handling works correctly

#### **3.4 Backend Integration Test**
1. **Monitor Network Tab**: Check API calls to `/api/v1/payments/initiate`
2. **Verify Response**: Should receive `clientSecret` starting with `pi_test_`
3. **Check Webhooks**: Monitor Stripe dashboard for webhook delivery
4. **User Credits**: Verify if credits are updated (depends on backend logic)

### **Step 4: Troubleshooting Test Issues**

#### **Common Test Issues**:
1. **No Test Cards Showing**: 
   - Check if `DEV = true` in config.ts
   - Verify `VITE_STRIPE_PUBLISHABLE_KEY_TEST` is set

2. **Payment Intent Creation Fails**:
   - Ensure backend uses test secret key
   - Check backend logs for errors
   - Verify API endpoint is accessible

3. **Webhook Failures**:
   - Configure test webhooks in Stripe dashboard
   - Use ngrok for local webhook testing if needed
   - Check webhook secret matches backend

## Phase 2: Production Deployment

### **Step 1: Pre-Production Checklist**
- [ ] All test scenarios pass successfully
- [ ] Backend integration works with test keys
- [ ] Webhooks deliver correctly in test mode
- [ ] Error handling works as expected
- [ ] User experience is smooth and professional

### **Step 2: Frontend Production Configuration**

#### **2.1 Update Configuration**
```typescript
// In src/services/config.ts
const DEV = false; // Change from true to false
```

#### **2.2 Verify Environment Variables**
```env
# Production environment variables
VITE_PRO_BASE_URL=http://**************:8182
VITE_STRIPE_PUBLISHABLE_KEY_LIVE=pk_live_your_live_key_here
```

#### **2.3 Remove Test Helper (Optional)**
The TestCardHelper component automatically hides in production (when using live keys), but you can remove the import if desired:
```typescript
// Remove this line from PaymentPage.tsx if desired
import TestCardHelper from '../../components/payment/TestCardHelper';
```

### **Step 3: Backend Production Configuration**

#### **3.1 Coordinate Backend Switch**
**CRITICAL**: Ensure backend team switches to live keys:
```javascript
// Backend production configuration
const stripe = require('stripe')('sk_live_your_live_secret_key_here');
```

#### **3.2 Backend Production Environment**
```env
# Backend production .env
STRIPE_SECRET_KEY=sk_live_your_live_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_live_your_webhook_secret_here
```

#### **3.3 Production Webhook Configuration**
- Configure live webhooks in Stripe dashboard
- Update webhook URL to production domain
- Test webhook delivery with live events

### **Step 4: Production Deployment Process**

#### **4.1 Deployment Steps**
1. **Frontend**: 
   - Set `DEV = false`
   - Build production bundle: `npm run build`
   - Deploy to production server

2. **Backend**:
   - Switch to live Stripe secret key
   - Update webhook configuration
   - Deploy backend changes

3. **Verification**:
   - Test with small real transaction
   - Verify webhook delivery
   - Check user credit updates

#### **4.2 Production Testing**
1. **Small Test Transaction**:
   - Use real card with small amount ($1)
   - Verify complete flow works
   - Check Stripe live dashboard

2. **Monitor Systems**:
   - Watch for errors in production logs
   - Monitor webhook delivery
   - Verify user experience

## Phase 3: Key Management Best Practices

### **Environment Coordination Matrix**

| Environment | Frontend Key | Backend Key | Webhooks | Status |
|-------------|--------------|-------------|----------|---------|
| **Testing** | `pk_test_...` | `sk_test_...` | Test Mode | ✅ Current |
| **Production** | `pk_live_...` | `sk_live_...` | Live Mode | 🔄 Next |

### **Deployment Coordination**
1. **Never Mix Keys**: Always ensure frontend and backend use matching key types
2. **Synchronized Deployment**: Deploy frontend and backend changes together
3. **Rollback Plan**: Keep test configuration ready for quick rollback if needed

### **Security Considerations**
- **Environment Variables**: Never commit keys to version control
- **Key Rotation**: Plan for periodic key rotation
- **Access Control**: Limit access to live keys
- **Monitoring**: Set up alerts for payment failures

## Phase 4: Monitoring & Maintenance

### **Production Monitoring**
1. **Stripe Dashboard**: Monitor live transactions and webhooks
2. **Application Logs**: Watch for payment-related errors
3. **User Feedback**: Monitor support tickets for payment issues
4. **Performance**: Track payment completion rates

### **Maintenance Tasks**
- Regular webhook delivery verification
- Payment failure rate monitoring
- User credit reconciliation
- Security audit of payment flow

## Quick Reference Commands

### **Switch to Test Mode**
```typescript
// src/services/config.ts
const DEV = true;
```

### **Switch to Production Mode**
```typescript
// src/services/config.ts
const DEV = false;
```

### **Verify Current Mode**
Check browser console for Stripe key format:
- Test: `pk_test_...`
- Live: `pk_live_...`

## Summary

The payment integration is ready for testing with Stripe test keys. Follow this guide to:

1. ✅ **Test Safely**: Use test keys to verify complete payment flow
2. 🔄 **Deploy Confidently**: Switch to live keys when testing is complete
3. 📊 **Monitor Effectively**: Track payment performance in production

The TestCardHelper component will guide you through testing different payment scenarios safely before going live.
