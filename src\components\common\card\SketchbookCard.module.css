.card {
  background: var(--color-background-secondary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition:
    all 0.2s ease,
    background-color 0.3s ease,
    border-color 0.3s ease,
    transform 0.2s ease;
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  color: var(--color-text-primary);
  touch-action: manipulation;
}

.card.grid {
  max-width: 280px;
}

.card.list {
  flex-direction: row;
  max-width: none;
  height: 120px;
}

.thumbnailSection {
  display: flex;
  aspect-ratio: 16/9;
  background: var(--color-background-tertiary);
  position: relative;
  flex-shrink: 0;
  justify-content: center;
  transition: background-color 0.3s ease;
}

/* .card.list .thumbnailSection {
  width: 213px;
  height: 120px;
} */

.thumbnail {
  width: 100%;
  aspect-ratio: 16/9;
  /* object-fit: contain; */
  height: 100%;
}

.placeholderThumbnail {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.sketchbookIcon {
  width: 48px;
  height: 48px;
  opacity: 0.6;
}

.content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  justify-content: space-between;
}

.title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-primary);
  height: 65px;
  transition: color 0.3s ease;
}

.metadata {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 13px;
  color: var(--color-text-secondary);
  transition: color 0.3s ease;
}

.privacy {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--color-text-secondary);
  transition: color 0.3s ease;
}

.privacy svg {
  width: 16px;
  height: 16px;
}

.creator,
.timeAgo {
  color: var(--color-text-secondary);
  transition: color 0.3s ease;
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .card {
    min-height: 240px;
    border-radius: 16px;
  }

  .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .card:active {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .card.grid {
    max-width: 320px;
  }

  .card.list {
    height: 140px;
    min-height: 140px;
  }

  .thumbnailSection {
    min-height: 140px;
  }

  .card.list .thumbnailSection {
    width: 240px;
    height: 140px;
    min-height: 140px;
  }

  .content {
    padding: 20px;
    gap: 12px;
  }

  .title {
    font-size: 18px;
    font-weight: 600;
    height: auto;
    min-height: 48px;
    line-height: 1.4;
  }

  .metadata {
    font-size: 14px;
    gap: 6px;
  }

  .sketchbookIcon {
    width: 56px;
    height: 56px;
  }
}

/* Tablet portrait specific */
@media (min-width: 768px) and (max-width: 834px) and (orientation: portrait) {
  .card.grid {
    max-width: 100%;
  }

  .card.list {
    height: 120px;
    min-height: 120px;
  }

  .card.list .thumbnailSection {
    width: 200px;
    height: 120px;
    min-height: 120px;
  }

  .content {
    padding: 16px;
  }

  .title {
    font-size: 16px;
    min-height: 40px;
  }
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
  .card {
    min-height: 200px;
    touch-action: manipulation;
  }

  .card:active {
    transform: scale(0.98);
  }

  .content {
    padding: 14px;
  }

  .title {
    font-size: 15px;
    min-height: 36px;
  }

  .metadata {
    font-size: 12px;
  }
}
